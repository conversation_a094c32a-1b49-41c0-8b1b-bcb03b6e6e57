---
description: 多语言开发规范
globs: 
alwaysApply: false
---

# 多语言开发规范 (MDC)

## 多语 Key 命名规范

### 基本结构
多语 key 应遵循以下结构：
```
i18n.项目名称.模块名.描述
```

### 项目名称获取
项目名称应从工作目录路径中获取：
1. 获取工作目录的完整路径
2. 取路径中的最后一个目录名作为项目名称
3. 将项目名称中的特殊字符转换为下划线，例如：
   - 连字符(`-`) -> 下划线(`_`)
   - 点号(`.`) -> 下划线(`_`)
   - 空格( ) -> 下划线(`_`)

例如：
- 工作目录：`E:\work\FS\fs-sail\dhtbiz\fe-sail-v2`
- 原始项目名称：`fe-sail-v2`
- 转换后的项目名称：`fe_sail_v2`

在多语言 key 中使用时，使用转换后的项目名称，并将原来的中文, 用注释的形式保留在对应位置, 例如：
```javascript
$t('i18n.fe_sail_v2.module.key'/*原来的中文*/)
```

### 命名规则

1. 项目名称
   - 从工作目录路径中获取最后一个目录名
   - 将特殊字符（如连字符、点号、空格等）转换为下划线
   - 不要硬编码为其他值

2. 模块名
   - 保持原有模块名称不变
   - 例如：common、dht、shoppingcart、orderform 等
   - common 用于存放通用的多语文本

3. 描述部分
   - 不超过两个下划线
   - 使用小写字母
   - 使用下划线连接
   - 保持语义清晰
   - 优先使用常见简写

### 简化原则

1. 去掉不必要的修饰词
   - first
   - must
   - from
   - at_least
   - has

2. 使用常见简写
   - calculate -> calc
   - minimum -> min
   - maximum -> max
   - readonly -> readonly
   - product -> prod
   - customer -> cust

    

### $t 使用规范

1. 在任何场景下都直接使用 `$t`，不要使用 `this.$t`
   - 示例：
     ```js
     // ✅ 正确
     description = [description, $t('i18n.fe_sail_v2.dht.gift'/*原来的中文*/)].join('，');
     data() {
       return {
         name: $t('i18n.fe_sail_v2.dht.title'/*原来的中文*/)
       }
     }

     // ❌ 错误
     description = [description, this.$t('i18n.fe_sail_v2.dht.gift')].join('，');
     data() {
       return {
         name: this.$t('i18n.fe_sail_v2.dht.title')
       }
     }
     ```

2. 在模板层（template）中
   - 继续使用 `$t`
   - 示例：
     ```html
     <!-- 在模板中使用 -->
     <div>{{ $t('i18n.fe_sail_v2.common.title'/*原来的中文*/) }}</div>
     ```

3. $t中包含变量的处理
   - 中文变量部分使用{{param}}包裹变量param 
   - 示例:
     ```js
     // 原始中文: `匹配失败的产品数量为: ${me.failCount}, 请先更换正确产品或删除` 
     // 处理为: 
     $t('i18n.dhtbiz.dht_ai_order.match_fail_tip'/*匹配失败的产品数量为: {{count}}, 请先更换正确产品或删除*/, { count: me.failCount })
     ```
   - 在模板层（template）中, 使用 {}包裹即可, 否则编译报错, 单是生成的csv中始终使用{{}}包裹, 如下:
   ```html
   <span class="fail-count result-count">{{ $t('i18n.dhtbiz.dht_ai_order.fail_count'/*{count} 条失败*/, { count: failCount }) }}</span>
   ```
   ```csv
   key,zh_CN,tags
   i18n.dhtbiz.dht_ai_order.fail_count,{count} 条失败,web 
   ```
    

### 示例

原始 key | 优化后 key | 说明
---|---|-----
i18n.shoppingcart.quantity_must_greater_than_zero | i18n.fe_sail_v2.shoppingcart.quantity_min | 添加项目名，去掉修饰词，使用简写
i18n.shoppingcart.product_has_common_unit | i18n.fe_sail_v2.shoppingcart.unit_readonly | 添加项目名，简化描述，突出核心含义
i18n.shoppingcart.calculation_error_retry | i18n.fe_sail_v2.shoppingcart.calc_error | 添加项目名，使用常见简写
i18n.shoppingcart.select_at_least_one_product | i18n.fe_sail_v2.shoppingcart.select_product | 添加项目名，去掉不必要的修饰词
i18n.industryorder.select_price_list_first | i18n.fe_sail_v2.industryorder.select_price | 添加项目名，保留核心含义
i18n.industryorder.add_from_gift_pool | i18n.fe_sail_v2.industryorder.add_gift | 添加项目名，简化描述

### 注意事项

1. 保持一致性：同一个概念在不同模块中应使用相同的描述方式
2. 避免歧义：简化时不应影响语义的清晰度
3. 可维护性：命名应具有自解释性，便于其他开发者理解
4. 长度控制：在保证语义清晰的前提下，尽可能简短
5. 整个文件处理完整:不遗漏,如果文件太长,可以分多次处理,比如每次处理300行,直到文件末尾 

## 多语言文件生成规范

### CSV 文件格式

1. 文件结构
   - 文件格式：标准 CSV 文件，使用逗号分隔
   - 文件编码：UTF-8
   - 必需列：key、zh_CN、tags
   - 所有字段使用双引号包围

2. 列说明
   - key：多语 key，遵循上述命名规范
   - zh_CN：中文翻译文本
   - tags：标签，默认为小写的"web"

3. CSV 文件示例
```csv
key,zh_CN,tags
i18n.appcustomization.dht.quantity_min,数量必须大于0,web
i18n.appcustomization.dht.unit_readonly,该产品有常用单位，单位相关字段不可编辑,web
```

### 文件处理流程

1. 生成 CSV 文件
   - 创建标准 CSV 文件
   - 确保所有字段都用双引号包围
   - 确保使用正确的列名和顺序

2. 转换为 Excel
   - 使用 Excel 打开 CSV 文件
   - 文件 -> 另存为 -> 选择"Excel 工作簿（*.xlsx）"格式
   - 选择保存位置并保存

### 注意事项

1. 字段引号：所有字段必须使用双引号包围，避免特殊字符造成解析错误
2. 编码格式：确保使用 UTF-8 编码，避免中文乱码
3. 标签统一：tags 字段统一使用小写的"web"
4. 格式转换：CSV 转 Excel 时注意保持编码格式
