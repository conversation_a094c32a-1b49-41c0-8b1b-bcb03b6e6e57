cache:
    untracked: true
    key: $CI_PROJECT_PATH
    paths:
        - node_modules/
        - release/

stages:
    - install
    - lint
    # - dev
    # - build

# 安装node_modules依赖
install:
    stage: install
    only:
        - bugfix
        - release-html
    script:
        - npm install

# 执行lint代码静态扫描
lint:
    stage: lint
    only:
        - bugfix
        - release-html
    script: npm run lint
#   # 执行gulp构建脚本
#   # 开发环境代码编译
# dev:
#     stage: dev
#     only:
#       - develop
#     script: npm run dev
#     artifacts:
#       name: "${CI_PROJECT_NAME}_${CI_BUILD_REF_NAME}"
#       paths:
#         - dev/

# # QA测试环境代码编译
# build:
#     stage: build
#     only:
#       - tags
#     script: npm run build
#     artifacts:
#       name: "${CI_PROJECT_NAME}_${CI_BUILD_REF_NAME}"
#       paths:
#         - build/
