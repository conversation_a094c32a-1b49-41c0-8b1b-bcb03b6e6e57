<template>
  <div class="dht-product-commodity-labels-warpper">    
    <template v-if="tag === '1'">
      <div
          v-for="option in product.commodityLabels"
          :key="option.value"
          class="dht-product-commodity-labels"
          :style="{backgroundColor: option.font_color}"
        >
        {{option.label}}
      </div>
    </template>
    <div class="dht-product-detail-name"> {{ product.display_name || product.name }} </div>
  </div>
</template>

<script>
import widgetsDetailMixins from './widgets-detail-mixins';

export default {
  name: 'ProductTitle',
  mixins: [widgetsDetailMixins],
  props: {
    tag: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="less" scoped>
.dht-product-detail {
  &-name {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 14px;
    color: #181C25;
  }
}

.dht-product-commodity-labels {
  font-size: 12px;
  color: #FFFFFF;
  line-height: 20px;
  margin-right: 5px;
  border-radius: 4px;
  padding: 1px 4px;
  height: 20px;
  margin-top: 3px;
  &.dht-labels1 {
    background-color: #624027;
  }
  &.dht-labels2 {
    background-color: #E0975E;
  }
}

.dht-product-commodity-labels-warpper {
  display: flex;
}
</style> 