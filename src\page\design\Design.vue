<template>
  <div class="dht-product-design">
    <div class="dht-product-design-header">
      <fx-button type="primary" size="small" @click="save">{{$t('保存')}} </fx-button>
    </div>
    <BizPageDesigner
        ref="customPageDesigner"
        :layoutApiName="layoutApiName"
        :appId="appId"
        :deviceType="'web'"
        :appType="appType"
        businessScene="dht-product-detail"
        :layoutConfig="{layoutType: '1'}"
        :isEdit="true"
        :isConnectApp="false"
        :options="dOptions"
    ></BizPageDesigner>
  </div>
</template>

<script>
import { dhtBizModel } from '@components/web/utils/model';

// const pageAppId = 'FSAID_11490c84-shopping';
const pageAppId = 'FSAID_11490c84-web-product-detail';
export default {
  components: {
    BizPageDesigner: () => Fx.getBizComponent('paasbiz', 'BizPageDesigner').then(res => res())
  },
  data() {
    return {
      // 商品列表
      showDesigner: false,
      layoutApiName: pageAppId,
      appId: pageAppId,
      appType: '6',
      args: {
        appId: pageAppId,
        appType: '6', 
      },
      objData: {},
      dOptions: {
        node: {
          data: {
            test123: 'test123',
            objectContext: {
              // 单条数据
              data: this.objData,
              // 对象描述
              describe: {
                fields: this.objFields,
                api_name: 'ProductObj',
              }
            }
          }
        }
      }
    }
  },
  computed: {
    mainObjApiName() {
      return dhtBizModel.isSpuMode() ? 'SPUObj' : 'ProductObj';
    }
  },
  methods: {
    save() {
      const data = this.$refs.customPageDesigner.getValue();
      console.log('data', data);

      let dht_web_product_detail_all = data.customerLayout.components.find(item => item.type === 'dht_web_product_detail_all');
      console.log('dht_web_product_detail_all', dht_web_product_detail_all);

      // 写入本地缓存中
      localStorage.setItem('dhtbiz_dht_web_product_detail_all', JSON.stringify(dht_web_product_detail_all));
      
      /* data2 = {
        "pageMultiType": 0,
        "pageLayoutType": 1,
        "customerLayout": {
        "layout": [
            {
                "columns": [
                    {
                        "width": "100%"
                    }
                ],
                "components": [
                    [
                        "widget_dht_web_product_detail_all_m56ebubl"
                    ]
                ]
            }
        ],
        "components": [
            {
                "appId": "FSAID_11490c84-shopping",
                "img": "1",
                "buttons": [],
                "related_list_name": "",
                "type": "dht_web_product_detail_all",
                "selectedFields": [
                    "name"
                ],
                "api_name": "widget_dht_web_product_detail_all_m56ebubl",
                "price": "1",
                "limit": 1,
                "name": "参数信息ss",
                "header": "商品详情",
                "showType": "1",
                "tag": "0",
                "attr": "2",
                "stock": "1",
                "title": "商品详情",
                "newHeader": "商品详情",
                "titleName": "商品详情",
                "nameI18nKey": "dht.component.web.product_detail_all",
                "grayLimit": 1,
                "newTitle": "商品详情"
            }
        ]
    },
    "customerLayoutList": [
        {
            "layout": [
                {
                    "columns": [
                        {
                            "width": "100%"
                        }
                    ],
                    "components": [
                        [
                            "widget_dht_web_product_detail_all_m56ebubl"
                        ]
                    ]
                }
            ],
            "components": [
                {
                    "appId": "FSAID_11490c84-shopping",
                    "img": "1",
                    "buttons": [],
                    "related_list_name": "",
                    "type": "dht_web_product_detail_all",
                    "selectedFields": [
                        "name"
                    ],
                    "api_name": "widget_dht_web_product_detail_all_m56ebubl",
                    "price": "1",
                    "limit": 1,
                    "name": "参数信息ss",
                    "header": "商品详情",
                    "showType": "1",
                    "tag": "0",
                    "attr": "2",
                    "stock": "1",
                    "title": "商品详情",
                    "newHeader": "商品详情",
                    "titleName": "商品详情",
                    "nameI18nKey": "dht.component.web.product_detail_all",
                    "grayLimit": 1,
                    "newTitle": "商品详情"
                }
            ]
        }
    ]
}
      
      let data1 = {
        "pageMultiType": 0,
        "pageLayoutType": 1,
        "customerLayout": {
        "layout": [
            {
                "columns": [
                    {
                        "width": "100%"
                    }
                ],
                "components": [
                    [
                        "widget_dht_web_product_detail_card_m53mam4x",
                        "widget_dht_web_product_detail_key_info_m53mam55"
                    ]
                ]
            }
        ],
        "components": [
            {
                "appId": "FSAID_11490c84-shopping",
                "name": "商品卡片",
                "img": "1",
                "buttons": [],
                "related_list_name": "",
                "type": "dht_web_product_detail_card",
                "api_name": "widget_dht_web_product_detail_card_m53mam4x",
                "price": "1",
                "limit": 1,
                "header": "商品卡片",
                "tag": "1",
                "attr": 0,
                "stock": "1",
                "title": "商品卡片",
                "newHeader": "商品卡片",
                "titleName": "商品卡片",
                "nameI18nKey": "dht.component.web.product_detail_card",
                "grayLimit": 1,
                "newTitle": "商品卡片"
            },
            {
                "appId": "FSAID_11490c84-shopping",
                "name": "参数信息",
                "showType": "1",
                "selectedFields": [
                    "name"
                ],
                "buttons": [],
                "api_name": "widget_dht_web_product_detail_key_info_m53mam55",
                "related_list_name": "",
                "limit": 1,
                "header": "参数信息",
                "type": "dht_web_product_detail_key_info",
                "title": "参数信息",
                "newHeader": "参数信息",
                "titleName": "参数信息",
                "nameI18nKey": "dht.component.web.product_detail_key_info",
                "grayLimit": 1,
                "newTitle": "参数信息"
            }
        ]
    },
        "customerLayoutList": [
        {
            "layout": [
                {
                    "columns": [
                        {
                            "width": "100%"
                        }
                    ],
                    "components": [
                        [
                            "widget_dht_web_product_detail_card_m53mam4x",
                            "widget_dht_web_product_detail_key_info_m53mam55"
                        ]
                    ]
                }
            ],
            "components": [
                {
                    "appId": "FSAID_11490c84-shopping",
                    "name": "商品卡片",
                    "img": "1",
                    "buttons": [],
                    "related_list_name": "",
                    "type": "dht_web_product_detail_card",
                    "api_name": "widget_dht_web_product_detail_card_m53mam4x",
                    "price": "1",
                    "limit": 1,
                    "header": "商品卡片",
                    "tag": "1",
                    "attr": 0,
                    "stock": "1",
                    "title": "商品卡片",
                    "newHeader": "商品卡片",
                    "titleName": "商品卡片",
                    "nameI18nKey": "dht.component.web.product_detail_card",
                    "grayLimit": 1,
                    "newTitle": "商品卡片"
                },
                {
                    "appId": "FSAID_11490c84-shopping",
                    "name": "参数信息",
                    "showType": "1",
                    "selectedFields": [
                        "name"
                    ],
                    "buttons": [],
                    "api_name": "widget_dht_web_product_detail_key_info_m53mam55",
                    "related_list_name": "",
                    "limit": 1,
                    "header": "参数信息",
                    "type": "dht_web_product_detail_key_info",
                    "title": "参数信息",
                    "newHeader": "参数信息",
                    "titleName": "参数信息",
                    "nameI18nKey": "dht.component.web.product_detail_key_info",
                    "grayLimit": 1,
                    "newTitle": "参数信息"
                }
            ]
        }
    ]
      }
     */
    },
    init() {
      dhtBizModel.fetchObjFields(this.mainObjApiName).then(fields => {
        // this.
        this.objFields = fields;
        this.dOptions.node.data.objectContext.describe.fields = fields;
        this.showDesigner = true;
        console.log('fetchObjFields fields', fields);
        
        // const api_name = this.$attrs.api_name;
        // this.setProps(api_name, (props) => {
        //   props.fields = fields;  
        // });
      });
    }
  },
  created() {
    console.error('created');
    this.init();
  },
  mounted() {
  }
};
</script>

<style lang="less">
  .dht-product-design {
    height: 100%;
    .dht-product-design-header {
      display: flex;
      justify-content: flex-end;
      padding: 10px 0;
      margin-right: 36px;
    }
    .bc-set-bar {
      width: 350px !important;
    }
  }
</style>
