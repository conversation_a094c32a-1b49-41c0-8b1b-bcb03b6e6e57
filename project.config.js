const path = require('path');

module.exports = {
    //
    // 工程名称
    // 可通过window['dhtbiz']访问
    name: 'dhtbiz',

    //
    // 默认打开的hash
    // 注意： 开启多工程的代理的时候，默认地址一定要是这一个
    // 否则多工程的代码变更不会监听
    openHash: '#project/dhtbiz/index',

    //
    // 缓存文件目录
    // 默认在模块中
    cacheDir: path.resolve(__dirname, './cache'),

    //
    // 是否开启热更新
    hot: true,

    //
    // 可过滤模版的函数
    //
    tplFn: null,

    //
    // 根据路径映射文件
    // 多用于开发多工程时
    //
    static: [
        {
            directory: path.resolve(__dirname, './release/'),
            publicPath: '/html/dhtbiz/',
        },
    ],

    //
    // 配合static使用
    // 替换掉入口文件
    // key 为tpl_config中生产的key
    // crm:  true,
    // fs:  true,
    // fx:   true,
    // fxui: true,
    // npaas: true,
    // qx: true,
    // icmanage: true,
    // appCommon: true,
    // appStandalone: true,
    // 'paasBpmSvg':'svg.js',
    // 'appSalaryJsApp':'app.js',
    // ....
    entrys: {
        // crm:  true,
        dhtbizJsEntry: 'app.js', // 当前工程的入口
        dhtbizCssEntry: 'app.css', // 当前工程的入口
    },

    //
    // 默认账号
    account: {
        ceshi112: {
            enterprise: 'fsceshi003',
            account: 'shiwj',
            password: '123qwe',
        },
        fktest: {
            enterprise: 'fktest086',
            account: 'a01',
            password: '1234qwer',
        },
    },
};
