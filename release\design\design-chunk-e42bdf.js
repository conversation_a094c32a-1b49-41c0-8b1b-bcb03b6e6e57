"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[598],{6738:(t,e,n)=>{n.r(e),n.d(e,{default:()=>d});var s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dht-product-design"},[n("div",{staticClass:"dht-product-design-header"},[n("fx-button",{attrs:{type:"primary",size:"small"},on:{click:t.save}},[t._v(t._s(t.$t("保存"))+" ")])],1),t.showDesigner?n("CustomPageDesigner",{ref:"customPageDesigner",attrs:{layoutApiName:t.layoutApiName,appId:t.appId,args:t.args,options:t.dOptions}}):t._e()],1)};s._withStripped=!0;var a=n(5861),i=n(4687),o=n.n(i),r=n(1141),u="FSAID_11490c84-shopping";var p;const c={components:{CustomPageDesigner:(p=(0,a.Z)(o().mark((function t(){return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Fx.getBizComponent("paasbiz","CustomPageDesigner");case 2:return t.t0=t.sent,t.abrupt("return",(0,t.t0)());case 4:case"end":return t.stop()}}),t)}))),function(){return p.apply(this,arguments)})},data:function(){return{showDesigner:!1,layoutApiName:u,appId:u,appType:"6",args:{appId:u,appType:"6"},objData:{},dOptions:{node:{data:{objectContext:{data:this.objData,describe:{fields:this.objFields,api_name:"ProductObj"}}}}}}},computed:{mainObjApiName:function(){return r.Z.isSpuMode()?"SPUObj":"ProductObj"}},methods:{save:function(){var t=this.$refs.customPageDesigner.getValue().customerLayout.components.find((function(t){return"dht_web_product_detail_all"===t.type}));localStorage.setItem("dhtbiz_dht_web_product_detail_all",JSON.stringify(t))},init:function(){var t=this;r.Z.fetchObjFields(this.mainObjApiName).then((function(e){t.objFields=e,t.dOptions.node.data.objectContext.describe.fields=e,t.showDesigner=!0}))}},created:function(){this.init()},mounted:function(){}};const d=(0,n(1900).Z)(c,s,[],!1,null,null,null).exports}}]);