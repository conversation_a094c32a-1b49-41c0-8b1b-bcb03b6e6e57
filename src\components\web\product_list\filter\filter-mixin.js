// 筛选组件公用 mixin
// 包含筛选组件的通用逻辑和方法

export default {
  props: {
    // 需要筛选的api_name ,仅支持input 和 selected
    filter_fields: {
      type: Array,
      default: () => []
    },
    objectFields: { 
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      expandedSections: {},
      // 存储各个筛选项的选中值
      selectedValues: {},
      // currency 区间响应式数据
      currencyRange: {}, // { field: { min: '', max: '', error: '' } }
    }
  },

  watch: {
    // 监听 filter_fields 变化，初始化选中值
    filter_fields: {
      handler(newFields) {
        this.initSelectedValues(newFields);
        this.initCurrencyRange(newFields);
      },
      immediate: true
    }
  },

  created() {
    console.log('filter mixin created, filter_fields:', this.filter_fields);
    this.initSelectedValues(this.filter_fields);
    this.initCurrencyRange(this.filter_fields);
  },

  methods: {
    /**
     * 初始化选中值
     */
    initSelectedValues(fields) {
      fields.forEach(field => {
        const fieldType = this.getFieldType(field);
        if (fieldType === 'select_many') {
          this.$set(this.selectedValues, field, []); // 保证响应式
        } else {
          this.$set(this.selectedValues, field, '');
        }
      });
    },

    /**
     * 获取字段的显示标签
     */
    getFieldLabel(fieldName) {
      const field = this.objectFields[fieldName];
      return field ? field.label : fieldName;
    },

    /**
     * 获取字段类型
     */
    getFieldType(fieldName) {
      const field = this.objectFields[fieldName];
      return field ? field.type : 'input';
    },

    /**
     * 获取字段的选项列表
     */
    getFieldOptions(fieldName) {
      const field = this.objectFields[fieldName];
      return field && field.options ? field.options : [];
    },

    /**
     * 初始化 currency 区间数据
     */
    initCurrencyRange(fields) {
      const range = {};
      fields.forEach(field => {
        if (this.getFieldType(field) === 'currency') {
          range[field] = { min: '', max: '', error: '' };
        }
      });
      this.currencyRange = range;
    },
    /**
     * 处理 currency 区间输入
     * 仅在 change/blur 时触发，且校验通过才触发 onFilterChange
     */
    onCurrencyInput(field) {
      const { min, max } = this.currencyRange[field];
      let error = '';
      if (min !== '' && max !== '' && Number(max) < Number(min)) {
        error = '最大值不能小于最小值';
      }
      this.currencyRange[field].error = error;
      if (!error) {
        this.onFilterChange(field, { min, max });
      }
      // 校验失败时不触发 onFilterChange
    },
    /**
     * 处理 input 类型输入，仅在 change/blur 时触发
     */
    onInputFieldChange(field, event) {
      this.onFilterChange(field, event.target ? event.target.value : event);
    },

    /**
     * 重写 onFilterChange，处理 currency 类型
     */
    onFilterChange(fieldName, value) {
      if (this.getFieldType(fieldName) === 'currency') {
        this.$set(this.currencyRange[fieldName], 'min', value.min);
        this.$set(this.currencyRange[fieldName], 'max', value.max);
      } else {
        this.$set(this.selectedValues, fieldName, value);
      }
      // 构建筛选条件数组
      const allFilters = [];
      const filter_fields = this.filter_fields;
      for (const field of filter_fields) {
        let type = this.getFieldType(field);
        let filter = null;
        if (type === 'select_many' && this.selectedValues[field] && this.selectedValues[field].length > 0) {
          filter = {
            field_name: field,
            field_values: this.selectedValues[field],
            operator: 'HASANYOF',
          };
        } else if (type === 'select_one' && this.selectedValues[field]) {
          filter = {
            field_name: field,
            field_values: [this.selectedValues[field]],
            operator: 'EQ',
          };
        } else if ((type === 'input' || type === 'text') && this.selectedValues[field]) {
          filter = {
            field_name: field,
            field_values: [this.selectedValues[field]],
            operator: 'LIKE',
          };
        } else if (type === 'currency' && this.currencyRange[field]) {
          const { min, max, error } = this.currencyRange[field];
          if (!error) {
            if (min !== '' && !isNaN(Number(min))) {
              allFilters.push({
                field_name: field,
                field_values: [Number(min)],
                operator: 'GTE',
              });
            }
            if (max !== '' && !isNaN(Number(max))) {
              allFilters.push({
                field_name: field,
                field_values: [Number(max)],
                operator: 'LTE',
              });
            }
          }
        }
        if (filter) {
          allFilters.push(filter);
        }
      }
      // 触发筛选变化事件，向父组件传递筛选数据
      this.$emit('filter-change', {
        field: fieldName,
        value: value,
        allFilters
      });
    },

    /**
     * 获取当前所有筛选条件
     */
    getFilterValues() {
      return this.selectedValues;
    },

    /**
     * 重置所有筛选条件
     */
    resetFilters() {
      this.initSelectedValues(this.filter_fields);
      this.$emit('filter-change', {
        field: null,
        value: null,
        allFilters: []
      });
    },

    /**
     * 设置筛选值（外部调用）
     */
    setFilterValues(values) {
      Object.keys(values).forEach(field => {
        this.$set(this.selectedValues, field, values[field]);
      });
    },

    /**
     * 切换筛选项的展开/收起状态
     */
    toggleSection(fieldName) {
      this.$set(this.expandedSections, fieldName, !this.expandedSections[fieldName]);
    },
  }
}
