<template>
  <div class="top-filter">
    <div class="filter-container">
      <div 
        v-for="filterField in filter_fields" 
        :key="filterField"
        class="filter-item"
      >
        <div class="filter-label">{{ getFieldLabel(filterField) }}</div>
        
        <!-- 单选筛选项 -->
        <fx-select 
          v-if="getFieldType(filterField) === 'select_one'"
          v-model="selectedValues[filterField]"
          @change="onFilterChange(filterField, $event)"
          :placeholder="`请选择${getFieldLabel(filterField)}`"
          clearable
          class="filter-select"
        >
          <fx-option 
            v-for="option in getFieldOptions(filterField)"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </fx-select>
        
        <!-- 多选筛选项 -->
        <fx-select 
          v-else-if="getFieldType(filterField) === 'select_many'"
          v-model="selectedValues[filterField]"
          @change="onFilterChange(filterField, $event)"
          :placeholder="`请选择${getFieldLabel(filterField)}`"
          multiple
          clearable
          class="filter-select"
        >
          <fx-option 
            v-for="option in getFieldOptions(filterField)"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </fx-select>
        
        <!-- 金额区间筛选 -->
        <div v-else-if="getFieldType(filterField) === 'currency'" class="filter-currency-range">
          <fx-input
            v-model="currencyRange[filterField].min"
            :placeholder="'￥ 最小值'"
            class="filter-currency-input"
            @change="onCurrencyInput(filterField)"
            @blur="onCurrencyInput(filterField)"
            type="number"
            min="0"
          />
          <span class="currency-range-separator">-</span>
          <fx-input
            v-model="currencyRange[filterField].max"
            :placeholder="'￥ 最大值'"
            class="filter-currency-input"
            @change="onCurrencyInput(filterField)"
            @blur="onCurrencyInput(filterField)"
            type="number"
            min="0"
          />
        </div>
        
        <!-- 字段筛选 -->
        <fx-input
          v-else
          v-model="selectedValues[filterField]"
          :placeholder="`请输入${getFieldLabel(filterField)}`"
          @change="onFilterChange(filterField, $event)"
          @blur="onFilterChange(filterField, $event)"
          class="filter-input"
        />
        
        <!-- 错误提示 -->
        <div v-if="getFieldType(filterField) === 'currency' && currencyRange[filterField] && currencyRange[filterField].error" 
             class="currency-range-error">
          {{ currencyRange[filterField].error }}
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="filter-actions">
        <fx-button @click="resetFilters" size="small">重置</fx-button>
      </div>
    </div>
  </div>
</template>

<script>
// @vue/component
import FilterMixin from '../../../filter-mixin.js';

export default {
  name: 'TopFilter',

  components: {},

  mixins: [FilterMixin],

  data () {
    return {
      // selectedValues 从 FilterMixin 继承
    }
  },

  computed: {},

  watch: {
    // 监听 filter_fields 变化，FilterMixin 中已处理 initSelectedValues 和 initCurrencyRange
  },

  created () {
    console.log('top-filter created, filter_fields:', this.filter_fields);
    // initSelectedValues 和 initCurrencyRange 在 FilterMixin 中处理
  },

  methods: {
    // 以下方法从 FilterMixin 继承：
    // - getFieldLabel
    // - getFieldType
    // - getFieldOptions
    // - onFilterChange
    // - onCurrencyInput
    // - getFilterValues
    // - resetFilters
    // - setFilterValues
    // - initSelectedValues
    // - initCurrencyRange
  }
}
</script>

<style lang="less" scoped>
.top-filter {
  width: 100%;
  background: #fff;
  border-radius: 4px;
  padding: 16px;

  .filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-start;

    .filter-item {
      display: flex;
      flex-direction: column;
      min-width: 200px;

      .filter-label {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
      }

      .filter-select,
      .filter-input {
        width: 200px;

        /deep/ .fx-input__inner {
          font-size: 13px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;

          &:focus {
            border-color: #409eff;
          }
        }
      }

      .filter-currency-range {
        display: flex;
        align-items: center;
        gap: 8px;

        .filter-currency-input {
          width: 90px;
        }

        .currency-range-separator {
          color: #999;
          font-size: 14px;
          margin: 0 4px;
        }
      }

      .currency-range-error {
        color: #f56c6c;
        font-size: 12px;
        margin-top: 4px;
      }
    }

    .filter-actions {
      display: flex;
      align-items: flex-end;
      padding-bottom: 4px;

      .fx-button {
        margin-left: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .top-filter {
    .filter-container {
      flex-direction: column;

      .filter-item {
        width: 100%;

        .filter-select,
        .filter-input {
          width: 100%;
        }
      }

      .filter-actions {
        width: 100%;
        justify-content: flex-end;
        padding-top: 16px;
        border-top: 1px solid #EAEBEE;
      }
    }
  }
}
</style>
