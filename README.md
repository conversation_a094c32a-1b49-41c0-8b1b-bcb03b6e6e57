# dhtbiz
## 目的
构建新的暴露给主站的业务模块, 包含api, components, actions等, 其中components兼容beecrafjs设计器和老的设计器,也即运行态和设计态都在其中构建. 



## 规范
参考地址: 
https://fe.firstshare.cn/paas-fe-biz-doc/guide/other/%E5%A6%82%E4%BD%95%E5%88%9B%E5%BB%BA%E6%96%B0%E5%B7%A5%E7%A8%8B%EF%BC%88%E7%BB%84%E4%BB%B6%E5%92%8C%E5%8A%A8%E4%BD%9C%EF%BC%89.html#%E7%AC%AC%E4%BA%8C%E6%AD%A5-%E4%BB%8Edhtbiz%E9%A1%B9%E7%9B%AE-%E5%A4%8D%E5%88%B6%E5%87%BA%E6%9E%84%E5%BB%BA%E7%9A%84%E8%84%9A%E6%89%8B%E6%9E%B6

## 发布

该工程需要本地构建！！！

1. npm run build
2. r2发布
- 项目分组：fe-sail
- dhtbiz

## 注意

1、components 和 action的暴露需要遵循以下规范：
- 遵循 Promise 风格
```
Fx.getBizComponent('dhtbiz','test').then(text => {
    console.log(text);
})

Fx.getBizAction('dhtbiz','test').then(text => {
    console.log(text);
})

```


- 使用异步的方式
```
export default function() {
    return (() => import('./src/test'))().then(fn => fn(...arguments));
}
```
