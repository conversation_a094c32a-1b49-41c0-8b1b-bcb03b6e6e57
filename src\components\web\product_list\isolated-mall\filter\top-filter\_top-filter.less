.top-filter {
  width: 100%;
  position: relative;
  
  .filter-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    
    .filter-item {
      position: relative;
      
      .filter-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        white-space: nowrap;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 4px 8px;
        margin-right: 8px;
        cursor: pointer;
        transition: all 0.2s;
        user-select: none;
        background: #fff;
        box-sizing: border-box;
        
        &:hover {
          border-color: #409eff;
          color: #409eff;
        }
        
        &.selected {
          border-color: #409eff;
          color: #409eff;
          background: #f0f7ff;
        }
        
        .label-text {
          flex-shrink: 0;
        }
        
        .expand-icon {
          font-size: 12px;
          transition: transform 0.2s;
          color: #909399;
          flex-shrink: 0;
          
          &.expanded {
            transform: rotate(180deg);
            color: #409eff;
          }
        }
        
        .selected-tags {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-left: 8px;
          flex-wrap: wrap;
          
          .selected-tag {
             background: #409eff;
             color: #fff;
             font-size: 12px;
             font-weight: 400;
             padding: 2px 6px;
             border-radius: 2px;
             white-space: nowrap;
             max-width: 80px;
             overflow: hidden;
             text-overflow: ellipsis;
             
             &.more-tag {
               background: #909399;
               font-style: normal;
             }
           }
        }
      }
      
             // 展开状态下的label样式调整
       &.is-expanded .filter-label {
         border-bottom-left-radius: 0;
         border-bottom-right-radius: 0;
         border-bottom: none;
         border-color: #409eff;
         color: #409eff;
         background: #f0f7ff;
         z-index: 2;
         position: relative;
       }
    }    
  }
  
  // 展开的筛选控制面板
  .filter-control-panel {
    display: flex;
    position: absolute;
    top: calc(100% - 1px); // 与上边框重叠1px实现无缝连接
    left: 0; // 与内容区左对齐
    right: 0; // 与内容区右对齐
    background: #f0f7ff; // 与展开label背景色一致
    border-radius: 0 0 4px 4px;
    padding: 16px;
    z-index: 1; // 降低层级，让label在上方
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.2s ease-out;
    margin-top: 0; // 确保紧贴label

    .filter-control-content {
      flex: 1;
    }
      
    
    .filter-radio-group,
    .filter-checkbox-group {
      display: flex;
      flex-direction: row;
      gap: 16px;
      flex-wrap: wrap;
      align-items: flex-start;
    }
    
    .filter-option {
       margin-right: 0;
       margin-bottom: 8px;
       white-space: nowrap;
       background: #fff; // 添加白色背景确保选项清晰
       border-radius: 4px;
       padding: 4px 8px;
       border: 1px solid transparent;
       transition: all 0.2s;
       
       &:hover {
         border-color: #409eff;
         box-shadow: 0 1px 3px rgba(64, 158, 255, 0.1);
       }
     }
     
     .filter-actions {
       width: 50px;
       text-align: right;
       display: flex;
       align-items: center;
       flex-direction: row-reverse;
       
       .dht-clear-btn {
         font-size: 12px;
        }
     }
  }
}

// 展开动画
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
