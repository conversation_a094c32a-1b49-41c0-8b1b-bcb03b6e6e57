"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[82],{7773:(t,e,n)=>{n.r(e),n.d(e,{default:()=>d});var s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dht-product-detail-key-info-display"},[n("div",{staticClass:"dht-title"},[t._v(t._s(t.$t("dht.component.web.product_detail_key_info.title","参数信息")))]),n("div",{staticClass:"dht-content"},t._l(t.cFields,(function(e,s){return n("div",{key:e.key,staticClass:"dht-item",class:{"dht-double":"2"===t.showType}},[n("div",{staticClass:"dht-field"},[n("span",{staticClass:"dht-text dht-name"},[t._v(t._s(e.name))]),n("span",{staticClass:"dht-text dht-val"},[t._v(t._s(e.val||"--"))])])])})),0)])};s._withStripped=!0;var a=n(5861),i=n(4687),l=n.n(i),r=n(1141);const c={inject:["useInternalNode"],props:{objectContext:{type:Object,default:function(){return{}}},showType:{type:String,default:"1"},selectedFields:{type:Array,default:function(){return["name"]}}},computed:{cFields:function(){var t=this.selectedFields||[],e=this.allFields;return t.map((function(t){var n=e[t]||{};return{key:t,name:n.label_r||n.label||"--",val:"--"}}))}},data:function(){return{allFields:[]}},methods:{init:function(){var t=this;return(0,a.Z)(l().mark((function e(){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.Z.fetchObjFields(r.Z.mainObjApiName());case 2:t.allFields=e.sent;case 3:case"end":return e.stop()}}),e)})))()}},created:function(){this.init()}};const d=(0,n(1900).Z)(c,s,[],!1,null,"861248b0",null).exports}}]);