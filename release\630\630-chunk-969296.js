"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[630],{9630:(t,e,i)=>{i.r(e),i.d(e,{default:()=>s});var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dht-product-detail-key-info-running"},[i("div",{staticClass:"dht-title"},[t._v(t._s(t.cName))]),i("div",{staticClass:"dht-content"},t._l(t.cFields,(function(e,a){return i("div",{key:e.key,staticClass:"dht-item",class:{"dht-double":"2"===t.showType}},[i("div",{staticClass:"dht-field"},[i("div",{staticClass:"dht-text dht-name"},[t._v(t._s(e.name))]),i("div",{staticClass:"dht-text dht-val"},["file_attachment"===e.type?i("div",[e.value&&e.value.length?void 0:[t._v("--")]],2):"image"===e.type?i("div",[e.value&&e.value.length?t._l(e.value,(function(a,n){return i("div",{key:n,staticClass:"dht-meta-item-img-wrap",on:{click:function(i){return t.previewPicture(e.value,n)}}},[i("fx-image",{staticClass:"dht-meta-item-img",attrs:{src:a.smallUrl,fit:"fill"}},[i("div",{staticStyle:{"font-size":"18px"},attrs:{slot:"error"},slot:"error"},[i("i",{staticClass:"el-icon-picture-outline"})])])],1)})):[t._v("--")]],2):"url"===e.type?i("div",[e.value?i("a",{staticClass:"link-url",attrs:{href:e.value,target:"_blank"}},[t._v("\n              "+t._s(e.value)+"\n            ")]):[t._v("--")]],2):"long_text"===e.type?i("div",t._l(e.value,(function(e,a){return i("p",{key:a},[t._v(t._s(e))])})),0):[t._v(t._s(e.value||"--"))]],2)])])})),0)])};a._withStripped=!0;const n={name:"dht_web_product_detail_key_info",props:{name:{type:String,default:""},showType:{type:String,default:"1"},selectedFields:{type:Array,default:function(){return[]}},objectContext:{type:Object,default:function(){return{data:{},describe:{}}}}},computed:{cName:function(){return this.name||""},cFields:function(){var t=this.selectedFields||[],e=this.objectContext.describe.fields||[],i=this.objectContext.data||{};return t.map((function(t){var a=e[t]||{};return{key:t,type:a.type,name:a.label_r||a.label||"--",value:i[t]||"--"}}))}},data:function(){return{}},methods:{init:function(){},previewPicture:function(t,e){}},mounted:function(){this.init()}};const s=(0,i(1900).Z)(n,a,[],!1,null,"37c7f2cb",null).exports}}]);