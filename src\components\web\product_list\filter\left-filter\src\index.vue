<template>
  <div class="left-filter">
    <!--for debug  <pre>{{ selectedValues }}</pre> -->
    <div
      v-for="filterField in filter_fields"
      :key="filterField"
      class="filter-section"
    >
      <div
        class="filter-header"
        @click="toggleSection(filterField)"
      >
        <span class="filter-title">{{ getFieldLabel(filterField) }}</span>
        <span
          class="expand-icon fx-icon-arrow-down"
          :class="{ 'expanded': expandedSections[filterField] }"
        >
        </span>
      </div>

      <div
        v-show="expandedSections[filterField]"
        class="filter-content"
      >
        <!-- 单选筛选项 -->
        <fx-radio-group
          v-if="getFieldType(filterField) === 'select_one'"
          v-model="selectedValues[filterField]"
          @change="onFilterChange(filterField, $event)"
        >
          <fx-radio
            v-for="option in getFieldOptions(filterField)"
            :key="option.value"
            :label="option.value"
            class="filter-option"
          >
            {{ option.label }}
          </fx-radio>
        </fx-radio-group>

        <!-- 多选筛选项 -->
        <fx-checkbox-group
          v-else-if="getFieldType(filterField) === 'select_many'"
          v-model="selectedValues[filterField]"
          @change="onFilterChange(filterField, $event)"
        >
          <fx-checkbox
            v-for="option in getFieldOptions(filterField)"
            :key="option.value"
            :label="option.value"
            class="filter-option"
          >
            {{ option.label }}
          </fx-checkbox>
        </fx-checkbox-group>

        <!-- 金额区间筛选 -->
        <div v-else-if="getFieldType(filterField) === 'currency'" class="filter-currency-range">
          <fx-input
            v-model="currencyRange[filterField].min"
            :placeholder="'￥ 最小值'"
            class="filter-currency-input"
            @change="onCurrencyInput(filterField)"
            @blur="onCurrencyInput(filterField)"
            type="number"
            min="0"
          />
          <span class="currency-range-separator">-</span>
          <fx-input
            v-model="currencyRange[filterField].max"
            :placeholder="'￥ 最大值'"
            class="filter-currency-input"
            @change="onCurrencyInput(filterField)"
            @blur="onCurrencyInput(filterField)"
            type="number"
            min="0"
          />
          <div v-if="currencyRange[filterField].error" class="currency-range-error">{{ currencyRange[filterField].error }}</div>
        </div>
        <!-- 字段筛选 -->
        <fx-input
          v-else
          v-model="selectedValues[filterField]"
          :placeholder="`请输入${getFieldLabel(filterField)}`"
          @change="onFilterChange(filterField, $event)"
          @blur="onFilterChange(filterField, $event)"
          class="filter-input"
        ></fx-input>
      </div>
    </div>
  </div>
</template>

<script>
// @vue/component
import FilterMixin from '../../../filter-mixin.js';

export default {
  name: 'LeftFilter',

  components: {},

  mixins: [FilterMixin],

  data () {
    return {
      // selectedValues 从 FilterMixin 继承
    }
  },

  computed: {},

  watch: {
    // 监听 filter_fields 变化，初始化展开状态
    filter_fields: {
      handler(newFields) {
        this.initExpandedSections(newFields);
        // initSelectedValues 在 FilterMixin 中处理
      },
      immediate: true
    }
  },

  created () {
    console.log('left-filter created, filter_fields:', this.filter_fields);
    this.initExpandedSections(this.filter_fields);
    // initSelectedValues 在 FilterMixin 中处理
  },

  methods: {

    /**
     * 初始化展开状态，默认第一个筛选项展开
     */
    initExpandedSections(fields) {
      const expandedSections = {};
      fields.forEach((field, index) => {
        expandedSections[field] = index === 0; // 默认第一个展开
      });
      this.expandedSections = expandedSections;
    },

    // 以下方法从 FilterMixin 继承：
    // - getFieldLabel
    // - getFieldType
    // - getFieldOptions
    // - onFilterChange
    // - getFilterValues
    // - resetFilters
    // - setFilterValues

  }
}
</script>

<style lang="less" scoped>
.left-filter {
  width: 100%;
  background: #fff;
  border-radius: 4px;

  .filter-section {
    border-bottom: 1px solid #EAEBEE;
    margin-bottom: 24px;
    padding-bottom: 16px;

    &:last-child {
      border-bottom: none;
    }

    .filter-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px 16px 0;

      cursor: pointer;

      .filter-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .expand-icon {
        font-size: 12px;
        color: #999;
        transition: transform 0.3s;
        user-select: none;

        &.expanded {
          transform: rotate(180deg);
        }
      }
    }

    .filter-content {
      padding: 0 16px 8px 0;
      box-sizing: border-box;

      .filter-option {
        display: block;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .filter-input {
        width: 100%;

        /deep/ .fx-input__inner {
          font-size: 13px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;

          &:focus {
            border-color: #409eff;
          }
        }
      }
    }
  }

  // 单选组和多选组的样式
  /deep/ .fx-radio-group,
  /deep/ .fx-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  // 单选和多选项的样式
  /deep/ .fx-radio,
  /deep/ .fx-checkbox {
    margin-right: 0;
    margin-bottom: 0;
  }

  .filter-currency-range {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
  }
  .filter-currency-input {
    width: 90px;
  }
  .currency-range-separator {
    color: #999;
    font-size: 18px;
    margin: 0 4px;
  }
  .currency-range-error {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 2px;
    margin-left: 2px;
  }
}
</style>