<template>
  <div class="dht-left-filter-display">
    <div v-if="loading" class="dht-loading">加载中...</div>
    <left-filter
      v-else
      :filter_fields="finalFilterFields"
      :objectFields="finalObjectFields"
      @filter-change="handleFilterChange"
      ref="leftFilter"
    />
  </div>
</template>

<script>
import LeftFilter from '../src/index.vue';

/**
 * 左侧筛选显示组件
 * 功能：
 * 1. 显示左侧筛选组件
 * 2. 支持配置筛选字段
 * 3. 支持从API获取数据或使用模拟数据
 * 4. 向父组件传递筛选变化事件
 */
export default {
  name: 'dht_web_product_list_left_filter_beecraft_display',
  inject: ['useInternalNode'],
  components: {
    LeftFilter
  },
  props: {
    bizContext: {
      type: Object,
      default: () => ({}),
    },
    filterFields: {
      type: String,
      default: ''
    },
    objectFieldsConfig: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      internalFilterFields: '',
      internalObjectFieldsConfig: ''
    }
  },
  computed: {
    finalFilterFields() {
      const fieldsStr = this.filterFields || this.internalFilterFields;
      if (!fieldsStr) {
        return this.getDefaultFilterFields();
      }
      try {
        return JSON.parse(fieldsStr);
      } catch (e) {
        console.warn('解析 filterFields 失败，使用默认值:', e);
        return this.getDefaultFilterFields();
      }
    },
    finalObjectFields() {
      const configStr = this.objectFieldsConfig || this.internalObjectFieldsConfig;
      if (!configStr) {
        return this.getDefaultObjectFields();
      }
      try {
        return JSON.parse(configStr);
      } catch (e) {
        console.warn('解析 objectFieldsConfig 失败，使用默认值:', e);
        return this.getDefaultObjectFields();
      }
    }
  },
  methods: {
    getDefaultFilterFields() {
      return ['category', 'price', 'brand', 'status'];
    },
    
    getDefaultObjectFields() {
      return {
        category: {
          label: '商品分类',
          type: 'select_one',
          options: [
            { label: '电缸', value: 'cylinder' },
            { label: '电动夹爪', value: 'gripper' },
            { label: '多轴机器人', value: 'robot' },
            { label: '驱动器', value: 'driver' }
          ]
        },
        price: {
          label: '价格区间',
          type: 'currency'
        },
        brand: {
          label: '品牌',
          type: 'input'
        },
        status: {
          label: '状态',
          type: 'select_many',
          options: [
            { label: '在售', value: 'active' },
            { label: '停售', value: 'inactive' },
            { label: '预售', value: 'presale' }
          ]
        }
      };
    },

    handleFilterChange(filterData) {
      console.log('左侧筛选变化:', filterData);
      this.$emit('filter-change', filterData);
    },

    async init() {
      if (this.useInternalNode) {
        this.updateInternalData();
      }
    },

    updateInternalData() {
      if (this.useInternalNode) {
        const { filterFields, objectFieldsConfig } = this.useInternalNode(node => {
          return node.data;
        });

        this.internalFilterFields = filterFields || '';
        this.internalObjectFieldsConfig = objectFieldsConfig || '';

        console.log('Display updated - filterFields:', filterFields, 'objectFieldsConfig:', objectFieldsConfig);
      }
    },

    // 对外暴露的方法
    getFilterValues() {
      return this.$refs.leftFilter ? this.$refs.leftFilter.getFilterValues() : {};
    },

    resetFilters() {
      if (this.$refs.leftFilter) {
        this.$refs.leftFilter.resetFilters();
      }
    },

    setFilterValues(values) {
      if (this.$refs.leftFilter) {
        this.$refs.leftFilter.setFilterValues(values);
      }
    }
  },
  watch: {
    filterFields: {
      handler() {
        this.updateInternalData();
      },
      immediate: false
    },
    objectFieldsConfig: {
      handler() {
        this.updateInternalData();
      },
      immediate: false
    }
  },
  created() {
    this.init();
  },

  mounted() {
    // 定期检查数据变化
    this.dataCheckInterval = setInterval(() => {
      this.updateInternalData();
    }, 500);
  },

  beforeDestroy() {
    if (this.dataCheckInterval) {
      clearInterval(this.dataCheckInterval);
    }
  }
}
</script>

<style lang="less" scoped>
.dht-left-filter-display {
  width: 100%;
  background: #fff;
  border: 1px solid #409eff;
  border-radius: 6px;
  overflow: hidden;

  .dht-loading {
    text-align: center;
    padding: 20px;
    color: #999;
  }
}
</style>
