<template>
  <div class="dht-product-detail-card-display">
    <div class="dht-product-detail dht-product-detail-content">
      <component
        v-if="img == '1'"
        :is="widgets.preview || 'product-preview'"
        v-bind="$attrs"
      />
      <div class="dht-product-detail-main">
        <component 
          v-for="compName in compNames"
          :is="widgets[compName] || compName"
          :key="compName"
          v-bind="$attrs"
          :tag="tag"
          :price="price"
          :attr="attr"
          :stock="stock"
          :objectContext="objectContext"
        />
      </div>
    </div>
  </div>
</template>

<script>
// import { dhtBizModel } from '../../utils/model';
import ProductTitle from './widgets/ProductTitle.vue';
import ProductPrice from './widgets/ProductPrice.vue';
import ProductPromotion from './widgets/ProductPromotion.vue';
import ProductSpec from './widgets/ProductSpec.vue';
import ProductQuantity from './widgets/ProductQuantity.vue';
import ProductOperate from './widgets/ProductOperate.vue';
import ProductPreview from './widgets/ProductPreview.vue';


// import widgetsDetailMixins from './widgets/widgets-detail-mixins';

export default {
  name: 'dht_web_product_detail_card',
  // mixins: [widgetsDetailMixins],
  components: {
    ProductPrice,
    ProductPromotion,
    ProductSpec,
    ProductQuantity,
    ProductTitle,
    ProductOperate,
    ProductPreview
  },
  props: {
    // 允许外部传入自定义组件替换默认组件
    widgets: {
      type: Object,
      default: () => ({})
    },
    img: {
      type: String,
      default: ''
    },
    tag: {
      type: String,
      default: ''
    },
    price: {
      type: String,
      default: ''
    },
    attr: {
      type: String,
      default: '0'
    },
    stock: {
      type: String,
      default: ''
    }
  },
  computed: {
    compNames() {
      return ['product-title', 'product-price', 'product-promotion', 'product-spec', 'product-quantity', 'product-operate'];
    },
  },
  watch: {
    // img: {
    //   handler(newVal) {
    //     console.log('img newVal', newVal);
    //   },
    //   deep: true
    // }
  },
  data() {
    return {
      // objfields: dhtBizModel.getObjFields(),
    }
  },
  methods: {
    init() {
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="less" scoped>
@label-color: #999;

.dht-product-detail {
  font-size: 14px;
  &-content {
    display: flex;
  }

  &-main {
    flex: 1;
    min-width: 420px;
    &-item {
      display: flex;
      align-items: center;
    }
    &-label {
      width: 60px;
      flex: none;
      color: @label-color;
      display: inline-block;
      font-size: 14px;
    }
    &-content {
      flex: 1;
    }
  }
}
</style>
