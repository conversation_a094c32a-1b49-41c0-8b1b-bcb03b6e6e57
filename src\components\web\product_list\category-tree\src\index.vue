<template>
  <div class="dhtbiz-category-tree">
    <category-tree-item
      :items="nodes"
      :level="1"
      @select="handleChildSelect"
    ></category-tree-item>
  </div>
</template>

<script>
import CategoryTreeItem from './category-tree-item.vue';

export default {
  name: 'dht_web_product_list_category_tree',
  components: {
    CategoryTreeItem
  },
  props: {
    dhtPageContext: {
      type: Object,
      default: () => ({})
    },
    dhtContainerApi: {
      type: Object,
      default: () => ({})
    },
    templateData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      nodes: []
    };
  },
  created() {
    this.getTreeList();
  },
  methods: {    
    handleChildSelect(node, level) {
      this.$emit('select', node, level);
    },

    async getTreeList() {
      const storeId = await this.getStoreIdByApp();

      CRM.util.FHHApi({
        url: '/EM1HNCRM/API/v1/object/shop_category/service/query_sub_shop_category_list',
        data: {
          pid: '',
          storeId,
        },
        success: (res) => {
          if (res.Result.StatusCode === 0) {
            this.nodes = this._buildTree(res.Value.shopCategoryList);
          } else {
            CRM.util.alert(res.Result.FailureMessage || $t("暂时无法获取数据") + '!');
          }
        },
        error: (err) => {
          console.error(err);
        }
      }, {
        errorAlertModel: 1
      });
    },

    getStoreIdByApp() {
      return new Promise((resolve, reject) => {
        CRM.util.FHHApi({
          url: '/EM1HNCRM/API/v1/inner/object/online_store/service/get_by_link_app_id',
          data: {
            linkAppId: window.Portal.appId
          },
          success: (res) => {
            if (res.Result.StatusCode === 0) {
              resolve(res.Value.result.storeId);
            } else {
              reject(null);
              CRM.util.alert(res.Result.FailureMessage || $t("暂时无法获取数据") + '!');
            }
          },
          error: (err) => {
            reject(err);
          },
        }, {
          errorAlertModel: 1
        });
      });
    },

    _buildTree(nodeList) {
      // 创建 ID 到节点的映射表
      const nodeMap = {};
      nodeList.forEach(node => {
        // 复制节点数据，避免修改原始数据
        nodeMap[node.id] = { ...node, children: [], active: false };
      });

      // 存储根节点
      const rootNodes = [];

      // 构建树结构
      nodeList.forEach(node => {
        const mappedNode = nodeMap[node.id];
        const parentId = node.pid;

        // 如果父节点不存在或 pid 为空，则视为根节点
        if (!parentId || !nodeMap[parentId]) {
          if (!rootNodes.length) {
            rootNodes.push({
              pid: '',
              name: '全部',
              storeId: mappedNode.storeId,
              id: '1',
              active: true,
            });
          }
          rootNodes.push(mappedNode);
        } else {
          const children = nodeMap[parentId].children;
          if (!children.length) {
            children.push({
              pid: '',
              name: '全部',
              storeId: mappedNode.storeId,
              id: '1',
              active: false,
            });
          }
          // 将当前节点添加到父节点的 children 中
          children.push(mappedNode);
        }
      });

      return rootNodes;
    }
  }
};
</script>

<style lang="less" scoped>
.dhtbiz-category-tree {
  width: 100%;
  margin: 0;
  padding: 0 12px;
  background: white;
  border-radius: 8px;
}
</style> 