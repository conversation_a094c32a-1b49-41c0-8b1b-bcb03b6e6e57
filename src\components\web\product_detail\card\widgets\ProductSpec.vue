<template>
  <div>
    <div class="dht-product-detail-attr-wrapper">
      <div class="dht-product-detail-main-item">
        <div class="dht-product-detail-main-label">{{ $t('规格') }}</div>
        <div class="dht-product-detail-main-content">
          <fx-button type="primary" size="mini" plain="plain">{{ $t('示例') }}1</fx-button>
          <fx-button :type="attr2Checked" size="mini" plain="plain">{{ $t('示例') }}2</fx-button>
          <fx-button type="default" size="mini" plain="plain">{{ $t('示例') }}3</fx-button>
        </div>
      </div>
    </div>

    <!-- 规格展示产品明细行 -->
    <div v-if="!(attr === '0')" class="dht-product-detail-attr-rows-wrapper">
      <div class="dht-product-detail-main-item dht-detail-rows">
        <div v-for="i in 2" :key="'dht_row_'+i" class="dht-row">
          <div v-for="j in 4" :key="'dht_cell_'+j" class="dht-cell-middle-line">&nbsp;</div>
          <fx-input-number :min="1" :max="100" size="micro" :step="1" :value="1" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductSpec',
  props: {
    attr: {
      type: String,
      default: '0'
    }
  },
  computed: {
    attr2Checked() {
      return this.attr === '2' ? 'primary' : 'default';
    }
  }
}
</script>

<style lang="less" scoped>
.dht-product-detail {
  &-attr-wrapper {
    padding: 16px;
  }

  &-attr-rows-wrapper {
    padding: 0 16px 16px;
    .dht-detail-rows {
      display: flex;
      flex-direction: column;
    }
    .dht-row {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: nowrap;      
      border: 1px solid #EEF0F3;
      height: 56px;
      padding: 0 16px;

      .dht-cell-middle-line {
        width: 56px;
        height: 5px;
        border-radius: 4px;
        background: #EAEBED;
      }
    }
  }
}
</style> 