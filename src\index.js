//
// 确保第一个载入
// 不可删除
//
import '../publicpath.config.js';

import '@css/index.less';

import Index from '@page/index/Index';

console.log(222);



const api = {};
const actions = {};
import components from '@components';
const imports = (require) => require.keys().map(require);

imports(require.context('./api', true, /index.js$/)).map((v) => {
    const module = v;
    if (module) {
        Object.keys(module).forEach((key) => {
            if (key === 'default') return;
            if (api[key]) {
                throw new Error(`api ${key} duplicate`);
            }
            api[key] = module[key];
        });
    }
});

// imports(require.context('./actions', true, /index.js$/)).map((v) => {
//     const module = v;
//     if (module) {
//         Object.keys(module).forEach((key) => {
//             if (key === 'default') return;
//             if (actions[key]) {
//                 throw new Error(`actions ${key} duplicate`);
//             }
//             actions[key] = module[key];
//         });
//     }
// });

console.log('dhtbiz加载成功：', components);

let app = {
    //
    //
    isVue: true,

    //
    //
    template: '<div class="dhtbiz" style="height: 100%;"></div>',

    /**
     *
     */
    setup() {
        //alert('初始化工程');
    },

    /**
     *
     * @param {*} to
     * @param {*} from
     * @param {*} next
     */
    breforeEach(to, from, next) {
        //alert('breforeEach')
        // var a =  12;
        next();
    },

    /**
     *
     */
    afterEach(to, from) {
        //var b = c;
        var b = 'ad';
        console.log(from, b);
        console.log('xcxdddddfc');
        //alert('afterEach')
    },

    /**
     * @returns
     */
    routes() {
        return {
            'index(/=/*param)': {
                beforeEnter(to, from, next) {
                    console.log(to, from);
                    // alert('beforeEnter');
                    next();
                },
                component: Index,
            },
            design: () => import( /* webpackChunkName: "design" */ './page/design/Design.vue' ),
            detail: () => import( /* webpackChunkName: "detail" */ './page/detail/Detail.vue' ),
            detail2: () => import( /* webpackChunkName: "detail2" */ './page/detail2/detail2.vue' ),
            test: () => import( /* webpackChunkName: "test" */ './page/test/test.vue' ),
            nt: () => import( /* webpackChunkName: "nt" */ './page/nt/nt.vue' ),
            ts: () => import( /* webpackChunkName: "ts" */ './page/ts/ts.vue' ),
            orderlist: () => import( /* webpackChunkName: "orderlist" */ './page/orderlist/orderlist.vue' )
        };
    },

    /**
     *
     */
    destroy() {
        $('.dhtbiz').off();
        $('.dhtbiz').remove();
    },

    api,
    actions,
    components,
};

export default app;
