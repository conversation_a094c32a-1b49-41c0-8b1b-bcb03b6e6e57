{"name": "dhtbiz", "version": "1.0.0", "description": "a description of the application", "main": "index.js", "engines": {"node": "^16.14.0"}, "scripts": {"sceshi112": "fx-server --ENV=ceshi112 --THEME=new", "fktest": "fx-server --ENV=fktest --THEME=new", "start": "webpack-dev-server --inline --progress --config build/webpack/webpack.dev.js --hot", "dev": "webpack --config build/webpack/webpack.dev.js --watch", "start-host": "rimraf dev && webpack --config build/webpack/webpack.dev.js --watch", "dev:ceshi112": "concurrently \"npm run start-host\" \"npm run sceshi112\"", "dev:fktest": "concurrently \"npm run start-host\" \"npm run fktest\"", "build": "rimraf release && webpack --config build/webpack/webpack.prod.js", "build:ceshi112": "rimraf release && cross-env NODE_ENV=ceshi112 webpack --config build/webpack/webpack.prod.js", "getThemeVars": "node ./tools/getThemeVars.js", "lint:write": "eslint src/ --ext .js,.vue,.json --fix", "init": "node build/replaceAppName.js", "report": "cross-env REPORT=report webpack --config build/webpack/webpack.prod.js ", "lint": "eslint src --ext .js --ext .vue --ext .json --fix", "prepare": "husky install", "prettier": "prettier --write ./src/**/*.js"}, "lint-staged": {"*.js": ["npm run prettier"], "*.{js,vue,json}": ["npm run lint"]}, "keywords": ["template|application"], "author": "wyg", "license": "MIT", "browserslist": ["> 1%", "last 2 versions", "Firefox ESR", "not ie <= 10"], "devDependencies": {"@babel/core": "7.19.1", "@babel/plugin-transform-runtime": "7.19.1", "@babel/preset-env": "7.19.1", "@tools/i18n2": "^1.4.28", "axios": "1.1.3", "babel-loader": "8.2.5", "browserslist": "^4.24.2", "caniuse-lite": "^1.0.30001680", "chai": "^4.2.0", "chalk": "4.1.2", "clean-webpack-plugin": "4.0.0", "concurrently": "^8.2.2", "copy-webpack-plugin": "^5.0.0", "coveralls": "^3.0.3", "cp-cli": "^1.0.2", "cross-env": "7.0.3", "css-loader": "6.7.1", "css-minimizer-webpack-plugin": "4.2.1", "cssnano": "5.1.13", "es6-promise": "^4.0.5", "eslint": "8.23.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-json": "3.1.0", "eslint-plugin-vue": "9.4.0", "fx-git-commit": "1.0.3", "fx-server": "^1.0.117", "happypack": "5.0.1", "husky": "8.0.1", "json5": "2.2.1", "less": "4.1.3", "less-loader": "11.0.0", "lint-staged": "13.0.3", "mini-css-extract-plugin": "2.6.1", "postcss": "8.4.17", "postcss-custom-properties": "12.1.9", "postcss-loader": "7.0.1", "postcss-wrap": "0.0.4", "prettier": "2.7.1", "rimraf": "4.4.1", "style-loader": "3.3.1", "style-resources-loader": "1.5.0", "terser-webpack-plugin": "5.3.6", "vue-loader": "15.10.0", "vue-template-compiler": "2.7.10", "webpack": "5.74.0", "webpack-bundle-analyzer": "4.6.1", "webpack-cli": "4.10.0", "webpack-dev-server": "4.11.1", "webpack-dev-server-output": "0.0.6", "webpack-merge": "5.8.0", "yamljs": "0.3.0"}, "dependencies": {"@babel/runtime-corejs3": "7.21.0", "@beecraft/core": "1.4.11", "@beecraft/engine": "1.4.11", "@beecraft/workbench": "1.4.11", "core-js": "3.25.2", "plugin_base": "^960.0.1"}}