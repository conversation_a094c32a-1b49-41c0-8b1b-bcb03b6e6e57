"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[187],{3153:(t,e,i)=>{i.r(e),i.d(e,{default:()=>c});var n=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)};n._withStripped=!0;const s={components:{},data:function(){return{plugins:[]}},created:function(){this.$$={}},mounted:function(){this.init()},methods:{init:function(){seajs.use("vcrm/sdk",(function(t){this.productDetail=t.widgetService.getWidgetApp("goodDetail",{$el:"#productDetail",propsData:{isSpuMode:!1,isUseByModal:!1,product:{spu_id:"",_id:"65fd4703ca4c5d0007ceeec0"}}})}))}}};const c=(0,i(1900).Z)(s,n,[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"dht-product-detail"},[e("div",{attrs:{id:"productDetail"}})])}],!1,null,null,null).exports}}]);