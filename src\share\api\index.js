export const DhtApi = {
  FHHApi(params) {
    return new Promise((resolve, reject) => {
      CRM.util.FHHApi({
        url: params.url,
        data: params.data,
        success(res) {
          if (res.Result.StatusCode === 0) {
            resolve(res.Value);
          } else {
            CRM.util.alert(res.Result.FailureMessage);
            reject(res);
          }
        },
        error: function(err) {
          reject(err);
        },
      }, {
        errorAlertModel: 1
      });
    });
  }
}
