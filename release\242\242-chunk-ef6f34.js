"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[242],{9242:(t,e,i)=>{i.r(e),i.d(e,{default:()=>u});var s=function(){var t=this.$createElement;return(this._self._c||t)("div",{staticClass:"dhtbiz-quick-order-all",staticStyle:{width:"100%",height:"700px"}})};s._withStripped=!0;var r=i(885),l=i(5927),a=(0,l.XC)("crm-modules/page/quicklyorder/quicklyorder"),n=(0,l.Yc)("crm-assets/style/all.css"),c=(0,l.Yc)("crm-assets/style/page.css");const o={name:"dht_web_quick_order_all",props:{apiname:{type:String,default:"QuicklyOrder"}},methods:{initTable:function(){var t=this;Promise.all([(0,l.ii)(),n,c,a]).then((function(e){var i=(0,r.Z)(e,4),s=(i[0],i[1],i[2],i[3]);t.isDestroy||$(".dhtbiz-quick-order-all")[0]&&(t.$list=new s({wrapper:$(".dhtbiz-quick-order-all"),apiname:t.apiname}),t.$once("hook:beforeDestroy",(function(){t.$List&&t.$list.destroy(),t.$list=null})),t.$list.render())}))}},created:function(){this.initTable()},beforeDestroy:function(){this.isDestroy=!0,this.$list&&this.$list.destroy(),this.$list=null}};const u=(0,i(1900).Z)(o,s,[],!1,null,"0ceb3326",null).exports}}]);