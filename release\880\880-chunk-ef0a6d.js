"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[880,748],{7525:(t,e,n)=>{n.r(e),n.d(e,{default:()=>c});var a=function(){var t=this,e=t.$createElement;return(t._self._c||e)("card",t._b({attrs:{objectContext:t.objectContext}},"card",Object.assign({},t.$attrs,t.$props),!1))};a._withStripped=!0;const r={name:"ProductDetailCardBeecraftDisplay",inject:["useInternalNode"],components:{Card:n(4773).default},props:{img:{type:String,default:"1"},tag:{type:String,default:"1"},price:{type:String,default:"1"},attr:{type:String,default:"0"},stock:{type:String,default:"1"}},data:function(){return{}},methods:{mockData:function(){}},mounted:function(){this.mockData()}};const c=(0,n(1900).Z)(r,a,[],!1,null,null,null).exports}}]);