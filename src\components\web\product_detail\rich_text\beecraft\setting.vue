<template>
  <div class="dht-product-detail-main-setting" >
      <div class="dht-name dht-title">
        {{ $t('dht.component.web.product_detail_rich_text.title', '图文详情') }}
      </div>
  </div>
</template>
<script>
// import { dhtBizModel } from '../../../utils/model';

export default {
  inject: ['useInternalNode'],
  components: {        
  },
  data() {
      return {   
        name: '',
        
      };
  },
  computed: {  
  },
  methods: {  
    init() {
      const { name} = this.useInternalNode(node => {
            return node.data;
      });
    },  
         
    updateProps(key, val) {
      const { actions } = this.useInternalNode();
      actions.setCustom((data) => {
        data[key] = val;
      });
      this.$set(this, key, val);
    },          
  },
  created() {
      this.init();

  },
  mounted () {
  },
};
</script>
<style lang="less">
.dht-product-detail-rich-text-setting {
  display: flex;
  flex-direction: column;
  width: 100%;   

  .dht-name {
    font-size: 16px;
  } 

  .dht-title {
      color: var(--color-neutrals15, #545861);
      font-size: 12px;
      font-weight: 700;
      margin-top: 12px;
      margin-bottom: 8px;
  }
}
</style>
