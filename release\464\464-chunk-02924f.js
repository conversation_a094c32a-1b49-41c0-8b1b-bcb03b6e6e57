"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[464],{9464:(t,e,i)=>{i.r(e),i.d(e,{default:()=>d});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dht-product-detail-main-setting"},[i("div",{staticClass:"dht-name dht-title dht-title-card",on:{click:t.toggleShow}},[i("i",{class:t.show?"el-icon-caret-bottom":"el-icon-caret-right"}),t._v("\n      "+t._s(t.$t("dht.component.web.product_detail_card"))+"\n    ")]),i("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"show"}],staticClass:"dht-content"},[i("div",{staticClass:"dht-section"},[i("div",{staticClass:"dht-title"},[t._v(t._s(t.$t("dht.component.web.product_detail_card.img")))]),i("div",{staticClass:"dht-radio-group dht-row-radios"},[i("fx-radio",{attrs:{value:t.img,label:"1"},on:{change:function(e){return t.imgChange("1")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.img.display","显示")))]),i("fx-radio",{attrs:{value:t.img,label:"0"},on:{change:function(e){return t.imgChange("0")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.img.hidden","隐藏")))])],1)]),i("div",{staticClass:"dht-section"},[i("div",{staticClass:"dht-title"},[t._v(t._s(t.$t("dht.component.web.product_detail_card.tag")))]),i("div",{staticClass:"dht-radio-group dht-row-radios"},[i("fx-radio",{attrs:{value:t.tag,label:"1"},on:{change:function(e){return t.tagChange("1")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.tag.display","显示")))]),i("fx-radio",{attrs:{value:t.tag,label:"0"},on:{change:function(e){return t.tagChange("0")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.tag.hidden","隐藏")))])],1)]),i("div",{staticClass:"dht-section"},[i("div",{staticClass:"dht-title"},[t._v("\n              "+t._s(t.$t("dht.component.web.product_detail_card.stock"))+"\n              "),i("fx-link",{attrs:{href:"",target:"_blank",title:t.$t("dht.component.web.product_detail_card.stock.tips")}})],1),i("div",{staticClass:"dht-radio-group dht-row-radios"},[i("fx-radio",{attrs:{value:t.stock,label:"1"},on:{change:function(e){return t.stockChange("1")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.stock.display","显示")))]),i("fx-radio",{attrs:{value:t.stock,label:"0"},on:{change:function(e){return t.stockChange("0")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.stock.hidden","隐藏")))])],1)])])])};n._withStripped=!0;var a=i(1141),o=["life_status_before_invalid","life_status","_id"];const s={name:"WebProductDetailCardSetting",inject:["useInternalNode"],components:{},data:function(){return{show:!0,img:"1",tag:"1",price:"1",attr:"0",stock:"1",moreFieldsOptions:[],objectContext:{}}},computed:{spuMode:function(){return a.Z.isSpuMode()},mainObjApiName:function(){return a.Z.isSpuMode()?"SPUObj":"ProductObj"}},methods:{init:function(){var t=this.useInternalNode((function(t){return t.data})),e=t.img,i=t.tag,n=t.price,a=t.attr,o=t.stock;t.objectContext;this.img=e,this.tag=i,this.price=n,this.attr=a+"",this.stock=o},formatFields:function(){var t,e,i=[],n=null===(t=this.objectContext)||void 0===t||null===(e=t.describe)||void 0===e?void 0:e.fields,a={};for(var s in n)n.hasOwnProperty(s)&&"system"!==(a=n[s]).define_type&&o.indexOf(s)<0&&["object_reference","text","currency","number","date","datetime","email","phone","count"].indexOf(a.type)>=0&&i.push({value:s,key:s,label:a.label_r||a.label});return this.moreFieldsOptions=i,i},toggleShow:function(){this.show=!this.show},imgChange:function(t){this.updateProps("img",t)},tagChange:function(t){this.updateProps("tag",t)},priceChange:function(t){this.updateProps("price",t)},bomStyleChange:function(t){this.updateProps("attr",t)},stockChange:function(t){this.updateProps("stock",t)},moreFieldsChange:function(t,e,i){},updateProps:function(t,e){this.useInternalNode().actions.setCustom((function(i){i[t]=e})),this.$set(this,t,e)}},created:function(){this.init()},mounted:function(){}};const d=(0,i(1900).Z)(s,n,[],!1,null,null,null).exports}}]);