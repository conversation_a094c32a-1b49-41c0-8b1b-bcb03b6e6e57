<template>
  <div class="dht-product-detail">
    <div id="productDetail"></div>
  </div>
</template>

<script>

export default {
  components: {
  },
  data() {
    return {
      plugins: []
    }
  },
  created() {
    this.$$ = {}  
        
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      seajs.use('vcrm/sdk', function(Sdk) {
        this.productDetail = Sdk.widgetService.getWidgetApp('goodDetail', {
          $el: '#productDetail',
          propsData: {
            isSpuMode: false,
            isUseByModal: false,
            product: {
              spu_id: "",
              _id: "65fd4703ca4c5d0007ceeec0"
            }
          }
        });
      });
    },
  }

};
</script>

<style lang="less">
  .dhtbiz-list {
      
      .desc {
          padding-top: 30px;
          font-size: 22px;
      }
  }
</style>
