import nodeHelper from '../../utils/nodeHelper.js';

export default {
  hooks: {
    rendered(node, { query, actions }) {
      console.log('top_shopping_cart running hooks rendered', node, query, actions);      
      // 获取容器组件实例
      // const containerInstance = nodeHelper.getContainerInstanceByName(query, 'dht_web_container_product_list');
      // 获取当前组件实例
      const vm = query.instance(node.id);
      const cartService = $dht?.getService('cart');

      if(cartService && vm) {
        // 首次初始化
        const list = cartService.store.getCartList();
        if(list && list.length > 0) {
          vm.$set(vm, 'cartCount', list.length);
        }

        cartService.store.on('store:cart.list.updateNumber', () => {
          const list = cartService.store.getCartList();
          vm.$set(vm, 'cartCount', list.length);
        });
      }
    }
  }    
}