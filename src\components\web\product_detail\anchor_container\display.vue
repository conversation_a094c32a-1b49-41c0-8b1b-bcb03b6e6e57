<template>
  <div class="dht-product-detail-swiper-display">
    <fx-carousel :autoplay="false">
      <fx-carousel-item v-for="(item, index) in products" :key="index">
        <img :src="item.img" class="dht-image">
      </fx-carousel-item>
    </fx-carousel>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        products: [
          {img: 'https://a9.fspage.com/FSR/weex/avatar/object_list/images/list_default_icon.png'},
          {img: 'https://a9.fspage.com/FSR/weex/avatar/object_list/images/list_default_icon.png'},
          {img: 'https://a9.fspage.com/FSR/weex/avatar/object_list/images/list_default_icon.png'},
        ]
      }
    },    
  }
</script>

<style lang="less" scoped>
// @import '~less/class.less';
.dht-product-detail-swiper-display {
  width: 100%;
  height: 340px;
  .dht-image {
    width: 100%;
    height: 340px;
  }
}

</style>