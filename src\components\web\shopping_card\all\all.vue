<template>
  <div class="dhtbiz-shopping-cart" style="width: 100%; height: 700px;">
    <ShoppingCartObj />
  </div>
</template>

<script>

import {loadCss, loadFxModule, loadnsail  } from '@/share/utils/load';

// 依赖crm 中css样式的加载
// crm-dist/assets/style/page-4333aa43ad.css
const cssModule = loadCss('crm-assets/style/page.css');

export default {
  name: 'dht_web_shopping_cart_all',
  props: {

  },
  components: {
    ShoppingCartObj: () => {
      return new Promise((resolve) => {
        Promise.all([loadnsail(), cssModule]).then(async ([sail, css]) => {
          dht.api('ShoppingCartObj')().then(res => {
            resolve(res);
          });
          // dht.api('QuicklyOrder')().then(res => {
          //   resolve(res);
          // });
        });
      });
    },
  },
  methods: {
  },
  created() {
    // this.initTable();
  },
};
</script>

<style lang="less" scoped>
.dht-shopping-cart {
  width: 100%;
  height: 100%;
  overflow: hidden;
  /deep/ .crm-table {
    .dt-term-batch {
      z-index: 0;
    }
    .dt-caption {
      z-index: 10;
    }
  }
}
</style>
