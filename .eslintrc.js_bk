module.exports = {
    root: true,

    // 环境定义
    // 预定义的全局变量
    env: {
        node: true,
        browser: true,
        es2021: true,
        jquery: true,
        amd: true,
    },

    //全局变量
    globals: {
        FS: 'readonly',
        Fx: 'readonly',
        Vue: 'readonly',
        CRM: 'readonly',
        FxUI: 'readonly',
        $t: 'readonly',
        seajs: 'readonly',
        Backbone: 'readonly',
        Plug: 'readonly',
        Cmpt: 'readonly',
        _: 'readonly',
    },

    //
    // 规则继承
    extends: [
        // "standard",
        'eslint:recommended', // eslint 核心规则
        'plugin:vue/essential', // vue规则
        'plugin:json/recommended', // json
        'prettier',
    ],

    //
    // 可按类型执行插件
    // TODO细分@babel/eslint
    // TODO细分@typescript
    overrides: [],

    //
    // 解析参数
    // "parser": "esprima",
    parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        ecmaFeatures: {
            jsx: true,
        },
    },

    //
    // 插件
    plugins: ['json', 'vue'],

    // 规则
    rules: {
        'vue/no-parsing-error': 'off',
        'vue/multi-word-component-names': 'off',
        'json/*': ['error', { allowComments: true }],
        'no-console': 'off',
        'no-await-in-loop': 'error',
        'no-alert': 'error',
        'consistent-return': 'warn',
        camelcase: 'off',
        'max-len': [
            'error',
            {
                code: 120, // 每行最大长度
                tabWidth: 2,
                comments: 100,
                ignoreUrls: true,
                ignoreComments: true,
                ignoreTemplateLiterals: true,
                ignoreRegExpLiterals: true,
            },
        ],
        'max-lines': [
            'error',
            {
                max: 500,
                skipBlankLines: true,
                skipComments: true,
            },
        ],
        'max-nested-callbacks': ['error', 3],
        'max-lines-per-function': [
            'error',
            {
                max: 40,
                skipComments: true,
                skipBlankLines: true,
            },
        ],
        'max-params': ['error', 3],
        'new-cap': 'error',
        'no-multi-assign': 'error',
        quotes: ['warn', 'single'],
        eqeqeq: ['error', 'always', { null: 'ignore' }],
    },
};
