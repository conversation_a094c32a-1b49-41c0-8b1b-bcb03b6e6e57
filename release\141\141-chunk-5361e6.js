(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[141],{1141:(t,e,r)=>{"use strict";r.d(e,{Z:()=>m});var n=r(5861),o=r(5671),i=r(3144),u=r(4687),a=r.n(u),c=r(4942);CRM.util.sendLog,FS.util.crmLog,r(2982);function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}var l=function(t){return Object.keys(t).reduce((function(e,r){return e[t[r]]=r,e}),function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach((function(e){(0,c.Z)(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},t))};l({group:1,menu:2,widgetCollection:3,widget:4}),l({role:4,outer:5,group:2,member:1,usergroup:3,outerUids:6,outerTenantIds:7,outerTgroupIds:8});CRM.util;var f=Object.prototype.toString,h=(location.hash.includes("business_customization/appsetting/tmp"),function(t,e){for(var r in e)t[r]||(t[r]=e[r]);return t});FS.util.setls,FS.util.getls,FS.contacts,FS.PAAS_APPCUSTOMIZATION_MODULE.ASSETS_PATH,FS.PAAS_APPCUSTOMIZATION_MODULE.ASSETS_PATH,FS.PAAS_APPCUSTOMIZATION_MODULE.ASSETS_PATH;var p=function(t,e){var r=JSON.parse(JSON.stringify(t));if(e)for(var n in t)r[n]?"[object Object]"===f.call(r[n])&&h(r[n],t[n]):r[n]=t[n];return r};var d=CRM.util;function y(t){return!(!t.Error||"s211030015"!=t.Error.Code)&&(setTimeout((function(){d.alert(t.Error.Message,(function(){location.reload()}))}),300),!0)}function v(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,n=new Promise((function(n,o){var i=t.success,u=t.error;r=d.FHHApi(Object.assign(t,{success:function(t){y(t)||(0!==t.Result.StatusCode||t.Error?(u&&u(t),o(t)):(i&&i(t.Value),n(t.Value)))},error:function(t){u&&u(t),o(t)},fail:function(t){u&&u(t),o(t)}}),e)}));return n.abort=function(){r&&r.abort&&r.abort()},n}var g=CRM.util,m=new(function(){return(0,i.Z)((function t(){(0,o.Z)(this,t),this.config=null,this.objFields={},this.configPromise=null,this.objFieldsPromise=null}),[{key:"getObjFields",value:function(){return this.objFields}},{key:"setObjFields",value:function(t){this.objFields=t}},{key:"init",value:function(){this.config=null,this.configPromise=null,this.getDhtConfig()}},{key:"detailInit",value:function(){this.objFields={},this.objFieldsPromise=null,this.fetchObjFields()}},{key:"fetchObjFields",value:(e=(0,n.Z)(a().mark((function t(e){var r=this;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.objFieldsPromise||(this.objFieldsPromise=new Promise((function(t,n){v({url:"/EM1HDHT/API/v1/object/dht_describe/service/get_simple_describe",data:{objectApiName:e}}).then((function(e){var n=p(e.objectDescribe.fields);r.objFields=n,t(n)})).catch(n)}))),t.abrupt("return",this.objFieldsPromise);case 2:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"getDhtConfig",value:(t=(0,n.Z)(a().mark((function t(){var e,r=this;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this.config?this.configPromise=Promise.resolve(this.config):(e=["dht_product_list_mode","dht_order_list_display_picture","dht_minimum_order_quantity","dht_order_supports_select_price_list","is_open_dht_product_list_tab","dht_product_list_tab_data","multi_unit_show_type","multi_unit_linkage","multiple_unit","category_model_type","shop_category_model_type","enable_shop_category_model_type","has_first_category_picture","shopping_mall_mode","spu"],this.configPromise=Promise.all([this.getConfigValues(e)]).then((function(t){var e=_.extend({},t[0]);return r.config=e,e}))),t.abrupt("return",this.configPromise);case 2:case"end":return t.stop()}}),t,this)}))),function(){return t.apply(this,arguments)})},{key:"isSpuMode",value:function(){return CRM._cache.productOpenSpu}},{key:"mainObjApiName",value:function(){return this.isSpuMode()?"SPUObj":"ProductObj"}},{key:"getUnitTypeConfig",value:function(){return new Promise((function(t,e){g.FHHApi({url:"/EM1HNCRM/API/v1/object/lazyLoadOptions/service/getUnitTypeConfig",data:{},success:function(r){0===r.Result.StatusCode&&r.Value?t(r.Value.unitTypeList):(g.error(r.Result.FailureMessage),e(r))}})}))}},{key:"getConfigValues",value:function(t){var e={online_pay_switch:"2",offline_pay_switch:"2",multi_spec_order_type:"1",dht_shopping_cart_custom_field_config:""};return new Promise((function(r,n){g.FHHApi({url:"/EM1HDHT/API/v1/object/dht_config/service/get_config_values",data:{isAllConfig:!1,keys:t},success:function(o){if(0===o.Result.StatusCode){var i={},u={},a=o.Value.values;return _.each(a,(function(t){i[t.key]=t.value})),_.each(t,(function(t){u[t]=_.has(i,t)?i[t]:e[t]})),void r(u)}g.error(o.Result.FailureMessage),n(o)}})}))}},{key:"setConfigValues",value:function(t){return new Promise((function(e,r){g.FHHApi({url:"/EM1HDHT/API/v1/object/dht_config/service/set_config_values",data:{ConfigInfoList:t},success:function(t){0!==t.Result.StatusCode?(g.error(t.Result.FailureMessage||$t("设置失败请稍后再试")),r(t)):e(t.Value)}})}))}},{key:"getCRMConfigValues",value:function(t){var e={};return new Promise((function(r,n){g.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_values",data:{isAllConfig:!1,keys:t},success:function(o){if(0===o.Result.StatusCode){var i={},u={},a=o.Value.values;return _.each(a,(function(t){i[t.key]=t.value})),_.each(t,(function(t){u[t]=_.has(i,t)?i[t]:e[t]})),void r(u)}g.error(o.Result.FailureMessage),n(o)}})}))}},{key:"setCRMConfigValues",value:function(t){return new Promise((function(e,r){g.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:t,success:function(t){0!==t.Result.StatusCode?(g.error(t.Result.FailureMessage||$t("设置失败请稍后再试")),r(t)):e(t.Value)}})}))}}]);var t,e}())},7061:(t,e,r)=>{var n=r(8698).default;function o(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t.exports=o=function(){return r},t.exports.__esModule=!0,t.exports.default=t.exports;var e,r={},i=Object.prototype,u=i.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},c="function"==typeof Symbol?Symbol:{},s=c.iterator||"@@iterator",l=c.asyncIterator||"@@asyncIterator",f=c.toStringTag||"@@toStringTag";function h(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{h({},"")}catch(e){h=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var o=e&&e.prototype instanceof _?e:_,i=Object.create(o.prototype),u=new M(n||[]);return a(i,"_invoke",{value:F(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}r.wrap=p;var y="suspendedStart",v="suspendedYield",g="executing",m="completed",b={};function _(){}function w(){}function j(){}var P={};h(P,s,(function(){return this}));var O=Object.getPrototypeOf,S=O&&O(O(T([])));S&&S!==i&&u.call(S,s)&&(P=S);var x=j.prototype=_.prototype=Object.create(P);function E(t){["next","throw","return"].forEach((function(e){h(t,e,(function(t){return this._invoke(e,t)}))}))}function A(t,e){function r(o,i,a,c){var s=d(t[o],t,i);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==n(f)&&u.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,c)}),(function(t){r("throw",t,a,c)})):e.resolve(f).then((function(t){l.value=t,a(l)}),(function(t){return r("throw",t,a,c)}))}c(s.arg)}var o;a(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,o){r(t,n,e,o)}))}return o=o?o.then(i,i):i()}})}function F(t,r,n){var o=y;return function(i,u){if(o===g)throw Error("Generator is already running");if(o===m){if("throw"===i)throw u;return{value:e,done:!0}}for(n.method=i,n.arg=u;;){var a=n.delegate;if(a){var c=C(a,n);if(c){if(c===b)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===y)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=g;var s=d(t,r,n);if("normal"===s.type){if(o=n.done?m:v,s.arg===b)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=m,n.method="throw",n.arg=s.arg)}}}function C(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,C(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),b;var i=d(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,b;var u=i.arg;return u?u.done?(r[t.resultName]=u.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,b):u:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,b)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function T(t){if(t||""===t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(u.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(n(t)+" is not iterable")}return w.prototype=j,a(x,"constructor",{value:j,configurable:!0}),a(j,"constructor",{value:w,configurable:!0}),w.displayName=h(j,f,"GeneratorFunction"),r.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,j):(t.__proto__=j,h(t,f,"GeneratorFunction")),t.prototype=Object.create(x),t},r.awrap=function(t){return{__await:t}},E(A.prototype),h(A.prototype,l,(function(){return this})),r.AsyncIterator=A,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var u=new A(p(t,e,n,o),i);return r.isGeneratorFunction(e)?u:u.next().then((function(t){return t.done?t.value:u.next()}))},E(x),h(x,f,"Generator"),h(x,s,(function(){return this})),h(x,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=T,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(k),!t)for(var r in this)"t"===r.charAt(0)&&u.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,o){return a.type="throw",a.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=u.call(i,"catchLoc"),s=u.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&u.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,b):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),k(r),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;k(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:T(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),b}},r}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports},8698:t=>{function e(r){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(r)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},4687:(t,e,r)=>{var n=r(7061)();t.exports=n;try{regeneratorRuntime=n}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},5861:(t,e,r)=>{"use strict";function n(t,e,r,n,o,i,u){try{var a=t[i](u),c=a.value}catch(t){return void r(t)}a.done?e(c):Promise.resolve(c).then(n,o)}function o(t){return function(){var e=this,r=arguments;return new Promise((function(o,i){var u=t.apply(e,r);function a(t){n(u,o,i,a,c,"next",t)}function c(t){n(u,o,i,a,c,"throw",t)}a(void 0)}))}}r.d(e,{Z:()=>o})},5671:(t,e,r)=>{"use strict";function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}r.d(e,{Z:()=>n})},3144:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});var n=r(2881);function o(t,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,(0,n.Z)(o.key),o)}}function i(t,e,r){return e&&o(t.prototype,e),r&&o(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}},2982:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});var n=r(907);var o=r(181);function i(t){return function(t){if(Array.isArray(t))return(0,n.Z)(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||(0,o.Z)(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}}}]);