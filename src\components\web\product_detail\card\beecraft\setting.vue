<template>
  <div class="dht-product-detail-main-setting" >
      <div class="dht-name dht-title dht-title-card"
        @click="toggleShow"
      >
        <!-- 向下剪头 -->
        <i :class="show ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i>
        {{$t('dht.component.web.product_detail_card')}}
      </div>  

      <div class="dht-content" v-show="show">
        <!-- 主图 -->
        <div class="dht-section">
              <div class="dht-title">{{ $t('dht.component.web.product_detail_card.img') }}</div>
            <div class="dht-radio-group dht-row-radios">
                <fx-radio :value="img" label='1' @change="imgChange('1')">{{ $t('dht.component.web.product_detail_card.img.display', '显示') }}</fx-radio>
                <fx-radio :value="img" label='0' @change="imgChange('0')">{{ $t('dht.component.web.product_detail_card.img.hidden', '隐藏') }}</fx-radio>
            </div>
        </div>

        <!-- 标签 -->
        <div class="dht-section">
            <div class="dht-title">{{ $t('dht.component.web.product_detail_card.tag') }}</div>
            <div class="dht-radio-group dht-row-radios">
                <fx-radio :value="tag" label='1' @change="tagChange('1')">{{ $t('dht.component.web.product_detail_card.tag.display', '显示') }}</fx-radio>
                <fx-radio :value="tag" label='0' @change="tagChange('0')">{{ $t('dht.component.web.product_detail_card.tag.hidden', '隐藏') }}</fx-radio>
            </div>
        </div>

        <!-- 商品价格 -->
        <!-- <div v-if="!spuMode" class="dht-section">
              <div class="dht-title">{{ $t('dht.component.web.product_detail_card.price') }}</div>
              <div class="dht-radio-group">
                  
                  <fx-radio :value="price" label='0' @change="priceChange('0')">{{ $t('dht.component.web.product_detail_card.price.interval', '显示区间价格') }}</fx-radio>
                 
                  <fx-radio :value="price" label='1' @change="priceChange('1')">{{ $t('dht.component.web.product_detail_card.price.lowest', '显示最低价') }}</fx-radio>
              </div>
        </div> -->

        <!-- 选择规格样式 
        <div v-if="!spuMode" class="dht-section">
              <div class="dht-title">{{ $t('dht.component.web.product_detail_card.bom_style') }}</div>
              <div class="dht-radio-group">
                  <fx-radio :value="attr" label='0' @change="bomStyleChange('0')">{{ $t('dht.component.web.product_detail_card.bom_style.single', '单选') }}</fx-radio>
                  <fx-radio :value="attr" label='1' @change="bomStyleChange('1')">{{ $t('dht.component.web.product_detail_card.bom_style.single_detail', '单选并展示产品明细行') }}</fx-radio>
                  <fx-radio :value="attr" label='2' @change="bomStyleChange('2')">{{ $t('dht.component.web.product_detail_card.bom_style.multiple_detail', '多选并展示产品明细行')  }}</fx-radio>
              </div>
        </div>-->

        <!-- 库存 -->
        <div class="dht-section">
              <div class="dht-title">
                {{ $t('dht.component.web.product_detail_card.stock') }}
                <!-- 去库存管理配置 -->
                <fx-link                
                  href=""
                  target="_blank"
                  :title="$t('dht.component.web.product_detail_card.stock.tips')"
                ></fx-link>
              </div>
              <div class="dht-radio-group dht-row-radios">
                  <fx-radio :value="stock" label='1' @change="stockChange('1')">{{ $t('dht.component.web.product_detail_card.stock.display', '显示') }}</fx-radio>
                  <fx-radio :value="stock" label='0' @change="stockChange('0')">{{ $t('dht.component.web.product_detail_card.stock.hidden', '隐藏') }}</fx-radio>
              </div>
        </div>

        <!-- 更多字段 
        <div class="dht-section">
            <div class="dht-title">{{ $t('dht.component.web.product_detail_card.more_fields') }}</div>
            <fx-transfer
              filterable            
              v-model="moreFields"
              :data="moreFieldsOptions"
              draggable
              target-order0="push"
              @change="moreFieldsChange"
            >
            </fx-transfer>                       
        </div>-->

      <!-- end of  dht-content -->
      </div>
  </div>
</template>
<script>
/* 多语如下 */
// dht.component.web.product_detail_card: 商品卡片
// dht.component.web.product_detail_card.img: 主图
// dht.component.web.product_detail_card.img.display: 显示
// dht.component.web.product_detail_card.img.hidden: 隐藏
// dht.component.web.product_detail_card.tag: 标签
// dht.component.web.product_detail_card.tag.display: 显示
// dht.component.web.product_detail_card.tag.hidden: 隐藏
// dht.component.web.product_detail_card.price: 商品价格
// dht.component.web.product_detail_card.price.interval: 显示区间价格
// dht.component.web.product_detail_card.price.lowest: 显示最低价 
// dht.component.web.product_detail_card.bom_style: 选择规格样式
// dht.component.web.product_detail_card.bom_style.single: 单选
// dht.component.web.product_detail_card.bom_style.single_detail: 单选并展示产品明细行
// dht.component.web.product_detail_card.bom_style.multiple_detail: 多选并展示产品明细行
// dht.component.web.product_detail_card.stock: 库存
// dht.component.web.product_detail_card.stock.display: 显示
// dht.component.web.product_detail_card.stock.hidden: 隐藏
// dht.component.web.product_detail_card.more_fields: 更多字段

/* 
todo not finish namefield.relation.tips 对应的 href需要修改
*/

import { dhtBizModel } from '../../../utils/model';

// 排除可选的字段apiName 作废前生命状态 生命状态
const blackFields = ['life_status_before_invalid', 'life_status', '_id'];

export default {
  name: 'WebProductDetailCardSetting',
  inject: ['useInternalNode'],
  components: {        
  },
  data() {
      return {
        show: true,
        img: '1',
        tag: '1',
        price: '1',
        attr: '0',
        stock: '1',
        moreFieldsOptions: [],
        objectContext: {}
      };
  },
  computed: {
    // moreFields() {
    //   return this.$attrs.props.more_fields || [];
    // },
    // 可选的更多字段
    // moreFieldsOptions() {
    //   let result = [];
    //   let obj = this.fields;
    //   let item = {}
    //   for (let key in obj) {        
    //     if (obj.hasOwnProperty(key)) {
    //       item = obj[key];
    //       if(item.define_type !== 'system' &&  //系统字段
    //         blackFields.indexOf(key) < 0 &&    // 黑名单字段
    //         (['object_reference', 'text', 'currency', 'number', 'date', 'datetime', 'email', 'phone', 'count'].indexOf(item.type) >= 0) ) { // 文本类型字段
    //           result.push({
    //             value: key,
    //             key: key,
    //             label: item.label_r || item.label
    //           })
    //         }          
    //     }
    //   }
    //   return result;
    // },
    spuMode() {
      return dhtBizModel.isSpuMode();
    },
    mainObjApiName() {
      return dhtBizModel.isSpuMode() ? 'SPUObj' : 'ProductObj';
    },
  },
  methods: {
    init() {
      const { img, tag, price, attr, stock, objectContext } = this.useInternalNode(node => {
            return node.data;
      });
      this.img = img;
      this.tag = tag;
      this.price = price;
      this.attr = attr + '';
      this.stock = stock;
      // this.objectContext = objectContext;
      // this.formatFields();
    },     
    formatFields() {
      let result = [];
      let obj = this.objectContext?.describe?.fields;
      let item = {}
      for (let key in obj) {        
        if (obj.hasOwnProperty(key)) {
          item = obj[key];
          if(item.define_type !== 'system' &&  //系统字段
            blackFields.indexOf(key) < 0 &&    // 黑名单字段
            (['object_reference', 'text', 'currency', 'number', 'date', 'datetime', 'email', 'phone', 'count'].indexOf(item.type) >= 0) ) { // 文本类型字段
              result.push({
                value: key,
                key: key,
                label: item.label_r || item.label
              })
            }          
        }
      }
      this.moreFieldsOptions = result;
      return result;
    },
    toggleShow() {
      this.show = !this.show;
    },
    imgChange(val) {
      this.updateProps('img', val);
    },  
    tagChange(val) {
      this.updateProps('tag', val);
    },
    priceChange(val) {
      this.updateProps('price', val);
    },
    bomStyleChange(val) {
      this.updateProps('attr', val);
    },
    stockChange(val) {
      this.updateProps('stock', val);
    },
    // 更多字段改变
    moreFieldsChange(val, direction, movedKeys) {
      console.log('moreFieldsChange', val, direction, movedKeys);
    },       
    updateProps(key, val) {
      const { actions } = this.useInternalNode();

      actions.setCustom((data) => {
        data[key] = val;
      });
      this.$set(this, key, val);
    },
  },
  created() {
    this.init();      
  },
  mounted () {
  },
};
</script>
<style lang="less">
.dht-product-detail-main-setting {
  display: flex;
  flex-direction: column;
  width: 100%;  
  
  .dht-title-card {
    margin-top: -12px;
  }
}
</style>
