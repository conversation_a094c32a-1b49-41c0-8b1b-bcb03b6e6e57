export const data = {
    "commodityLabels": [
    {
        value: 'option1',
        label: '推荐111',
        font_color: '#624027'
    }, {
        value: 'option1',
        label: '新品',
        font_color: '#E0975E'
    },{
        value: 'option3',
        label: '热销',
        font_color: '#E0975E'
    }], 
  "lock_rule": null,
  "price_book_product_id__r": "PBProdCode20200403000003",
  "batch_sn": "3",
  "price_book_product_id": "5e869924f71c7b0001edb324679488",
  "field_w1026__c": null,
  "is_multiple_unit": false,
  "virtual_price_book_selling_price": 2,
  "product_category_id": "6576b22080e8a00001e6ec6d",
  "shop_category_id": null,
  "commodity_label": null,
  "extend_obj_data_id": "63857dda967c1c0001e309c5",
  "off_shelves_time": null,
  "created_by__r": {
      "picAddr": "N_202409_05_d25f7c232a644d2aa968e00a4ed4d6fb",
      "mobile": null,
      "description": "",
      "dept": "1000",
      "supervisorId": "1000",
      "title": null,
      "empNum": "",
      "modifyTime": 1725526996579,
      "post": "",
      "createTime": 1576121491800,
      "phone": "",
      "name": "guanrs",
      "nickname": "guanrs",
      "tenantId": "679488",
      "id": "1001",
      "position": null,
      "enterpriseName": null,
      "email": "",
      "status": 0
  },
  "product_category_id__c": null,
  "price": "2.00",
  "life_status_before_invalid": null,
  "owner_department_id": "1000",
  "total_num": 1,
  "mnemonic_code": null,
  "owner_department": "test",
  "model": null,
  "virtual_available_stock": "-1.0000块",
  "virtual_price_book_price": 2,
  "searchAfterId": [
      "1730103937797",
      "1730103937826615",
      "5e869924f71c7b0001edb324"
  ],
  "barcode": null,
  "maintenance_period": null,
  "price_book_id__r": "标准价目表",
  "lock_status": "0",
  "package": "CRM",
  "reg_cert_id": null,
  "is_giveaway": "0",
  "create_time": 1585879332185,
  "belong_to_supplier": null,
  "product_category_id__r": "最小起订",
  "minimum_order_quantity": "0",
  "order_field": "99",
  "purchase_unit_price": null,
  "version": "11",
  "created_by": [
      "1001"
  ],
  "relevant_team": [
      {
          "teamMemberEmployee": [
              "1001"
          ],
          "teamMemberType": "0",
          "teamMemberRole": "1",
          "teamMemberPermissionType": "2",
          "teamMemberDeptCascade": "0"
      }
  ],
  "field_cyY7r__c": null,
  "product_line": null,
  "unit": "2",
  "data_own_department": null,
  "price_book_id": "5da8ab23657abf0001718739",
  "name": "序列号产品",
  "description__o": null,
  "_id": "5e869924f71c7b0001edb324",
  "virtual_base_price_book_price": 2,
  "tenant_id": "679488",
  "field_x25an__c": null,
  "mc_currency__r": "¥",
  "product_status": "1",
  "shop_category_id__c": null,
  "description": null,
  "remark": null,
  "replacement_period": null,
  "product_code": null,
  "origin_source": null,
  "price_book_discount": 100,
  "virtual_base_price_book_price__n": "¥2.00",
  "lock_user": null,
  "on_shelves_time": 1585879332165,
  "field_M6Ez2__c": null,
  "safety_stock": null,
  "virtual_product_price": 2,
  "is_deleted": false,
  "object_describe_api_name": "ProductObj",
  "owner__l": [
      {
          "id": "1001",
          "tenantId": "679488",
          "name": "guanrs",
          "picAddr": "N_202409_05_d25f7c232a644d2aa968e00a4ed4d6fb",
          "email": "",
          "nickname": "guanrs",
          "supervisorId": "1000",
          "phone": "",
          "description": "",
          "status": 0,
          "createTime": 1576121491800,
          "modifyTime": 1725526996579,
          "dept": "1000",
          "post": "",
          "empNum": ""
      }
  ],
  "out_owner": null,
  "relevant_team__r": "guanrs",
  "mc_functional_currency__r": "¥",
  "field_pbiil__c": "mini/0",
  "owner__r": {
      "picAddr": "N_202409_05_d25f7c232a644d2aa968e00a4ed4d6fb",
      "mobile": null,
      "description": "",
      "dept": "1000",
      "supervisorId": "1000",
      "title": null,
      "empNum": "",
      "modifyTime": 1725526996579,
      "post": "",
      "createTime": 1576121491800,
      "phone": "",
      "name": "guanrs",
      "nickname": "guanrs",
      "tenantId": "679488",
      "id": "1001",
      "position": null,
      "enterpriseName": null,
      "email": "",
      "status": 0
  },
  "virtual_min_order_quantity": null,
  "owner": [
      "1001"
  ],
  "product_category_id__relation_ids": "6576b22080e8a00001e6ec6d",
  "picture_path": [
      {
          "ext": "jpeg",
          "path": "N_202410_28_7e7aae5927ec484ea436c1709b49d0ed",
          "filename": "u=1812823750,4281976966&fm=15&gp=0.jpg",
          "size": 35478,
          "signature": "679488$p50MWnPCF-uiMqjkoGTid16x_lA=$Bjewl9hhhxzmkVCA0obJhjQL",
          "signedUrl": "https://img.fxiaoke.com/FilesOne/?Fid=E71C6566E97D0B8870E05EB428198372B65D4BB196D97E8208F8CF934082B11B6C5D002E7AFBC49786455BAFFBA39669F64374D70A3924535CB7C5113345E49F72F3ACC1894037E2591116DD9FD8DF3DD6174437ADFA57A0E92AC41C75BE91C9FE4F0B195EE96FF0F8FD659F3E98EBA7222548DD2D962225&Acid=679488.1001.200539161.311183300&Ets=1733988635518&Ak=x1jRJt5O1anXnMZZadkSTIc2&Fn=u%3D1812823750%2C4281976966%26fm%3D15%26gp%3D0.jpeg&Sig=COnyl-iUFjKZb7ANIUFWxJ1hxd4=&Ds=xfaEjyEg4iWzQxw2pbTCBg==&traceId=E-E.0.0-14549067"
      }
  ],
  "last_modified_time": 1730103937797,
  "unit__r": "块",
  "life_status": "normal",
  "last_modified_by__l": [
      {
          "id": "1001",
          "tenantId": "679488",
          "name": "guanrs",
          "picAddr": "N_202409_05_d25f7c232a644d2aa968e00a4ed4d6fb",
          "email": "",
          "nickname": "guanrs",
          "supervisorId": "1000",
          "phone": "",
          "description": "",
          "status": 0,
          "createTime": 1576121491800,
          "modifyTime": 1725526996579,
          "dept": "1000",
          "post": "",
          "empNum": ""
      }
  ],
  "virtual_price_book_selling_price__n": "¥2.000",
  "product_video": [],
  "created_by__l": [
      {
          "id": "1001",
          "tenantId": "679488",
          "name": "guanrs",
          "picAddr": "N_202409_05_d25f7c232a644d2aa968e00a4ed4d6fb",
          "email": "",
          "nickname": "guanrs",
          "supervisorId": "1000",
          "phone": "",
          "description": "",
          "status": 0,
          "createTime": 1576121491800,
          "modifyTime": 1725526996579,
          "dept": "1000",
          "post": "",
          "empNum": ""
      }
  ],
  "last_modified_by": [
      "1001"
  ],
  "technical_parameter": null,
  "out_tenant_id": null,
  "record_type": "default__c",
  "last_modified_by__r": {
      "picAddr": "N_202409_05_d25f7c232a644d2aa968e00a4ed4d6fb",
      "mobile": null,
      "description": "",
      "dept": "1000",
      "supervisorId": "1000",
      "title": null,
      "empNum": "",
      "modifyTime": 1725526996579,
      "post": "",
      "createTime": 1576121491800,
      "phone": "",
      "name": "guanrs",
      "nickname": "guanrs",
      "tenantId": "679488",
      "id": "1001",
      "position": null,
      "enterpriseName": null,
      "email": "",
      "status": 0
  },
  "virtual_price_book_price__n": "¥2.00",
  "max_stock": null,
  "product_spec": null,
  "order_by": null,
  "category": "1702277664161"
}

export const describe = {
    "tenant_id": "679488",
    "store_table_name": "biz_product",
    "package": "CRM",
    "is_active": true,
    "last_modified_time": 1733833369172,
    "create_time": 1585793749511,
    "description": "",
    "last_modified_by": "1001",
    "display_name": "产品",
    "version": 232,
    "is_open_display_name": false,
    "index_version": 1,
    "icon_index": 0,
    "is_deleted": false,
    "api_name": "ProductObj",
    "icon_path": "",
    "is_udef": true,
    "define_type": "package",
    "display_name_r": "产品",
    "_id": "5e854ad5f71c7b0001bfdfc2",
    "fields": {
        "lock_rule": {
            "describe_api_name": "ProductObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1547943504169,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "description": "锁定规则",
            "is_unique": false,
            "rules": [],
            "default_value": "default_lock_rule",
            "label": "锁定规则",
            "type": "lock_rule",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "lock_rule",
            "define_type": "package",
            "_id": "5e854ad5f71c7b0001bfdfc3",
            "is_single": false,
            "is_extend": false,
            "label_r": "锁定规则",
            "is_index_field": false,
            "index_name": "s_1",
            "status": "released"
        },
        "batch_sn": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "批次与序列号管理",
            "is_unique": false,
            "disable_after_filter": true,
            "type": "select_one",
            "default_to_zero": false,
            "is_required": true,
            "options": [
                {
                    "label": "不开启",
                    "value": "1"
                },
                {
                    "label": "开启批次管理",
                    "value": "2"
                },
                {
                    "label": "开启序列号管理",
                    "value": "3"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "is_extend": false,
            "label_r": "批次与序列号管理",
            "index_name": "s_11",
            "is_index": true,
            "is_active": true,
            "create_time": 1547905280522,
            "is_encrypted": false,
            "default_value": "1",
            "label": "批次与序列号管理",
            "is_need_convert": false,
            "api_name": "batch_sn",
            "_id": "5e8785205c6c25377d83e62e",
            "is_index_field": false,
            "config": {},
            "help_text": "",
            "status": "released"
        },
        "field_w1026__c": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "input_mode": "",
            "is_single": false,
            "is_extend": true,
            "index_name": "t_15",
            "max_length": 100,
            "is_index": true,
            "is_active": true,
            "create_time": 1664172926844,
            "is_encrypted": false,
            "default_value": "",
            "label": "订货通单行文本",
            "field_num": 3,
            "api_name": "field_w1026__c",
            "_id": "6331437eed427f00017a6d80",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "new"
        },
        "virtual_price_book_selling_price": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "价目表售价",
            "index_name": "t_11",
            "max_length": 500,
            "is_index": false,
            "is_active": true,
            "create_time": 1642793675319,
            "is_encrypted": false,
            "default_value": "",
            "label": "价目表售价",
            "is_abstract": true,
            "is_need_convert": false,
            "api_name": "virtual_price_book_selling_price",
            "_id": "61eb0acb5be252000165a88c",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "is_multiple_unit": {
            "describe_api_name": "ProductObj",
            "is_index": true,
            "is_active": true,
            "create_time": 1675653684249,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "description": "是否多单位",
            "is_unique": false,
            "default_value": false,
            "label": "是否多单位",
            "type": "true_or_false",
            "is_need_convert": false,
            "is_required": true,
            "api_name": "is_multiple_unit",
            "options": [
                {
                    "label": "是",
                    "value": true
                },
                {
                    "label": "否",
                    "value": false
                }
            ],
            "define_type": "package",
            "_id": "63e07234dffd190001c0ccb0",
            "is_single": false,
            "label_r": "是否多单位",
            "is_index_field": false,
            "index_name": "b_1",
            "help_text": "",
            "status": "released"
        },
        "product_category_id": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference",
            "relation_outer_data_privilege": "not_related",
            "related_where_type": "",
            "is_required": true,
            "wheres": [],
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "产品分类",
            "index_name": "s_13",
            "is_index": true,
            "is_active": true,
            "create_time": 1662343876628,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品分类",
            "target_api_name": "ProductCategoryObj",
            "target_related_list_name": "productcategory_productobj_list",
            "target_related_list_label": "产品",
            "action_on_target_delete": "set_null",
            "is_need_convert": false,
            "related_wheres": [],
            "api_name": "product_category_id",
            "_id": "63155ac4efa1700001c1657a",
            "is_index_field": true,
            "help_text": "",
            "status": "released"
        },
        "shop_category_id": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "商城分类",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference_many",
            "is_required": false,
            "wheres": [
                {
                    "connector": "OR",
                    "filters": [
                        {
                            "value_type": 0,
                            "operator": "EQ",
                            "field_name": "record_type",
                            "field_values": [
                                "shop_category__c"
                            ]
                        }
                    ]
                }
            ],
            "define_type": "package",
            "is_single": false,
            "label_r": "商城分类",
            "index_name": "a_0",
            "is_index": true,
            "is_active": true,
            "create_time": 1705080367211,
            "is_encrypted": false,
            "default_value": "",
            "label": "商城分类",
            "target_api_name": "ProductCategoryObj",
            "target_related_list_name": "shop_category_product_list",
            "target_related_list_label": "商城产品",
            "action_on_target_delete": "set_null",
            "is_need_convert": false,
            "api_name": "shop_category_id",
            "_id": "65aa9c1b7e77a70001bfc319",
            "is_index_field": false,
            "help_text": "",
            "status": "new",
            "biz_field_api_name": "shop_category_type"
        },
        "commodity_label": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "标签",
            "is_unique": false,
            "disable_after_filter": true,
            "type": "select_many",
            "default_to_zero": false,
            "is_required": false,
            "options": [
                {
                    "font_color": "#30C776",
                    "label": "新品",
                    "value": "option1"
                },
                {
                    "font_color": "#FF8000",
                    "label": "热销",
                    "value": "option2"
                },
                {
                    "font_color": "#FF8000",
                    "label": "清仓",
                    "value": "option3"
                },
                {
                    "font_color": "#FF8000",
                    "label": "推荐",
                    "value": "option4"
                },
                {
                    "font_color": "#FF8000",
                    "label": "重要",
                    "value": "option5"
                },
                {
                    "label": "其他",
                    "value": "option6"
                },
                {
                    "font_color": "#936de3",
                    "label": "镇店之宝",
                    "value": "e3Fo4Panm"
                },
                {
                    "font_color": "#936de3",
                    "not_usable": true,
                    "label": "严选",
                    "value": "5vwOx9c3v"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "label_r": "标签",
            "index_name": "a_3",
            "is_index": true,
            "is_active": true,
            "create_time": 1724436555792,
            "is_encrypted": false,
            "default_value": [],
            "label": "标签",
            "is_need_convert": false,
            "api_name": "commodity_label",
            "_id": "66c8d04b94b4160001b97663",
            "is_index_field": true,
            "config": {},
            "help_text": "",
            "status": "released"
        },
        "off_shelves_time": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "下架时间",
            "is_unique": false,
            "type": "date_time",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "is_extend": false,
            "label_r": "下架时间",
            "index_name": "l_2",
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "not_use_multitime_zone": false,
            "default_value": "",
            "label": "下架时间",
            "time_zone": "GMT+8",
            "is_need_convert": false,
            "api_name": "off_shelves_time",
            "date_format": "yyyy-MM-dd HH:mm",
            "_id": "5e854ad5f71c7b0001bfdfcc",
            "is_index_field": false,
            "help_text": "",
            "status": "released"
        },
        "extend_obj_data_id": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "extend_obj_data_id",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "system",
            "is_single": false,
            "is_extend": false,
            "label_r": "extend_obj_data_id",
            "index_name": "t_3",
            "max_length": 100,
            "is_index": false,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "default_value": "",
            "label": "extend_obj_data_id",
            "api_name": "extend_obj_data_id",
            "_id": "5e854ad5f71c7b0001bfdfcb",
            "is_index_field": false,
            "help_text": "",
            "status": "released"
        },
        "product_category_id__c": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference_many",
            "is_required": false,
            "wheres": [],
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "a_5",
            "is_index": true,
            "is_active": true,
            "create_time": 1681961794302,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品分类(多选)",
            "target_api_name": "ProductCategoryObj",
            "target_related_list_name": "target_related_list_2aTaj__c",
            "field_num": 6,
            "target_related_list_label": "产品2",
            "action_on_target_delete": "set_null",
            "api_name": "product_category_id__c",
            "_id": "6440b3429e940d0001353e75",
            "is_index_field": false,
            "help_text": "",
            "status": "new"
        },
        "price": {
            "describe_api_name": "ProductObj",
            "default_is_expression": true,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "标准价格",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": true,
            "define_type": "package",
            "is_single": false,
            "label_r": "标准价格",
            "index_name": "d_3",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "length": 12,
            "default_value": "0",
            "label": "标准价格",
            "currency_unit": "￥",
            "is_need_convert": false,
            "currency_type": "oc",
            "api_name": "price",
            "_id": "5f1afaae4c22060001002205",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released"
        },
        "life_status_before_invalid": {
            "describe_api_name": "ProductObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "作废前生命状态",
            "is_unique": false,
            "label": "作废前生命状态",
            "type": "text",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "life_status_before_invalid",
            "define_type": "package",
            "_id": "5e854ad5f71c7b0001bfdfce",
            "is_single": false,
            "is_extend": false,
            "label_r": "作废前生命状态",
            "is_index_field": false,
            "index_name": "t_4",
            "max_length": 256,
            "status": "released"
        },
        "mnemonic_code": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "助记码",
            "is_unique": true,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "助记码",
            "index_name": "t_10",
            "max_length": 100,
            "is_index": true,
            "is_active": true,
            "create_time": 1566266996925,
            "is_encrypted": false,
            "default_value": "",
            "label": "助记码",
            "is_need_convert": false,
            "api_name": "mnemonic_code",
            "_id": "5e854ad5f71c7b0001bfdfcf",
            "is_index_field": true,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "model": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "型号",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "型号",
            "index_name": "t_5",
            "max_length": 256,
            "is_index": false,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "default_value": "",
            "label": "型号",
            "is_need_convert": false,
            "api_name": "model",
            "_id": "5e854ad5f71c7b0001bfdfd0",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "virtual_available_stock": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "可用库存",
            "index_name": "t_13",
            "max_length": 500,
            "is_index": false,
            "is_active": true,
            "create_time": 1642793675319,
            "is_encrypted": false,
            "default_value": "",
            "label": "可用库存",
            "is_abstract": true,
            "is_need_convert": false,
            "api_name": "virtual_available_stock",
            "_id": "61eb0acb5be252000165a88d",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "owner_department": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "负责人主属部门",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "负责人主属部门",
            "index_name": "owner_dept",
            "max_length": 100,
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "default_value": "",
            "label": "负责人主属部门",
            "is_need_convert": false,
            "api_name": "owner_department",
            "_id": "5e854ad5f71c7b0001bfdfd1",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "virtual_price_book_price": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "价目表价格",
            "index_name": "t_12",
            "max_length": 500,
            "is_index": false,
            "is_active": true,
            "create_time": 1642793675318,
            "is_encrypted": false,
            "default_value": "",
            "label": "价目表价格",
            "is_abstract": true,
            "is_need_convert": false,
            "api_name": "virtual_price_book_price",
            "_id": "61eb0acb5be252000165a88b",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "barcode": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "条形码",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "条形码",
            "index_name": "t_6",
            "max_length": 50,
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "default_value": "",
            "label": "条形码",
            "is_need_convert": false,
            "api_name": "barcode",
            "_id": "5e854ad5f71c7b0001bfdfd3",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "maintenance_period": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "保养周期（天）",
            "is_unique": false,
            "type": "number",
            "decimal_places": 0,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "保养周期（天）",
            "index_name": "d_4",
            "max_length": 14,
            "is_index": false,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 14,
            "default_value": "",
            "label": "保养周期（天）",
            "is_need_convert": false,
            "api_name": "maintenance_period",
            "_id": "5e854ad5f71c7b0001bfdfd4",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released"
        },
        "reg_cert_id": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference",
            "relation_outer_data_privilege": "not_related",
            "related_where_type": "",
            "is_required": false,
            "wheres": [],
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "注册证名称",
            "index_name": "s_0",
            "is_index": true,
            "is_active": true,
            "create_time": 1729579721489,
            "is_encrypted": false,
            "default_value": "",
            "label": "注册证名称",
            "target_api_name": "RegistrationCertificateObj",
            "target_related_list_name": "target_related_list_ProductObj_reg_cert_id",
            "target_related_list_label": "产品",
            "action_on_target_delete": "set_null",
            "is_need_convert": false,
            "related_wheres": [],
            "api_name": "reg_cert_id",
            "_id": "67174ac969c6a70001275bde",
            "is_index_field": true,
            "help_text": "",
            "status": "released"
        },
        "lock_status": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "锁定状态",
            "is_unique": false,
            "disable_after_filter": true,
            "type": "select_one",
            "default_to_zero": false,
            "is_required": false,
            "options": [
                {
                    "label": "未锁定",
                    "value": "0"
                },
                {
                    "label": "锁定",
                    "value": "1"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "is_extend": false,
            "label_r": "锁定状态",
            "index_name": "s_4",
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504196,
            "is_encrypted": false,
            "default_value": "0",
            "label": "锁定状态",
            "api_name": "lock_status",
            "_id": "5e854ad5f71c7b0001bfdfd7",
            "is_index_field": false,
            "config": {},
            "help_text": "",
            "status": "new"
        },
        "package": {
            "describe_api_name": "ProductObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1585793749511,
            "pattern": "",
            "is_unique": false,
            "description": "package",
            "label": "package",
            "type": "text",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "package",
            "define_type": "system",
            "index_name": "pkg",
            "max_length": 200,
            "status": "released"
        },
        "belong_to_supplier": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "所属供应商",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference",
            "relation_outer_data_privilege": "not_related",
            "related_where_type": "",
            "is_required": false,
            "wheres": [],
            "define_type": "package",
            "input_mode": "scan",
            "is_single": false,
            "is_extend": false,
            "label_r": "所属供应商",
            "index_name": "s_15",
            "is_index": true,
            "is_active": true,
            "create_time": 1686938027225,
            "is_encrypted": false,
            "default_value": "",
            "label": "所属供应商",
            "target_api_name": "SupplierObj",
            "target_related_list_name": "supplier_product_list",
            "target_related_list_label": "产品",
            "action_on_target_delete": "set_null",
            "is_need_convert": false,
            "related_wheres": [],
            "api_name": "belong_to_supplier",
            "_id": "648ca1ab979c9c00016bb979",
            "is_index_field": true,
            "help_text": "",
            "status": "released"
        },
        "is_giveaway": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "是否赠品",
            "is_unique": false,
            "disable_after_filter": true,
            "type": "select_one",
            "default_to_zero": false,
            "is_required": false,
            "options": [
                {
                    "label": "否",
                    "value": "0"
                },
                {
                    "label": "是",
                    "value": "1"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "is_extend": false,
            "label_r": "是否赠品",
            "index_name": "s_5",
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504201,
            "is_encrypted": false,
            "default_value": "0",
            "label": "是否赠品",
            "api_name": "is_giveaway",
            "_id": "61eaee023a85d30001fb8afb",
            "is_index_field": false,
            "config": {},
            "help_text": "",
            "status": "new"
        },
        "create_time": {
            "describe_api_name": "ProductObj",
            "is_index": true,
            "create_time": 1585793749511,
            "is_unique": false,
            "description": "create_time",
            "label": "创建时间",
            "type": "date_time",
            "time_zone": "",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "create_time",
            "define_type": "system",
            "date_format": "yyyy-MM-dd HH:mm:ss",
            "index_name": "crt_time",
            "status": "released"
        },
        "purchase_unit_price": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "采购单价",
            "is_unique": false,
            "type": "currency",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "采购单价",
            "index_name": "d_10",
            "max_length": 16,
            "is_index": true,
            "is_active": true,
            "create_time": 1610217142372,
            "is_encrypted": false,
            "length": 14,
            "default_value": "",
            "label": "采购单价",
            "currency_unit": "￥",
            "is_need_convert": false,
            "currency_type": "oc",
            "api_name": "purchase_unit_price",
            "_id": "5ff9f6b62b8f6a0001317bfc",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new"
        },
        "minimum_order_quantity": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "number",
            "decimal_places": 0,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "is_extend": false,
            "label_r": "最小起订量",
            "index_name": "d_0",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1701756855342,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 14,
            "default_value": "",
            "label": "最小起订量",
            "is_need_convert": false,
            "api_name": "minimum_order_quantity",
            "_id": "656ebfb753ffc5000144f20d",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released"
        },
        "order_field": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "排序",
            "is_unique": false,
            "type": "number",
            "decimal_places": 0,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "排序",
            "index_name": "t_7",
            "max_length": 128,
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504215,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 128,
            "default_value": "",
            "label": "排序",
            "is_need_convert": false,
            "api_name": "order_field",
            "_id": "5e854ad5f71c7b0001bfdfda",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released"
        },
        "version": {
            "describe_api_name": "ProductObj",
            "is_index": false,
            "create_time": 1585793749511,
            "length": 8,
            "is_unique": false,
            "description": "version",
            "label": "版本",
            "type": "number",
            "decimal_places": 0,
            "is_need_convert": false,
            "is_required": false,
            "api_name": "version",
            "define_type": "system",
            "index_name": "version",
            "round_mode": 4,
            "status": "released"
        },
        "created_by": {
            "describe_api_name": "ProductObj",
            "is_index": true,
            "is_active": true,
            "create_time": 1585793749511,
            "is_unique": false,
            "label": "创建人",
            "type": "employee",
            "is_need_convert": true,
            "is_required": false,
            "api_name": "created_by",
            "define_type": "system",
            "is_single": true,
            "index_name": "crt_by",
            "status": "released",
            "description": ""
        },
        "relevant_team": {
            "describe_api_name": "ProductObj",
            "embedded_fields": {
                "teamMemberEmployee": {
                    "is_index": true,
                    "is_need_convert": true,
                    "is_required": false,
                    "api_name": "teamMemberEmployee",
                    "is_unique": false,
                    "define_type": "package",
                    "description": "成员员工",
                    "label": "成员员工",
                    "type": "employee",
                    "is_single": true,
                    "help_text": "成员员工"
                },
                "teamMemberRole": {
                    "is_index": true,
                    "is_need_convert": false,
                    "is_required": false,
                    "api_name": "teamMemberRole",
                    "options": [
                        {
                            "label": "负责人",
                            "value": "1"
                        },
                        {
                            "label": "普通成员",
                            "value": "4"
                        }
                    ],
                    "is_unique": false,
                    "define_type": "package",
                    "description": "成员角色",
                    "label": "成员角色",
                    "type": "select_one",
                    "help_text": "成员角色"
                },
                "teamMemberPermissionType": {
                    "is_index": true,
                    "is_need_convert": false,
                    "is_required": false,
                    "api_name": "teamMemberPermissionType",
                    "options": [
                        {
                            "label": "只读",
                            "value": "1"
                        },
                        {
                            "label": "读写",
                            "value": "2"
                        }
                    ],
                    "is_unique": false,
                    "define_type": "package",
                    "description": "成员权限类型",
                    "label": "成员权限类型",
                    "type": "select_one",
                    "help_text": "成员权限类型"
                }
            },
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504215,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "label": "相关团队",
            "type": "embedded_object_list",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "relevant_team",
            "define_type": "package",
            "_id": "5e854ad5f71c7b0001bfdfdd",
            "is_single": false,
            "label_r": "相关团队",
            "is_index_field": false,
            "index_name": "a_team",
            "help_text": "相关团队",
            "status": "new"
        },
        "field_cyY7r__c": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "department_many",
            "is_required": false,
            "wheres": [],
            "enable_clone": true,
            "optional_type": "department",
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "a_6",
            "is_index": true,
            "is_active": true,
            "create_time": 1722569718544,
            "is_encrypted": false,
            "default_value": "",
            "label": "测试部门(多选)",
            "field_num": 7,
            "api_name": "field_cyY7r__c",
            "_id": "66ac53f6f2fd4e0001c39f29",
            "is_index_field": false,
            "help_text": "",
            "status": "new"
        },
        "product_line": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "产品线",
            "is_unique": false,
            "disable_after_filter": true,
            "type": "select_one",
            "default_to_zero": false,
            "is_required": false,
            "options": [
                {
                    "label": "产品线1",
                    "value": "1"
                },
                {
                    "label": "产品线2",
                    "value": "2"
                },
                {
                    "label": "产品线3",
                    "value": "3"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "label_r": "产品线",
            "index_name": "s_8",
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504229,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品线",
            "is_need_convert": false,
            "api_name": "product_line",
            "_id": "5e854ad5f71c7b0001bfdfde",
            "is_index_field": false,
            "config": {},
            "help_text": "",
            "status": "released"
        },
        "unit": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "单位",
            "is_unique": false,
            "disable_after_filter": true,
            "type": "select_one",
            "default_to_zero": false,
            "is_required": true,
            "options": [
                {
                    "label": "个",
                    "value": "1"
                },
                {
                    "label": "块",
                    "value": "2"
                },
                {
                    "label": "只",
                    "value": "3"
                },
                {
                    "label": "把",
                    "value": "4"
                },
                {
                    "label": "枚",
                    "value": "5"
                },
                {
                    "label": "条",
                    "value": "6"
                },
                {
                    "label": "瓶",
                    "value": "7"
                },
                {
                    "label": "盒",
                    "value": "8"
                },
                {
                    "label": "套",
                    "value": "9"
                },
                {
                    "label": "箱",
                    "value": "10"
                },
                {
                    "label": "米",
                    "value": "11"
                },
                {
                    "label": "千克",
                    "value": "12"
                },
                {
                    "label": "吨",
                    "value": "13"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "label_r": "单位",
            "index_name": "s_9",
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504237,
            "is_encrypted": false,
            "default_value": "",
            "label": "单位1",
            "is_need_convert": false,
            "api_name": "unit",
            "_id": "5e854ad5f71c7b0001bfdfdf",
            "is_index_field": false,
            "config": {},
            "help_text": "",
            "status": "released"
        },
        "data_own_department": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "department",
            "is_required": false,
            "wheres": [],
            "optional_type": "department",
            "define_type": "package",
            "is_single": true,
            "label_r": "归属部门",
            "index_name": "data_owner_dept_id",
            "is_index": true,
            "is_active": true,
            "create_time": 1585793749511,
            "is_encrypted": false,
            "default_value": "",
            "label": "归属部门",
            "is_need_convert": false,
            "api_name": "data_own_department",
            "_id": "624524695a7cf000017c2fd0",
            "is_index_field": false,
            "help_text": "",
            "status": "released"
        },
        "name": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "产品名称",
            "is_unique": true,
            "type": "text",
            "default_to_zero": false,
            "is_required": true,
            "define_type": "system",
            "input_mode": "",
            "is_single": false,
            "label_r": "产品名称",
            "index_name": "name",
            "max_length": 100,
            "is_index": true,
            "is_active": true,
            "create_time": 1545727072884,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品名称",
            "is_need_convert": false,
            "api_name": "name",
            "_id": "5e854ad5f71c7b0001bfdfe1",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "_id": {
            "describe_api_name": "ProductObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1585793749511,
            "pattern": "",
            "is_unique": false,
            "description": "_id",
            "label": "ID",
            "type": "text",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "_id",
            "define_type": "system",
            "index_name": "_id",
            "max_length": 200,
            "status": "released"
        },
        "virtual_base_price_book_price": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "价目表价格（本位币）",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "价目表价格（本位币）",
            "index_name": "t_0",
            "max_length": 500,
            "is_index": false,
            "is_active": true,
            "create_time": 1715241904560,
            "is_encrypted": false,
            "default_value": "",
            "label": "价目表价格（本位币）",
            "is_abstract": true,
            "is_need_convert": false,
            "api_name": "virtual_base_price_book_price",
            "_id": "663c83b0212b6f0001d7d6b5",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "tenant_id": {
            "describe_api_name": "ProductObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1585793749511,
            "pattern": "",
            "is_unique": false,
            "description": "tenant_id",
            "label": "租户ID",
            "type": "text",
            "is_need_convert": false,
            "is_required": true,
            "api_name": "tenant_id",
            "define_type": "system",
            "index_name": "ei",
            "max_length": 200,
            "status": "released"
        },
        "field_x25an__c": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "number",
            "decimal_places": 0,
            "default_to_zero": true,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "d_5",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1702630258010,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 14,
            "default_value": "",
            "label": "小数位测试",
            "field_num": 2,
            "api_name": "field_x25an__c",
            "_id": "657c1372547cba000131e755",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new"
        },
        "shop_category_id__c": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "",
            "is_unique": false,
            "where_type": "field",
            "type": "object_reference_many",
            "is_required": false,
            "wheres": [],
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "a_4",
            "is_index": true,
            "is_active": true,
            "create_time": 1681801911961,
            "is_encrypted": false,
            "default_value": "",
            "label": "商城分类自定义",
            "target_api_name": "ShopCategory__c",
            "target_related_list_name": "target_related_list_T7Jnf__c",
            "field_num": 5,
            "target_related_list_label": "产品",
            "action_on_target_delete": "set_null",
            "api_name": "shop_category_id__c",
            "_id": "643e42b843425900013b77b0",
            "is_index_field": false,
            "help_text": "",
            "status": "new"
        },
        "description": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "",
            "is_unique": false,
            "type": "html_rich_text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "图文描述",
            "index_name": "s_12",
            "max_length": 131072,
            "is_index": false,
            "is_active": true,
            "create_time": 1639065090269,
            "is_encrypted": false,
            "default_value": "",
            "label": "图文描述",
            "api_name": "description",
            "_id": "61b22602400d10000153e774",
            "is_index_field": false,
            "help_text": "",
            "status": "released"
        },
        "product_status": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "上下架",
            "is_unique": false,
            "disable_after_filter": true,
            "type": "select_one",
            "default_to_zero": false,
            "is_required": true,
            "options": [
                {
                    "label": "已上架",
                    "value": "1"
                },
                {
                    "label": "已下架",
                    "value": "2"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "is_extend": false,
            "label_r": "上下架",
            "index_name": "s_2",
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504184,
            "is_encrypted": false,
            "default_value": "1",
            "label": "上下架",
            "is_need_convert": false,
            "api_name": "product_status",
            "_id": "5e854ad5f71c7b0001bfdfc4",
            "is_index_field": false,
            "config": {},
            "help_text": "",
            "status": "released"
        },
        "remark": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "备注",
            "is_unique": false,
            "type": "long_text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "备注",
            "index_name": "t_1",
            "max_length": 2000,
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "min_length": 0,
            "default_value": "",
            "label": "备注",
            "is_need_convert": false,
            "api_name": "remark",
            "_id": "5e854ad5f71c7b0001bfdfc5",
            "is_index_field": false,
            "help_text": "",
            "status": "released"
        },
        "replacement_period": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "更换周期（天）",
            "is_unique": false,
            "type": "number",
            "decimal_places": 0,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "更换周期（天）",
            "index_name": "d_1",
            "max_length": 14,
            "is_index": false,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 14,
            "default_value": "",
            "label": "更换周期（天）",
            "is_need_convert": false,
            "api_name": "replacement_period",
            "_id": "5e854ad5f71c7b0001bfdfc6",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released"
        },
        "product_code": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "产品编码",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "产品编码",
            "index_name": "t_2",
            "max_length": 50,
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "default_value": "",
            "label": "产品编码",
            "is_need_convert": false,
            "api_name": "product_code",
            "_id": "5e854ad5f71c7b0001bfdfc7",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "origin_source": {
            "describe_api_name": "ProductObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1585793749511,
            "is_unique": false,
            "label": "数据来源",
            "type": "select_one",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "origin_source",
            "options": [
                {
                    "label": "数据同步",
                    "value": "0"
                }
            ],
            "define_type": "system",
            "is_extend": false,
            "index_name": "s_os",
            "config": {
                "display": 0
            },
            "status": "released",
            "description": ""
        },
        "lock_user": {
            "describe_api_name": "ProductObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "description": "加锁人",
            "is_unique": false,
            "label": "加锁人",
            "type": "employee",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "lock_user",
            "define_type": "package",
            "_id": "5e854ad5f71c7b0001bfdfc8",
            "is_single": true,
            "is_extend": false,
            "label_r": "加锁人",
            "is_index_field": false,
            "index_name": "a_1",
            "status": "released"
        },
        "on_shelves_time": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "上架时间",
            "is_unique": false,
            "type": "date_time",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "is_extend": false,
            "label_r": "上架时间",
            "index_name": "l_1",
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "not_use_multitime_zone": false,
            "default_value": "",
            "label": "上架时间",
            "time_zone": "GMT+8",
            "is_need_convert": false,
            "api_name": "on_shelves_time",
            "date_format": "yyyy-MM-dd HH:mm",
            "_id": "5e854ad5f71c7b0001bfdfc9",
            "is_index_field": false,
            "help_text": "",
            "status": "released"
        },
        "field_M6Ez2__c": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "number",
            "decimal_places": 2,
            "default_to_zero": true,
            "is_required": false,
            "enable_clone": true,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "d_8",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1586876125323,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 12,
            "default_value": "",
            "label": "数字字段YOYO",
            "field_num": 1,
            "api_name": "field_M6Ez2__c",
            "_id": "5e95cedd1118d40001ad1509",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new"
        },
        "safety_stock": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "安全库存",
            "is_unique": false,
            "type": "number",
            "decimal_places": 0,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "安全库存",
            "index_name": "d_2",
            "max_length": 14,
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 14,
            "default_value": "",
            "label": "安全库存",
            "is_need_convert": false,
            "api_name": "safety_stock",
            "_id": "65a16baeac53990001362831",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released"
        },
        "is_deleted": {
            "describe_api_name": "ProductObj",
            "is_index": false,
            "create_time": 1585793749511,
            "is_unique": false,
            "description": "is_deleted",
            "default_value": false,
            "label": "删除状态",
            "type": "true_or_false",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "is_deleted",
            "options": [],
            "define_type": "system",
            "index_name": "is_del",
            "status": "released"
        },
        "object_describe_api_name": {
            "describe_api_name": "ProductObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1585793749511,
            "pattern": "",
            "is_unique": false,
            "description": "object_describe_api_name",
            "label": "object_describe_api_name",
            "type": "text",
            "is_need_convert": false,
            "is_required": true,
            "api_name": "object_describe_api_name",
            "define_type": "system",
            "index_name": "api_name",
            "max_length": 200,
            "status": "released"
        },
        "out_owner": {
            "describe_api_name": "ProductObj",
            "is_index": true,
            "is_active": true,
            "create_time": 1585793749511,
            "is_unique": false,
            "label": "外部负责人",
            "type": "employee",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "out_owner",
            "define_type": "system",
            "is_single": true,
            "index_name": "o_owner",
            "config": {
                "display": 1
            },
            "status": "released",
            "description": ""
        },
        "field_pbiil__c": {
            "expression_type": "js",
            "return_type": "text",
            "describe_api_name": "ProductObj",
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "formula",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "custom",
            "is_single": false,
            "is_extend": true,
            "index_name": "t_16",
            "is_index": true,
            "is_active": true,
            "expression": "$product_category_id__r.category_code$&'/'&$product_category_id__r.lock_status._value$",
            "create_time": 1670557260394,
            "is_encrypted": false,
            "label": "计算字段",
            "field_num": 4,
            "api_name": "field_pbiil__c",
            "_id": "6392ae4d3dab430001ed4d25",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "new"
        },
        "virtual_min_order_quantity": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "is_extend": false,
            "label_r": "最小起订量",
            "index_name": "t_14",
            "max_length": 500,
            "is_index": false,
            "is_active": false,
            "create_time": 1657292539531,
            "is_encrypted": false,
            "default_value": "",
            "label": "最小起订量_虚拟",
            "is_abstract": true,
            "is_need_convert": false,
            "api_name": "virtual_min_order_quantity",
            "_id": "62c846fb429c860001fbf1c6",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "owner": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "负责人",
            "is_unique": false,
            "where_type": "field",
            "type": "employee",
            "decimal_places": 0,
            "is_required": true,
            "wheres": [],
            "define_type": "package",
            "is_single": true,
            "is_extend": false,
            "label_r": "负责人",
            "index_name": "owner",
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504185,
            "is_encrypted": false,
            "length": 18,
            "default_value": "",
            "label": "负责人",
            "is_need_convert": true,
            "api_name": "owner",
            "_id": "5e854ad5f71c7b0001bfdfd5",
            "is_index_field": false,
            "round_mode": 4,
            "help_text": "",
            "status": "released"
        },
        "picture_path": {
            "describe_api_name": "ProductObj",
            "auto_adapt_places": false,
            "description": "产品图片",
            "is_unique": false,
            "file_source": [],
            "type": "image",
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "产品图片",
            "index_name": "a_2",
            "support_file_types": [],
            "is_index": true,
            "file_amount_limit": 5,
            "is_active": true,
            "watermark": [
                {
                    "type": "variable",
                    "value": "current_user"
                },
                {
                    "type": "variable",
                    "value": "current_time"
                },
                {
                    "type": "variable",
                    "value": "current_address"
                }
            ],
            "create_time": 1547943504185,
            "is_encrypted": false,
            "label": "产品图片",
            "is_watermark": false,
            "file_size_limit": 10485760,
            "is_ocr_recognition": false,
            "is_need_convert": false,
            "api_name": "picture_path",
            "is_need_cdn": true,
            "_id": "6612b8d254775900010881e0",
            "is_index_field": false,
            "identify_type": "",
            "help_text": "单个图片不得超过20M",
            "status": "released"
        },
        "last_modified_time": {
            "describe_api_name": "ProductObj",
            "is_index": true,
            "create_time": 1585793749511,
            "is_unique": false,
            "description": "last_modified_time",
            "label": "最后修改时间",
            "type": "date_time",
            "time_zone": "",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "last_modified_time",
            "define_type": "system",
            "date_format": "yyyy-MM-dd HH:mm:ss",
            "index_name": "md_time",
            "status": "released"
        },
        "life_status": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "description": "生命状态",
            "is_unique": false,
            "disable_after_filter": true,
            "type": "select_one",
            "default_to_zero": false,
            "is_required": false,
            "options": [
                {
                    "label": "未生效",
                    "value": "ineffective"
                },
                {
                    "label": "审核中",
                    "value": "under_review"
                },
                {
                    "label": "正常",
                    "value": "normal"
                },
                {
                    "label": "变更中",
                    "value": "in_change"
                },
                {
                    "label": "作废",
                    "value": "invalid"
                }
            ],
            "define_type": "package",
            "is_single": false,
            "is_extend": false,
            "label_r": "生命状态",
            "index_name": "s_6",
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504215,
            "is_encrypted": false,
            "default_value": "normal",
            "label": "生命状态",
            "is_need_convert": false,
            "api_name": "life_status",
            "_id": "5e854ad5f71c7b0001bfdfd9",
            "is_index_field": false,
            "config": {},
            "help_text": "生命状态",
            "status": "released"
        },
        "product_video": {
            "describe_api_name": "ProductObj",
            "auto_adapt_places": false,
            "support_file_suffix": [],
            "description": "",
            "is_unique": false,
            "file_source": [
                "local",
                "net"
            ],
            "type": "file_attachment",
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "视频",
            "index_name": "a_8",
            "support_file_types": [],
            "is_index": true,
            "file_amount_limit": 1,
            "is_active": true,
            "create_time": 1715270556515,
            "is_encrypted": false,
            "label": "主图视频",
            "file_size_limit": 104857600,
            "is_ocr_recognition": false,
            "api_name": "product_video",
            "_id": "663cf39c6bc4510001b11982",
            "is_index_field": false,
            "identify_type": "",
            "help_text": "单个文件不得超过100M",
            "status": "released",
            "biz_field_api_name": "biz_field_video"
        },
        "last_modified_by": {
            "describe_api_name": "ProductObj",
            "is_index": true,
            "is_active": true,
            "create_time": 1585793749511,
            "is_unique": false,
            "label": "最后修改人",
            "type": "employee",
            "is_need_convert": true,
            "is_required": false,
            "api_name": "last_modified_by",
            "define_type": "system",
            "is_single": true,
            "index_name": "md_by",
            "status": "released",
            "description": ""
        },
        "technical_parameter": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "description": "技术参数",
            "is_unique": false,
            "type": "long_text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "is_extend": false,
            "label_r": "技术参数",
            "index_name": "t_8",
            "max_length": 512,
            "is_index": false,
            "is_active": true,
            "create_time": 1547943504215,
            "is_encrypted": false,
            "min_length": 0,
            "default_value": "",
            "label": "技术参数",
            "is_need_convert": false,
            "api_name": "technical_parameter",
            "_id": "5e854ad5f71c7b0001bfdfdb",
            "is_index_field": false,
            "help_text": "",
            "status": "released"
        },
        "out_tenant_id": {
            "describe_api_name": "ProductObj",
            "is_index": false,
            "is_active": true,
            "create_time": 1585793749511,
            "pattern": "",
            "is_unique": false,
            "description": "out_tenant_id",
            "label": "外部企业",
            "type": "text",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "out_tenant_id",
            "define_type": "system",
            "index_name": "o_ei",
            "config": {
                "display": 0
            },
            "max_length": 200,
            "status": "released"
        },
        "record_type": {
            "describe_api_name": "ProductObj",
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504223,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "description": "record_type",
            "is_unique": false,
            "label": "业务类型",
            "type": "record_type",
            "is_need_convert": false,
            "is_required": true,
            "api_name": "record_type",
            "options": [
                {
                    "is_active": true,
                    "api_name": "default__c",
                    "description": "预设业务类型",
                    "label": "预设业务类型"
                }
            ],
            "define_type": "package",
            "_id": "5e854ad5f71c7b0001bfdfdc",
            "is_single": false,
            "is_extend": false,
            "label_r": "业务类型",
            "is_index_field": false,
            "index_name": "r_type",
            "config": {},
            "help_text": "",
            "status": "released"
        },
        "max_stock": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "remove_mask_roles": {},
            "description": "最大库存",
            "is_unique": false,
            "type": "number",
            "decimal_places": 0,
            "default_to_zero": true,
            "is_required": false,
            "define_type": "package",
            "is_single": false,
            "label_r": "最大库存",
            "index_name": "d_9",
            "max_length": 16,
            "is_index": true,
            "is_active": true,
            "create_time": 1603557105342,
            "is_encrypted": false,
            "display_style": "input",
            "step_value": 1,
            "length": 16,
            "default_value": "",
            "label": "最大库存",
            "is_need_convert": false,
            "api_name": "max_stock",
            "_id": "5f9456f1d8137700019e66e4",
            "is_index_field": false,
            "is_show_mask": false,
            "round_mode": 4,
            "help_text": "",
            "status": "new"
        },
        "product_spec": {
            "describe_api_name": "ProductObj",
            "default_is_expression": false,
            "auto_adapt_places": false,
            "pattern": "",
            "remove_mask_roles": {},
            "description": "规格属性",
            "is_unique": false,
            "type": "text",
            "default_to_zero": false,
            "is_required": false,
            "define_type": "package",
            "input_mode": "",
            "is_single": false,
            "label_r": "规格属性",
            "index_name": "t_9",
            "max_length": 400,
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504237,
            "is_encrypted": false,
            "default_value": "",
            "label": "规格属性",
            "is_need_convert": false,
            "api_name": "product_spec",
            "_id": "5e854ad5f71c7b0001bfdfe0",
            "is_index_field": false,
            "is_show_mask": false,
            "help_text": "",
            "status": "released"
        },
        "category": {
            "describe_api_name": "ProductObj",
            "is_index": true,
            "is_active": true,
            "create_time": 1547943504241,
            "is_encrypted": false,
            "auto_adapt_places": false,
            "description": "分类",
            "is_unique": false,
            "default_value": "",
            "label": "分类",
            "type": "select_one",
            "is_need_convert": false,
            "is_required": false,
            "api_name": "category",
            "options": [
                {
                    "label": "纺织",
                    "value": "1667391518346"
                },
                {
                    "label": "电子",
                    "value": "1667391503902"
                },
                {
                    "label": "农业",
                    "value": "1667391539936"
                },
                {
                    "label": "办公用品",
                    "value": "1"
                },
                {
                    "label": "办公用品/食品",
                    "value": "6"
                },
                {
                    "label": "服务",
                    "value": "3"
                },
                {
                    "label": "家居用品",
                    "value": "5"
                },
                {
                    "label": "渔业",
                    "value": "1667391530818"
                },
                {
                    "label": "牧业",
                    "value": "1667391552120"
                },
                {
                    "label": "快消品",
                    "value": "1667391570606"
                },
                {
                    "label": "纸张",
                    "value": "1667531044732"
                },
                {
                    "label": "炬森五金",
                    "value": "7"
                },
                {
                    "label": "炬森五金/开门器",
                    "value": "8"
                },
                {
                    "label": "炬森五金/开门器/开门子类",
                    "value": "11"
                },
                {
                    "label": "炬森五金/开门器/开门子类/四级分类",
                    "value": "12"
                },
                {
                    "label": "炬森五金/开门器/开门子类2",
                    "value": "1662539904132"
                },
                {
                    "label": "炬森五金/铰链",
                    "value": "9"
                },
                {
                    "label": "炬森五金/导轨",
                    "value": "10"
                },
                {
                    "label": "炬森五金/二级分类",
                    "value": "13"
                },
                {
                    "label": "炬森五金/二级分类1",
                    "value": "14"
                },
                {
                    "label": "炬森五金/二级分类1/三级分类",
                    "value": "1662539077074"
                },
                {
                    "label": "炬森五金/二级分类1/三级分类/四级分类",
                    "value": "1662539629284"
                },
                {
                    "label": "炬森五金/二级分类1/三级分类2",
                    "value": "1662539093733"
                },
                {
                    "label": "家用电器",
                    "value": "4"
                },
                {
                    "label": "数码产品",
                    "value": "2"
                },
                {
                    "label": "数码产品/手机",
                    "value": "1718703490099"
                },
                {
                    "label": "数码产品/手机/诺基亚",
                    "value": "1718703517194"
                },
                {
                    "label": "数码产品/手机/华为",
                    "value": "1718703531180"
                },
                {
                    "label": "数码产品/手机/iPhone",
                    "value": "1718703548494"
                },
                {
                    "label": "数码产品/平板",
                    "value": "1718703501918"
                },
                {
                    "label": "数码产品/平板/华为",
                    "value": "1718703563146"
                },
                {
                    "label": "数码产品/平板/iPad",
                    "value": "1718703574954"
                },
                {
                    "label": "饮料",
                    "value": "1667531029488"
                },
                {
                    "label": "多单位产品",
                    "value": "1671174047180"
                },
                {
                    "label": "商城分类-家电",
                    "value": "1705080673895"
                },
                {
                    "label": "商城分类-890全网",
                    "value": "1706369246530"
                },
                {
                    "label": "产品分类一级",
                    "value": "1705080573545"
                },
                {
                    "label": "商城分类-冰淇淋",
                    "value": "1705081459252"
                },
                {
                    "label": "商城分类-水果",
                    "value": "1705081382680"
                },
                {
                    "label": "最小起订",
                    "value": "1702277664161"
                },
                {
                    "label": "最小起订/二级1",
                    "value": "1721034144446"
                },
                {
                    "label": "最小起订/二级2",
                    "value": "1721034158956"
                },
                {
                    "label": "商城分类009",
                    "value": "1705660330572"
                },
                {
                    "label": "商城分类009/商城分类0092",
                    "value": "1705660437141"
                },
                {
                    "label": "商城分类一级",
                    "value": "1705080540625"
                },
                {
                    "label": "商城分类一级/商城分类二级",
                    "value": "1705080595411"
                }
            ],
            "define_type": "package",
            "_id": "5e854ad5f71c7b0001bfe00c",
            "is_single": false,
            "label_r": "分类",
            "is_index_field": false,
            "index_name": "s_10",
            "config": {},
            "help_text": "",
            "status": "released"
        }
    },
    "release_version": "6.4",
    "actions": {}
}
