export default function () {
  return {
    name: 'dht_web_product_detail_card',
    //   displayName: $t('商品参数信息'),
    // displayName: $t('dht.component.web.product_detail_card', '商品卡片'),
    data: {
      name: $t('dht.component.web.product_detail_card', '商品卡片'),
    },
    $$data: {
      isCanvas: true
    },
    rules: {
      canDrag: () => false,
    },
    related: {
      previewDisplay: () => import('./display.vue'),
      attributeSettings: [
        /* {
          inject: ['useInternalNode'],          
          components: {
            KeyInfoSetting: () => import('./setting.vue')
          },
        }  ,*/
        {
          name: 'SetterField',
          data: {
            //   label: $t('paasbiz.portal-designer.max-content-set', '参数设置'),
            // label: '参数设置',
            display: 'block',
            setter: {
              component: () => import('./setting.vue')
            }
          }
        }]
    }
  }
}