<template>
  <div class="dht-product-detail-key-info-display">    
    <div class="dht-title">{{ $t('dht.component.web.product_detail_key_info.title', '参数信息') }}</div>
    <div class="dht-content">
      <div class="dht-item" :class="{'dht-double': showType === '2'}" v-for="(item, index) in cFields" :key="item.key">
        <div class="dht-field">          
          <span class="dht-text dht-name">{{ item.name }}</span>
          <span class="dht-text dht-val">{{ item.val || '--'}}</span>
        </div>
      </div>      
    </div>
  </div>
</template>

<script>
import { dhtBizModel } from '../../../utils/model';

  export default {
    inject: ['useInternalNode'],
    props : {
      objectContext: {
        type: Object,
        default: () => ({}),
      },
      // name: {
      //   required: true,
      //   type: String,
      //   default: '',
      // },
      showType: {
        type: String,
        default: '1',
      },
      selectedFields: {
        type: Array,
        default: () => ['name'],
      },
    },
    computed: {
      cFields() {
        let sfields = this.selectedFields || [];
        // let allFields = this.objectContext?.describe?.fields || [];
        const allFields = this.allFields;
        
        let rst = sfields.map(key => {
          let item = allFields[key] || {};
          return {  
            key,
            name: item.label_r || item.label || '--',
            val: '--',
          }
        });    
        // console.log('key_info_display cFields rst:', rst);
        return rst;
      },
    },
    data() {
      return {   
        allFields: [],     
      }
    }, 
    methods: {
      async init() {
        this.allFields = await dhtBizModel.fetchObjFields(dhtBizModel.mainObjApiName());
      },
    }, 
    created() {
      this.init();
    }
  }
</script>

<style lang="less" scoped>
// @import '~less/class.less';
.dht-product-detail-key-info-display {
  padding: 16px;

.dht-title {
  font-size: 14px;
  color: #181c25;
  margin-bottom: 16px;
}

.dht-content {  
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 100%; 
}

.dht-item {
  flex: 0 0 100%;
  padding: 8px 0;  
  box-sizing: border-box;
  text-align: left;
  max-width: 100%;
}
.dht-item.dht-double {
  flex: 0 0 50%;
  max-width: 50%;
}

.dht-field {
  width: 100%;
  display: flex;
  align-items: flex-start;
  font-size: 11px;
  padding-right: 10px;
  .dht-text {
    width: 100%;
    overflow: hidden; 
    text-overflow: ellipsis; 
    white-space: nowrap; 
  }

  .dht-val {
    color: var(--color-neutrals19, #181c25);
    margin-bottom: 4px;
  }
  .dht-name {
    color: var(--color-neutrals11, #91959e);
  }
}

}

</style>