const path = require('path');
const config = require('../../project.config');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const { VueLoaderPlugin } = require('vue-loader');
const HappyPack = require('happypack'); // 开启多进程
const yaml = require('yamljs');
const json5 = require('json5');

let libraryName = config.name.replace(/^./, (m) => m.toLocaleUpperCase());

let webpackConfig = {
    entry: {
        app: path.resolve(__dirname, '../../src/index.js'),
    },
    output: {
        path: path.resolve(__dirname, '../../release'),
        filename: '[name].js',
        chunkFilename: '[name].chunk.js',
        libraryTarget: 'umd',
        libraryExport: 'default',
        library: libraryName,
        umdNamedDefine: true,
    },

    resolve: {
        extensions: ['.js', '.vue', '.json', '.ts'],
        alias: {
            '@': path.resolve(__dirname, '../../src'),
            '@assets': path.resolve(__dirname, '../../src/assets/assets'),
            '@img': path.resolve(__dirname, '../../src/assets/img'),
            '@css': path.resolve(__dirname, '../../src/assets/css'),
            '@components': path.resolve(__dirname, '../../src/components'),
            '@utils': path.resolve(__dirname, '../../src/utils'),
            '@config': path.resolve(__dirname, '../../src/config'),
            '@page': path.resolve(__dirname, '../../src/page'),
        },
    },

    target: 'web',

    //
    // 尽量用底层的全局cdn
    // 不要外部使用
    // externals: {
    //     jquery: 'jQuery',
    //     vue: 'Vue',
    //     underscore: '_',
    // },

    //
    // 自定义loader
    resolveLoader: {
        modules: [path.resolve(__dirname, '../../node_modules'), path.resolve(__dirname, '../loader')],
    },

    //
    //
    module: {
        rules: [
            {
                test: /\.(jsx?|babel|es6)$/,
                use: ['happypack/loader?id=babel'],
                //loader: 'babel-loader',
                include: [path.resolve(__dirname, '../../src')],
            },
            {
                test: /\.vue$/,
                loader: 'vue-loader',
                options: {
                    compilerOptions: {
                        preserveWhitespace: false,
                    },
                },
            },
            {
                test: /\.(png|svg|jpg|jpeg|gif)$/i,
                type: 'asset',
                generator: {
                    filename: 'assets/img/[name].[contenthash:6][ext][query]',
                },
                parser: {
                    dataUrlCondition: {
                        maxSize: 4 * 1024,
                    },
                },
            },
            {
                test: /\.(woff|woff2|eot|ttf|otf)$/i,
                type: 'asset/inline',
            },
            {
                test: /\.yaml$/i,
                type: 'json',
                parser: {
                    parse: yaml.parse,
                },
            },
            {
                test: /\.json5$/i,
                type: 'json',
                parser: {
                    parse: json5.parse,
                },
            },
        ],
    },

    plugins: [
        //
        // 清空目录
        new CleanWebpackPlugin(),

        new VueLoaderPlugin(),

        new HappyPack({
            id: 'babel',
            loaders: [
                {
                    loader: 'babel-loader',
                },
            ],
        }),
    ],

    stats: {
        entrypoints: false,
        modules: false,
        children: false,
        chunks: false,
        chunkModules: false,
    },
};

//
// 分析
if (process.env.REPORT === 'report') {
    const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

    webpackConfig.plugins.push(new BundleAnalyzerPlugin());
}

module.exports = webpackConfig;
