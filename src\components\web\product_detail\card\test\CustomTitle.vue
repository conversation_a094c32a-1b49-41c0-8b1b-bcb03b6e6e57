<template>
  <div class="custom-title">
    <div class="tags">
      <span v-if="tag" class="tag hot">热销</span>
      <span v-if="tag" class="tag new">上新</span>
    </div>
    <h1 class="title">自定义商品标题样式</h1>
  </div>
</template>

<script>
export default {
  name: 'CustomTitle',
  props: {
    tag: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
.custom-title {
  padding: 16px;
}
.tags {
  margin-bottom: 8px;
}
.tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
  margin-right: 8px;
}
.tag.hot {
  background: #f56c6c;
}
.tag.new {
  background: #67c23a;
}
.title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin: 0;
}
</style> 