<template>
  <div class="dht-product-detail-key-info-display">
    test123
    <div class="dht-title">{{ name }}</div>

    <div class="dht-content">
      <div class="dht-item" :class="{'dht-double': showType === '2'}" v-for="(item, index) in cFields" :key="item.key">
        <div class="dht-field">          
          <span class="dht-text dht-name">{{ item.name }}</span>
          <span class="dht-text dht-val">{{ item.val || '--'}}</span>
        </div>
      </div>      
    </div>
  </div>
</template>

<script>
import { dhtBizModel } from '../../utils/model';

  export default {
    name: 'dht_web_product_detail_key_info',
    inject: {
      setProps: {
          from: 'setProps',
          default() {
              return () => { };
          }
      }
    },
    props : {  
      name: {
        type: String,
        default: '',
      },    
      showType: {
        type: String,
        default: '1',
      },
      selectedFields: {
        type: Array,
        default: () => [],
      },
      fields: {
        type: Object,
        default: () => {},
      },
    },
    computed: {
      /* name() {
        return this.$attrs.props.name || '';
      },
      showType() {
        return this.$attrs.props.showType || '1';
      },
      fields() {
        return this.$attrs.props.fields || {};
      },
      selectedFields() {
        return this.$attrs.props.selectedFields || [];
      }, */
      cFields() {
        let sfields = this.selectedFields || [];
        let allFields = this.fields;
        let rst = sfields.map(key => {
          let item = allFields[key] || {};
          return {  
            key,
            name: item.label_r || item.label || '--',
            val: '--',
          }
        });
        return rst;
      },
      mainObjApiName() {
        return dhtBizModel.isSpuMode() ? 'SPUObj' : 'ProductObj';
      },
    },
    data() {
      return {        
      }
    }, 
    methods: {
      init() {
        dhtBizModel.fetchObjFields(this.mainObjApiName).then(fields => {
          const apiName = this.$attrs.apiName;
          this.setProps(apiName, (props) => {
            props.fields = fields;  
          });
        });
      },
    },   
    mounted() {
      this.init();
    }    
  }
</script>

<style lang="less" scoped>
// @import '~less/class.less';
.dht-product-detail-key-info-display {
  padding: 16px;

.dht-title {
  font-size: 14px;
  color: #181c25;
  margin-bottom: 16px;
}

.dht-content {  
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 100%; 
}

.dht-item {
  flex: 0 0 100%;
  padding: 8px 0;  
  box-sizing: border-box;
  text-align: left;
  max-width: 100%;
}
.dht-item.dht-double {
  flex: 0 0 50%;
  max-width: 50%;
}

.dht-field {
  width: 100%;
  display: flex;
  align-items: flex-start;
  font-size: 11px;
  padding-right: 10px;
  .dht-text {
    width: 100%;
    overflow: hidden; 
    text-overflow: ellipsis; 
    white-space: nowrap; 
  }

  .dht-val {
    color: var(--color-neutrals19, #181c25);
    margin-bottom: 4px;
  }
  .dht-name {
    color: var(--color-neutrals11, #91959e);
  }
}

}

</style>