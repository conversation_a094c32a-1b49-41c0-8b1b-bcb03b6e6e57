## 首页购物车飘数组件

### 组件说明
该组件用于显示网站顶部的购物车图标和商品数量，点击可跳转到购物车页面。

### 显示态
显示态包含购物车图标和可选的数量显示及文字显示。
- 购物车图标：使用 el-icon-shopping-cart-2 图标
- 数量显示：当购物车中有商品时，在图标右上角显示红色圆形数量标记
- 文字显示：可选显示"购物车"文字

### 设置态
设置态提供以下配置选项：
- 显示数量：控制是否显示购物车中的商品数量
- 显示文字：控制是否显示"购物车"文字

### 属性说明
```javascript
props: {
  showCount: {
    type: String,
    default: '1'  // '1'表示显示数量，'0'表示不显示
  },
  showText: {
    type: String,
    default: '1'  // '1'表示显示文字，'0'表示不显示
  }
}
```

### 事件
- cart-update：更新购物车数量的事件
- cart-click：点击购物车时触发的事件

### 使用方法
```html
<top-shopping-cart 
  :showCount="'1'" 
  :showText="'1'" 
  @cart-click="handleCartClick">
</top-shopping-cart>
```