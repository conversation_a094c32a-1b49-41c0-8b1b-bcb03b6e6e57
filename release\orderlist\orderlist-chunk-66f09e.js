"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[150,452],{4656:(t,i,r)=>{r.r(i),r.d(i,{default:()=>n});var s=function(){var t=this,i=t.$createElement,r=t._self._c||i;return r("div",{staticClass:"dht-orderlist-page"},[r("div",{staticClass:"desc"},[t._v("orderlist page")]),r("button",{on:{click:t.AiImport}},[t._v("AI import")]),r("button",{on:{click:t.showDhtAiOrder}},[t._v("showDhtAiOrder")]),r("button",{on:{click:t.destroyDhtAiOrder}},[t._v("destroyDhtAiOrder")])])};s._withStripped=!0;r(1819);const e={components:{},data:function(){return{dialogVisible:!1,ins:null}},methods:{AiImport:function(){this.dialogVisible=!0},showDhtAiOrder:function(){var t=this;Fx.getBizApi("dhtbiz","AiOrder").then((function(i){t.ins=i.showDhtAiOrder()}))},destroyDhtAiOrder:function(){this.ins&&(this.ins.destroy(),this.ins=null)}},beforeDestroy:function(){this.destroyDhtAiOrder()}};const n=(0,r(1900).Z)(e,s,[],!1,null,"773fdc13",null).exports}}]);