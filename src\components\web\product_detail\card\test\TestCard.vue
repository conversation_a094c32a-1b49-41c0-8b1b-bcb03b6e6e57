<template>
  <div>
    <h3>默认组件:</h3>
    <card :props="props" />
    
    <h3>自定义组件:</h3>
    <card 
      :props="props"
      :widgets="{
        price: CustomPrice,
        title: CustomTitle,
        preview: CustomPreview
      }"
    />
  </div>
</template>

<script>
import Card from '../card.vue';
import CustomPrice from './CustomPrice.vue';
import CustomTitle from './CustomTitle.vue';
import CustomPreview from './CustomPreview.vue';

export default {
  components: {
    Card,
    CustomPrice,
    CustomTitle,
    CustomPreview
  },
  data() {
    return {
      props: {
        img: '1',
        tag: '1',
        price: '1',
        attr: '0',
        stock: '1'
      }
    }
  }
}
</script> 