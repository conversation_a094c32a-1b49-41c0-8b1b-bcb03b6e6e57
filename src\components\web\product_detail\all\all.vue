<template>
  <div class="dhtbiz dht-product-detail-all">
    <goodDetail
      v-if="ready"
      class="dht-goodDetail-container"
      ref="goodDetail"
      :isSpuMode="isSpuMode"
      :isUseByModal="isUseByModal"
      :product="product"
      :pageConfig="pageConfig"
      :pluginService="pluginService"
    ></goodDetail>
  </div>
</template>

<script>
import { loadnsail } from '@/share/utils/load';

export default {
  name: 'dht_web_product_detail_all',
  components: {
    goodDetail: () => Fx.getBizComponent('dhtbiz', 'goodDetail').then(res => res())
  },
  // 对应url上的参数信息
  props: {
    _id: {
      type: String,
      default: ''
    },
    spu_id: {
      type: String,
      default: ''
    },
    pluginService: {
      type: String,
      default: () => {}
    },
    // options: {
    //   type: Object,
    //   default: () => ({})
    // }
  },
  computed: {
    isSpuMode() {
      return window.$dht.config.sail.isSpuMode || false;
    },
    isUseByModal() {
      return false;
    }
  },
  data() {
    return {
      ready: false,
      pageConfig: {}, // 设计器中配置的属性
      editorOptions: {},
      product: {
        _id: this._id,
        spu_id: this.spu_id || '',
      }
    };
  },
  created() {
    this.getPageConfig();
    // 站点中要先确保加载了 $dht
    // 需要确保先加载并执行了 https://ceshi112.fspage.com/html/nsail-dist/dht.e9984e18.js
    // nsail-dht:dht.95a32781.js
    /* seajs.use('nsail-dht', (dht) => {
      console.log(dht);
      this.ready = true;
    }); */

    if(!window.$dht) {
      // seajs.use('nsail-dist/dht.e9984e18.js', (dht) => {
      //   console.log(dht);
      //   try {
      //     dht.appWillMount().then(() => {
      //       this.product._id = '5f51f1a2750f730001768e82';
      //       this.ready = true;
      //     });

      //     // this.product._id = '5f51f1a2750f730001768e82';
      //     // dht.appWillMount()
      //     // this.ready = true;
      //   } catch (error) {
      //     this.product._id = '5f51f1a2750f730001768e82';
      //     this.ready = true;
      //     console.error(error);
      //   }
      // });
      loadnsail().then(() => {
        // todo not finish id:62ce853a928dcb000160a157 对应112环境 81028上游, 18938939236/1234qwer下游
        this.product._id = this._id || '62ce853a928dcb000160a157';
        if(this.spu_id) {
          this.product.spu_id = this.spu_id;
        }
        this.ready = true;
        // console.log(window.$dht);
      })

    } else {
      this.ready = true;
    }
  },

  methods: {
    getPageConfig() {
      let pageConfig = {};
      try {
        // 获取设计器中配置的参数信息, 因后台接口未准备好, 此处用本地缓存代替接口
        pageConfig = localStorage.getItem('dhtbiz_dht_web_product_detail_all') || {};        //
        if(pageConfig) {
          pageConfig = JSON.parse(pageConfig) ;
        }
      } catch (error) {
        pageConfig = {};
      }
      this.pageConfig = pageConfig;
      return pageConfig;
    },
    /* renderGoodDetail() {
      const me = this;
      const $goodDetail = this.$refs.goodDetail;
      let pageConfig = {};
      try {
        // 获取设计器中配置的参数信息, 因后台接口未准备好, 此处用本地缓存代替接口
        pageConfig = localStorage.getItem('dhtbiz_dht_web_product_detail_all') || {};        //
        if(pageConfig) {
          pageConfig = JSON.parse(pageConfig) ;
        }
      } catch (error) {
        pageConfig = {};
      }

      Fx.getBizComponent('dhtbiz', 'goodDetail').then(res => res()).then(Comp => {
        me.goodDetail = new Vue.extend(Comp)({
          el: $goodDetail,
          propsData: {
            isSpuMode: window.$dht.config.sail.isSpuMode,
            isUseByModal: false,
            product: {
              _id: me._id,
              spu_id: me.spu_id,
            },
            pageConfig,
            pluginService: me.pluginService
          }
        });
      });
    } */
  },
  mounted() {
  }
}
</script>

<style lang="less" scoped>

</style>
