<div class="dht-card-item"
     :class="{'dht-card-item-hide': !isShow}"
     @mouseenter="showElem(true)"
     @mouseleave="showElem(false)">
  <div class="dht-card-item-content">
    <configured-card
      v-if="isConfigured"
      :product="product"
      :options="options"
      :product-config="productConfig"
      :product-fields="productFields"
      @on-action="onAction"
    ></configured-card>
    <base-card
      v-else
      :product="product"
      :options="options"
      @on-action="onAction"
    ></base-card>
    <div class="dht-card-item-operate" v-if="isShopMallMode">
      <slot name="operate">
        <fx-button
          v-if="product.is_package"
          icon="el-icon-shopping-cart-2"
          size="mini"
          @click="onAction('BOM', product)">{{ $t('选配置') }}
        </fx-button>
        <fx-button
          v-else-if="product.is_spec"
          icon="el-icon-shopping-cart-2"
          size="mini"
          @click="onAction('SPEC', product)">
          {{ $t('选规格') }}
        </fx-button>
        <fx-button
          v-else-if="product.attribute"
          icon="el-icon-shopping-cart-2"
          size="mini"
          @click="onAction('ATTR', product)">
        {{ $t('product_cart.select_attribute') }}
        </fx-button>
        <div class="single-spec-operate" v-else>
          <div class="card-item-input-wrap">
            <input-number
              ref="fkInput"
              class="quantity-input"
              :min="productMinNum"
              :max="1000000"
              :precision="precision"
              @enter="onAction('CART', product)"
              v-model.trim="product.quantity">
            </input-number>
            <span class="product-unit">
              <unit-selector
                :product="product"
                :visible-arrow="false"
                :no-padding="true"
                position="top"
                @enter="showElem(true)"
                @leave="showElem(false)">
              </unit-selector>
            </span>
          </div>
          <i class="el-icon-shopping-cart-2 add-cart-btn" @click="onAction('CART', product)"
             :class="{active: product.quantity}"></i>
        </div>
      </slot>
    </div>
    <div class="dht-collection-btn" @click="onAction('COLLECTION', product)">
      <i :class="[product.is_in_collection ? 'el-icon-star-on' : 'el-icon-star-off']"></i>
      <span>{{ product.is_in_collection ? $t('已收藏') : $t('收藏') }}</span>
    </div>
  </div>
</div>

