<template>
  <card v-bind="{...$attrs, ...$props}" v-bind:objectContext="objectContext" />
</template>

<script>
import Card from '../card.vue';
export default {

  name: 'ProductDetailCardBeecraftDisplay',
  // mixins: [WidgetsDetailMixins],
  inject: ['useInternalNode'],
  components: {
    Card
  },
  props: {
    img: {
      type: String,
      default: '1'
    },
    tag: {
      type: String,
      default: '1'
    },
    price: {
      type: String,
      default: '1'
    },
    attr: {
      type: String,
      default: '0'
    },
    stock: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
    }
  },
  methods: {
    mockData() {      
    }
  },
  mounted() {
    this.mockData();
  }
}
</script>
