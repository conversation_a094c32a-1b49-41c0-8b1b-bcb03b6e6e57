一、通用组件输入输出
输入：
```
props: {
  dhtPageContext: {
    config: {},
    desctibe: {},
    // pageState是page中多个组件公用数据
    pageState: {
      category: {},
      search: {},
      pagnigation: {},
      filters: [
        // {
        //   field_name: 'name',
        //   field_values: ['test'],
        //   operator: 'LIKE'
        // }
      ],
      orders: [
        // {
        //   fieldName: 'name',
        //   isAsc: false
        // }
      ],
    }
  },
  // 设计器 右侧配置的数据合计
  templateData: {},
}
```

# 事件
| 事件名称                | 参数 |
|:--------------------|:--:| 
| list.render.before  |    |
| list.render.after  | 18 |

## list.render.before
```
{
  formatRequestParam,
  formatResponeResult,
  componentControl: {
    [key]: {
      render: Vue
      ....
    }
  }
}
```


