export default function () {
    return {
        name: 'dht_web_container_product_detail',
        displayName: $t('paas.widget.dht_web_container_product_detail.name', '商品详情容器'),
        data: {
            style: {
                justifyContent: 'center',
                alignItems: 'center',
            }
        },
        $$data: {
            template: {
                name: 'dht_web_container_product_detail',
                children: [       
                    // {
                    //     name: 'dht_web_product_detail_card',                        
                    // },
                    // {
                    //     name: 'dht_web_product_detail_key_info',
                    // },                    
                ]
            },
            // 上下文节点
            isContext: true,
            isCanvas: true,
            contextNode: {
                data: {
                    dhtPageContext: {
                        config: {},
                        describe: {},
                        pageState: {
                            test: 'test123'
                        }
                    },
                    dhtContainerApi: {},
                }
            }
        },
        related: {
            attributeSettings: [
                {
                  name: 'Setter<PERSON><PERSON>',
                  data: {
                    setter: {
                      component: () => import('./setting.vue')
                    }
                  }
            }],
            previewDisplay: () => import(
                /* webpackChunkName: "dht_web_container_product_list-beecraft-PreviewDisplay" */
                '../src/index.vue'
            )
        },
        rules: {
            canMoveIn: (nodes) => {
                return true;
            }
        },
    };
}
