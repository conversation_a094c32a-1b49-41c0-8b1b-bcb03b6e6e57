<template>






  <div class="selector-demo">
    <icon-field       
        :value="form.menuIcon"
        @change="handleMenuIconChange"
    ></icon-field>

    22222222222
    <icon-field
        class="navigation-icon-list"
        :iconList="iconList"
        :value="form.navigationIcon"
        :images="[
            {
                label: $t('默认图标')
            },
            {
                label: $t('选中图标')
            }
        ]"
        @change="handleNavigationIconChange"
    ></icon-field>



    <h2>FX-UI Selector 组件示例</h2>

    <!-- 示例1: 输入框样式 + 弹窗操作 + 默认全选 -->
    <div class="demo-section">
      <h3>1. 输入框样式 + 弹窗操作 + 默认显示全部选项</h3>

      <!-- 字段选择器 -->
      <field-selector
        v-model="customSelectedItems"
        placeholder="请选择分类"
        title="分类"
        dialog-title="选择字段"
        :selector-options="customSelectorOpts"
        @change="onCustomChange"
        @sort-change="onCustomSortChange"
        @confirm="onCustomConfirm"
      />

      <div class="result">
        <p>已选择: {{ JSON.stringify(customSelectedItems) }}</p>
      </div>
    </div>

    <!-- 示例2: 标准下拉选择器对比 -->
    <div class="demo-section">
      <h3>2. 标准 fx-selector-input-v2 (对比)</h3>
      <fx-selector-input-v2
        ref="standardSelector"
        v-bind="standardSelectorOpts"
        style="width: 400px;"
        @change="onStandardChange"
      />

      <div class="result">
        <p>已选择: {{ JSON.stringify(standardSelectedItems) }}</p>
      </div>
    </div>

    <!-- 示例3: fx-selector-box-v2 弹窗选择器 -->
    <div class="demo-section">
      <h3>3. fx-selector-box-v2 (弹窗选择器)</h3>
      <fx-button type="primary" @click="showSelectorBox = true">
        打开选择器弹窗
      </fx-button>

      <fx-selector-box-v2
        ref="selectorBox"
        :show.sync="showSelectorBox"
        v-bind="selectorBoxOpts"
        title="选择分类"
        @change="onBoxChange"
        @confirm="onConfirm"
        @cancel="onCancel"
      />

      <div class="result">
        <p>已选择: {{ JSON.stringify(boxSelectedItems) }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import FieldSelector from '@/components/common/fieldselect/index.vue'

export default {
  name: 'SelectorDemo',
  components: {
    FieldSelector,
    IconField: PAAS.get_component('IconSelect')
  },
  data() {
    return {
      iconList: [],
      form: {
        menuIcon: {},
        navigationIcon: {}
      },
      // 自定义选择器状态
      customSelectedItems: [
        { id: '1', name: '商品名称ssssssssssssssssssssssssss拉拉手动阀的', tabId: 'fields' },
        { id: '2', name: 'asdfasdfaasfasfdasdfasfasdfasdfadfasdfasfa', tabId: 'fields' },
        { id: '3', name: '商品类别', tabId: 'fields' },
        { id: '4', name: '库存量', tabId: 'fields' },
        { id: '5', name: '供应商信息', tabId: 'fields' },
        { id: '6', name: '售价', tabId: 'fields' },
        { id: '7', name: '上架时间', tabId: 'fields' },
        { id: '8', name: '产品状态', tabId: 'fields' },
        { id: '9', name: '条码', tabId: 'fields' },
        { id: '10', name: '保质期限', tabId: 'fields' },
        { id: '11', name: '附加说明', tabId: 'fields' },
        { id: '12', name: '附加说明', tabId: 'fields' }
      ],

      // 标准选择器状态
      standardSelectedItems: {},

      // 弹窗选择器状态
      showSelectorBox: false,
      boxSelectedItems: {},

      // 基础分类数据
      categoryData: [
        { id: '1', name: '电机', description: '各类电机产品' },
        { id: '2', name: '电动夹爪', description: '自动化夹爪设备' },
        { id: '3', name: '单轴机器人', description: '单轴运动设备' },
        { id: '4', name: '机器视觉', description: '视觉检测设备' },
        { id: '5', name: '传感器', description: '各类传感器' },
        { id: '6', name: '控制器', description: '控制系统设备' },
        { id: '7', name: '气动元件', description: '气动控制设备' },
        { id: '8', name: '液压元件', description: '液压控制设备' }
      ],

      subCategoryData: [
        { id: '11', name: '步进电机', parentId: '1' },
        { id: '12', name: '伺服电机', parentId: '1' },
        { id: '13', name: '直流电机', parentId: '1' },
        { id: '21', name: '平行夹爪', parentId: '2' },
        { id: '22', name: '角度夹爪', parentId: '2' },
        { id: '31', name: '滑台模组', parentId: '3' },
        { id: '32', name: '直线模组', parentId: '3' },
        { id: '41', name: '工业相机', parentId: '4' },
        { id: '42', name: '镜头', parentId: '4' },
        { id: '51', name: '接近传感器', parentId: '5' },
        { id: '52', name: '光电传感器', parentId: '5' }
      ]
    }
  },

  computed: {
    // 自定义选择器配置 - 单tab + 三列布局 + 拖拽排序
    customSelectorOpts() {
      return {
        tabs: [
          {
            id: "fields",
            title: "字段选择",
            type: "list",
            showSelectAll: true,
            searchKeys: ["name"],
            data: [
              { id: '1', name: '商品名称ssssssssssssssssssssssssss拉拉手动阀的', description: '产品的名称信息' },
              { id: '2', name: 'asdfasdfaasfasfdasdfasfasdfasdfadfasdfasfa', description: '产品的唯一编号' },
              { id: '3', name: '商品类别', description: '产品所属分类' },
              { id: '4', name: '库存量', description: '当前库存数量' },
              { id: '5', name: '供应商信息', description: '供应商详细信息' },
              { id: '6', name: '售价', description: '产品销售价格' },
              { id: '7', name: '上架时间', description: '产品上架日期' },
              { id: '8', name: '产品状态', description: '产品当前状态' },
              { id: '9', name: '条码', description: '产品条形码' },
              { id: '10', name: '保质期限', description: '产品保质期' },
              { id: '11', name: '附加说明', description: '产品附加说明' },
              { id: '12', name: '附加说明', description: '产品附加说明' },
              { id: '13', name: '附加说明', description: '产品附加说明' },
              { id: '14', name: '附加说明', description: '产品附加说明' },
              { id: '15', name: '附加说明', description: '产品附加说明' }
            ]
          }
        ],
        maxNum: 0, // 无限制
        single: false,
        // 自定义属性：启用三列布局和拖拽排序
        enableThreeColumns: true,
        enableDragSort: true
      }
    },   
    // 标准选择器配置
    standardSelectorOpts() {
      return {
        tabs: [
          {
            id: "category1",
            title: "一级分类",
            type: "list",
            showSelectAll: true,
            searchKeys: ["name"],
            data: this.categoryData
          },
          {
            id: "category2",
            title: "二级分类",
            type: "list",
            showSelectAll: true,
            searchKeys: ["name"],
            data: this.subCategoryData
          }
        ],
        defaultSelectedItems: {},
        maxNum: 10,
        single: false
      }
    },
      
    // fx-selector-box-v2 配置
    selectorBoxOpts() {
      return {
        tabs: [
          {
            id: "products",
            title: "产品",
            type: "list",
            showSelectAll: true,
            searchKeys: ["name", "code"],
            data: [
              { id: 'p1', name: '步进电机 ST-42', code: 'ST42001', category: '电机' },
              { id: 'p2', name: '伺服电机 SV-60', code: 'SV60001', category: '电机' },
              { id: 'p3', name: '平行夹爪 PG-20', code: 'PG20001', category: '夹爪' },
              { id: 'p4', name: '角度夹爪 AG-30', code: 'AG30001', category: '夹爪' },
              { id: 'p5', name: '滑台模组 SM-100', code: 'SM100001', category: '模组' },
              { id: 'p6', name: '工业相机 IC-500', code: 'IC500001', category: '视觉' }
            ]
          },
          {
            id: "brands",
            title: "品牌",
            type: "list",
            showSelectAll: true,
            searchKeys: ["name"],
            data: [
              { id: 'b1', name: '松下', country: '日本' },
              { id: 'b2', name: '三菱', country: '日本' },
              { id: 'b3', name: '西门子', country: '德国' },
              { id: 'b4', name: '施耐德', country: '法国' },
              { id: 'b5', name: '欧姆龙', country: '日本' }
            ]
          }
        ],
        defaultSelectedItems: {},
        maxNum: 5
      }
    }
  },

  methods: {
    handleMenuIconChange(data) {
      // this.$set(this.form, 'menuIcon', data);
      console.log('handleMenuIconChange', data);      
      if (data) {
          const { type, value } = data;
          if (type === 'list') {
              // this.updateModel({
              //     icon: value.icon
              // });
          } else if (type === 'upload' && value.filter(Boolean).length === 1) {
              const img = value[0];
              const icon = img.response.TempFileName + '.' + img.response.FileExtension;

              // this.updateModel({ icon });
          }
      } else {
          // this.updateModel({ icon: '' });
      }

      // this.$refs.form.validateField('menuIcon');
  },
    // 自定义选择器事件
    onCustomChange(selectedItems) {
      console.log('Custom selector changed:', selectedItems);
    },

    onCustomSortChange(sortedItems) {
      console.log('Custom selector sorted:', sortedItems);
    },

    onCustomConfirm(selectedItems) {
      console.log('Custom selector confirmed:', selectedItems);
    },

    // 标准选择器事件
    onStandardChange(selectedItems) {
      console.log('Standard selector changed:', selectedItems);
      this.standardSelectedItems = selectedItems;
    },

    // 弹窗选择器事件
    onBoxChange(selectedItems) {
      console.log('Box selector changed:', selectedItems);
      this.boxSelectedItems = selectedItems;
    },

    onConfirm(selectedItems) {
      console.log('Box selector confirmed:', selectedItems);
      this.boxSelectedItems = selectedItems;
      this.showSelectorBox = false;
    },

    onCancel() {
      console.log('Box selector cancelled');
      this.showSelectorBox = false;
    }
  }
}
</script>

<style lang="less" scoped>
.selector-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
  }

  .demo-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;

    h3 {
      color: #666;
      margin-bottom: 20px;
      font-size: 16px;
    }

    .result {
      margin-top: 15px;
      padding: 10px;
      background: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 4px;

      p {
        margin: 0;
        font-size: 14px;
        color: #333;
        word-break: break-all;
      }
    }
  }



  .tab-desc {
    font-size: 12px;
    color: #999;
    padding: 5px 0;
  }
}
</style>
