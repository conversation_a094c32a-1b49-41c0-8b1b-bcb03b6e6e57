<template>
  <Designer v-if="isShow" v-bind="options" ref="desinger"></Designer>
</template>
<script>
import Designer from '@/common/widgets/lowcode/Designer.vue';
import Page from './widgets/Page.vue';
import PageFooter from './widgets/PageFooter.vue';
import PageMain from './widgets/PageMain.vue';
import PageHeader from './widgets/PageHeader.vue';
import PageSection from './widgets/page_section';
import PageAside from './widgets/PageAside.vue';
import PageContent from './widgets/page_content';
import SettingsPlugin from './plugins/SettingsPlugin';
// import { generatePageLayout, antiGeneratePageLayout } from './utils';
// import './portal.less';

export default {
  props: {
      // 站点详情数据
      // pageList - 站内页面布局描述  themeLayoutList - 主题布局描述  menuList - 导航布局
      dataInfo: Object
  },
  watch: {
      dataInfo: {
          handler: function (val) {
              this.model = val; // todo model可以删除了
              setTimeout(() => {
                  this.setValue(val);
              }, 200)
          },
          immediate: true
      }
  },
  data() {
      return {
          isShow: false
      }  
  },
  created() {
      Fx.store.getItem('portalsite').then((config = {}) => {
          this.options = {
              plugins: [
                  SettingsPlugin
              ],
              workbench: {
                  type: config?.workbenchType || 'floatingLayer',
                  enableTypeSwitch: true,
                  materials: [{
                      'title': 'basic components',
                      'children': [{
                          'title': 'menu',
                          'name': 'menu',
                          'source': 'paas'
                      }, {
                          'title': 'rich text',
                          'name': 'rich_text_widget',
                          'source': 'paas'
                      }, {
                          'title': 'image',
                          'name': 'bc_image',
                          'source': 'paas'
                      }]
                  }, {
                      'title': 'container components',
                      'children': [{
                          'title': 'grid container',
                          'name': 'grid_row',
                          'source': 'paas'
                      }, {
                          'title': 'tabs container',
                          'name': 'tabs',
                          'source': 'paas'
                      }, {
                          'title': 'test component',
                          'name': 'test',
                          'source': 'paasxt'
                      }]
                  }, {
                      'title': 'custom components',
                      'children': []
                  }]
              },
              resolver: {
                  'page': Page,
                  'page_header': PageHeader,
                  'page_main': PageMain,
                  'page_footer': PageFooter,
                  'page_section': PageSection,
                  'page_aside': PageAside,
                  'page_content': PageContent
              }
          }
          this.isShow = true;
      })
  },
  components: {
      Designer
  },
  methods: {
      active(pageApiName) {
          if (this.curPageApiName === pageApiName) {
              return;
          }

          setTimeout(() => {
              const { pageList, themeLayoutList } = this.model;
              const pageLayout = pageList.find(item => item.apiName === pageApiName);

              if (pageLayout) {
                  this.curPageApiName = pageApiName;
                  this.$refs.desinger?.setImport(generatePageLayout(pageLayout, themeLayoutList));
              }
          }, 200)
      },

      // 独立站点设计器步骤条容器需要调用的公共接口
      validData() {
          return true;
      },

      getValue() {
          let result = {};

          this._useDesigner((designer) => {
              const nodes = designer.getNodes();
              if (Object.keys(nodes).length) {
                  Object.assign(result, antiGeneratePageLayout(nodes));
              }

              result.menuList = editor.query.getOptions().menuList;
              result.themeLayoutList = editor.query.getOptions().themeLayoutList;
              result.pageList = editor.query.getOptions().pageList;
          })

          return result;
      },

      setValue(data) {
          this._useDesigner(designer => {
              designer.setOptions(options => {
                  // 同步model到低代码设计器当中
                  options.pageList = data.pageList;
                  options.menuList = data.menuList;
                  options.themeLayoutList = data.themeLayoutList;
                  options.siteInfo = data.siteInfo;
              }, false);
          });
      },

      _useDesigner(callback) {
          const designer = window.aaaaa = this.$refs.desinger;
          if (designer) {
              callback && callback(designer);
          } else {
              // throw new Error('');
          }
      }
  },

  beforeDestroy() {
      this._useDesigner((designer) => {
          const workbenchType = editor.query.getOptions().workbench.type;
          Fx.store.setItem('portalsite', {
              workbenchType
          });
      })
  }
}
</script>
<style lang="less" scoped></style>
