<div class="dht-card-base-content">
  <div
    class="dht-card-item-img"
    :class="{'dht-card-item-img-default': isDefaultImg}"
    @click="onAction('DETAIL', product)">
    <fx-image
      v-if="options.isLazyImg"
      class="img"
      :src="pictureUrl"
      fit="contain"
      :scroll-container="options.scrollContainer"
      @click.stop="onAction('DETAIL', product)"
      :lazy="true">
      <div slot="error" class="dht-lazy-load-error dht-absolute-center">
        <i class="el-icon-picture-outline"></i>
      </div>
    </fx-image>
    <img v-else :src="pictureUrl" class="img">
  </div>
  <div class="dht-card-item-info">
    <div class="item item-name">{{product.display_name || product.name__r || product.name}}</div>
    <div class="item item-price">
      <span class="price-prefix">{{product.mc_currency__r || defaultCurrencyFlag}}</span>{{displayPrice}}<span
      class="dht-price-unit" v-if="isShowPriceUnit">/{{priceUnitName}}</span>
      <span class="stock-info" v-if="options.isShowStock">{{stockText}}</span>
    </div>
    <div class="item item-tag">
      <product-tag
        v-if="product.hasPricePolicy"
        :style="{color: '#ff8000'}">
        {{ $t('促销') }}
      </product-tag>
      <product-tag
        v-for="option in commodityLabels"
        :key="option.value"
        :style="{color: option.font_color}">
        {{option.label}}
      </product-tag>
    </div>
  </div>
  <div class="dht-left-tag" v-if="isNew">
    <span class="dht-left-tag-text" style="background-color: #85D331">{{ $t('新') }}</span>
    <i class="dht-left-tag-icon" style="background-color: #85D331"></i>
  </div>
</div>

