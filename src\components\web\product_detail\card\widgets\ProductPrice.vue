<template>
  <div class="dht-product-detail-price-wrapper">
    <div class="dht-product-detail-main-item">
      <div class="dht-product-detail-main-label">{{ $t('价格') }}</div>
      <div class="dht-product-detail-main-content dht-price-content-wrapper">
        <div class="price-flag">￥</div>
        <div class="price-content">
          <div>39</div>
          <div class="dht-price-decimal-point">.90</div>
        </div>
        <div v-if="!cPrice" class="dht-between-line">~</div>
        <div v-if="!cPrice" class="price-content">              
          <div>139</div>
          <div class="dht-price-decimal-point">.90</div>
        </div>
        <div class="dht-price-normal-wrap">
          <div v-if="cPrice" class="dht-price-normal">￥49.90</div>
          <div v-else class="dht-price-normal">¥22.50~45.00</div>
        </div>              
      </div>            
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductPrice',
  props: {
    price: {
      type: String,
      default: '1'
    }
  },
  computed: {
    cPrice() {
      return this.price === '1';
    }
  }
}
</script>

<style lang="less" scoped>
@price-wrapper-bg-color: #F7F8FA;

.dht-product-detail {
  &-price-wrapper {
    padding: 16px;
    background-color: @price-wrapper-bg-color;
    .dht-price-content-wrapper {
      display: flex;
      align-items: baseline;
    }
    .price-content {
      display: flex;
      align-items: baseline;
      color: #333333;
      font-size: 28px;
      font-weight: 600;
      font-family: PingFangSC-Semibold;
    }

    .dht-between-line {
      position: relative;
      top: -3px;  
      padding: 0 2px 0 4px;
      font-size: 18px;
    }
    .price-flag {
      font-size: 16px;
      font-weight: 600;
    }

    .dht-price-normal {
      padding-left: 16px;
      font-size: 14px;
      color: #91959E;
      font-weight: 400;
      text-decoration-line: line-through;
    }
  }
}

.dht-price-decimal-point {
  font-size: 16px;
  font-weight: 600;
}
</style> 