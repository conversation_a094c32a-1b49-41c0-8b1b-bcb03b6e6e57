<template>
  <div class="fx-object-input-autocomplete-container">
    <fx-autocomplete
      class="fx-object-input-autocomplete"
      v-model="showVal"
      :size="size"
      :fetch-suggestions="querySearch"
      :placeholder="$t('i18n.dhtbiz.common.please_input'/*请输入内容*/)"      
      :class="{ 'is-error': hasError }"
      @select="handleSelect"
      @blur="handleBlur"
    >
      <i slot="suffix" class="el-input__icon fx-icon-add-2" @click.stop.prevent="handlePickDate"></i>
    </fx-autocomplete>
    <div v-if="hasError" class="error-tip">
      <i class="fx-icon-warning"></i>
      {{ $t('i18n.dhtbiz.common.no_match_data'/*未找到完全匹配的数据，点+进行查找*/) }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'fx-object-input-autocomplete',
  props: {
    objectApiName: {
      type: String,
      default: 'AccountObj'
    },
    value: {
      type: Object,
      default: () => ({})
    },
    size: {
      type: String,
      default: 'small'
    }
  },
  data() {
    return {
      inputValue: {},
      showVal: '',
      objDataList: [],
      hasError: false,
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.setShowVal(newVal);
      },
      deep: true
    }
  },
  mounted() {
    this.querySearch(this.value.name, (list) => {
      // 当value只有name而没有_id时，需要给value添加_id
      const item = list.find(item => item.name === this.value.name);
      if (!this.value._id && item) {
        this.setShowVal(item);
      } else {        
        this.setShowVal(this.value);
      }
      this.checkError();
    });
  },
  methods: {
    setShowVal(val) {
      val.value = val.display_name || val.name;
      this.showVal = val.value;
      this.inputValue = val;
      this.checkError();
    },
    checkError() {
      if (this.showVal) {
        const index = this.objDataList.findIndex(item => item._id === this.inputValue._id && item.value === this.showVal);
        if (index === -1) {
          this.showError();
        } else {
          this.clearError();  
        }
      } else {
        this.clearError();
      }
    },
    showError() {
      this.hasError = true;
    },
    clearError() {
      this.hasError = false;
    },
    handleBlur() {
      console.log('handleBlur');
      this.checkError();
      if(this.hasError) {
        this.inputValue = {
          name: this.showVal,
          value: this.showVal,
        };
      }
    },
    querySearch(queryString, cb) {
      const me = this;
      const params = {
        associated_object_describe_api_name: this.objectApiName,
        search_query_info: `{"limit":10,"offset":0,"filters":[{"field_name":"name","field_values":["${queryString}"],"operator":"LIKE"}]}`,
        include_describe: false,
        include_layout: false,
        client: 'web',
      };
      CRM.util.FHHApi({
          url: `/EM1HNCRM/API/v1/object/${this.objectApiName}/controller/RelatedList`,
          data: params,
          success(res) {
            if (res.Result.StatusCode === 0) {
              // console.log(res.Value);
              const list = res.Value.dataList.map(item => {
                item.value = item.display_name || item.name;
                return item;
              })
              me.objDataList = list;
              cb(list)
            } else {
              // resolve({});
              cb([])
            }
          }
      });
    },
    handlePickDate() {
      // console.log('handlePickDate');
      const me = this;
      const data = [];
      if(this.inputValue._id) {
        data.push({_id: this.inputValue._id, name: this.inputValue.name});
      }
      CRM.api.pick_data({
          apiName: this.objectApiName,
          data,
          single: true,
          hideAdd: true,
          methods: {
              select: function(res) {
                  console.log('pick data selected', res);
                  const npro = res.selected;
                  if(npro) {
                    npro.value = npro.display_name || npro.name;
                    me.objDataList = [npro];
                    me.setShowVal(npro);                    
                  }
              }
          }
      });
    },
    handleSelect(item) {
      // console.log(item);
      this.setShowVal(item);
    },
    getInputValue() {
      return this.inputValue;
    }
  },
};
</script>

<style lang="less" scoped>
.fx-object-input-autocomplete-container {
  display: flex;
  flex-direction: column;
  .fx-object-input-autocomplete.is-error {
    /deep/ .el-input__inner {
      border-color: #f56c6c;
    }
  }
  .error-tip {
    color: #f56c6c;
    font-size: 14px;
    margin-top: 4px;
    display: flex;
    align-items: center;
    .fx-icon-warning {
      font-size: 16px;
      margin-right: 4px;
      color: #f56c6c;
    }
  }
}
</style>
