<template>
  <div class="dht-product-detail-rich-text" v-if="spuInfo.description">
    <div class="dht-product-detail-rich-text-header">{{ $t('图文详情') }}</div>
    <div class="dht-product-detail-meta dht-product-detail-img-html" v-html="imgHtml"></div>
  </div>
</template>
<script>
export default {
  name: 'dht_web_product_detail_rich_text',
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      imgHtml: ''
    }
  },
  computed: {
    isSpuMode() {
      return this.data.isSpuMode;
    },
    product() {  
      let { skus, spuData, isSpuMode } = this.data.pageData || {};
      return isSpuMode ? spuData : skus[0];
    }, 
  },
  methods: {
    /**
 * 转化图文详情
 * @param des
 */
    convertImgHtml(des) {
      let description = des;
      const isCustomDomain = Fx.util.isCustomDomain();
      if (description) {
        const regExp = /(<img.*?src=")(.*?)(".*?\/?>)/g;
        // https://www.ceshi115.com/FSC/EM/File/ViewTempImg?TempFileName=N_202412_05_172b278f124641fdb140482b93086633

        description = description.replace(regExp,
          (match, p1, p2, p3) => {
            let sep = '/';
            // 线上环境, 非112域名, 图片地址中的appId用 &appid= 拼接
            if (isCustomDomain && p2.includes('ViewTempImg?TempFileName=')) {
              sep = '&appid=';
            }
            const replaceStr = `${p2.replace('/i/', '/o/')}${sep}FSAID_11490c84`;
            return `${p1}${replaceStr}${p3}`;
          });
      }
      return description || '';
    }
  },
  mounted() {
    // 获取图文详情
    let des = this.product.description;
    this.imgHtml = this.convertImgHtml(des);
  }
}

</script>
<style lang="less"></style>
