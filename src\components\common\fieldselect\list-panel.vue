<template>
  <div class="list-panel">
    <!-- 全选按钮 -->
    <div class="select-all-section" v-if="tabData.showSelectAll">
      <fx-checkbox
        :value="isAllSelected"
        :indeterminate="isIndeterminate"
        @change="onSelectAllChange"
      >
        {{ isAllSelected ? '取消全选' : '全选' }}
      </fx-checkbox>
    </div>
    
    <!-- 列表内容 -->
    <fx-scrollbar class="list-scrollbar">
      <div 
        :class="[
          'list-content',
          { 'three-columns': enableThreeColumns }
        ]"
      >
        <div
          v-for="item in filteredData"
          :key="item.id"
          :class="[
            'list-item',
            { 'selected': isItemSelected(item.id) }
          ]"
          @click="onItemClick(item)"
        >
          <fx-checkbox
            :value="isItemSelected(item.id)"
            @click.native.stop
            @change="() => onItemClick(item)"
          />
          <div class="item-content">
            <div class="item-name" :title="item.name">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </fx-scrollbar>
  </div>
</template>

<script>
export default {
  name: 'ListPanel',
  props: {
    tabData: {
      type: Object,
      required: true
    },
    searchKeyword: {
      type: String,
      default: ''
    },
    enableThreeColumns: {
      type: Boolean,
      default: false
    },
    selectedItems: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    filteredData() {
      if (!this.searchKeyword) {
        return this.tabData.data || []
      }
      
      const keyword = this.searchKeyword.toLowerCase()
      return (this.tabData.data || []).filter(item => {
        return item.name.toLowerCase().includes(keyword) ||
               (item.description && item.description.toLowerCase().includes(keyword))
      })
    },
    
    isAllSelected() {
      if (!this.filteredData.length) return false
      return this.filteredData.every(item => this.isItemSelected(item.id))
    },
    
    isIndeterminate() {
      const selectedCount = this.filteredData.filter(item => this.isItemSelected(item.id)).length
      return selectedCount > 0 && selectedCount < this.filteredData.length
    }
  },
  methods: {
    isItemSelected(itemId) {
      return this.selectedItems.includes(itemId)
    },
    
    onItemClick(item) {
      this.$emit('item-click', item, this.tabData.id)
    },
    
    onSelectAllChange(isSelectAll) {
      this.$emit('select-all', this.tabData.id, isSelectAll)
    }
  }
}
</script>

<style lang="less" scoped>
.list-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.select-all-section {
  padding: 0 16px 0 12px;
}

.list-scrollbar {
  flex: 1;
  height: 0;
}

.list-content {
  padding: 0;

  &.three-columns {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    min-width: 0;
  }
}

.list-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.three-columns .list-item {
  flex-direction: row;
  align-items: center;
  min-width: 0;
}

.item-content {
  flex: 1;
  margin-left: 8px;
  min-width: 0;
}



.item-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


</style>
