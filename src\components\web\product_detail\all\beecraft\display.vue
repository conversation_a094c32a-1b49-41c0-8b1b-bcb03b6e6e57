<template>
  <div class="dhtbiz dht-product-detail-card-beecraft-display">
    <card v-bind="{...$attrs, ...$props}" v-bind:objectContext="objectContext" />
    <key-info v-bind="{...$attrs, ...$props}"  v-bind:objectContext="objectContext" />
    <rich-text v-bind="{...$attrs, ...$props}" v-bind:objectContext="objectContext" />
  </div>
</template>

<script>
import { dhtBizModel } from '../../../utils/model';

import Card from '../../card/card.vue';
import KeyInfo from '../../key_info/beecraft/display.vue';
import RichText from '../../rich_text/beecraft/display.vue';

export default {
  name: 'ProductDetailAllBeecraftDisplay',
  inject: ['useInternalNode'],
  components: {
    Card,
    KeyInfo,
    RichText
  },
  props: {
    img: {
      type: String,
      default: '1'
    },
    tag: {
      type: String,
      default: '1'
    },
    price: {
      type: String,
      default: '1'
    },
    attr: {
      type: String,
      default: '0'
    },
    stock: {
      type: String,
      default: '1'
    },
    // name: {
    //   type: String,
    //   default: '',
    // },
    showType: {
      type: String,
      default: '1',
    },
    selectedFields: {
      type: Array,
      default: () => [],
    }
  },
  data() {
    return {
    }
  },
  created() {
    // console.log('objFields', this.objFields);
  },
  methods: {
    async init() {      
    },
    mockData() {}
  },
  mounted() {
    // this.mockData();
  }
}
</script>
