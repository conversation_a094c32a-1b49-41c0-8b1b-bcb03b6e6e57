"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[905,82,748],{2064:(t,e,n)=>{n.r(e),n.d(e,{default:()=>h});var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dhtbiz dht-product-detail-card-beecraft-display"},[n("card",t._b({attrs:{objectContext:t.objectContext}},"card",Object.assign({},t.$attrs,t.$props),!1)),n("key-info",t._b({attrs:{objectContext:t.objectContext}},"key-info",Object.assign({},t.$attrs,t.$props),!1)),n("rich-text",t._b({attrs:{objectContext:t.objectContext}},"rich-text",Object.assign({},t.$attrs,t.$props),!1))],1)};a._withStripped=!0;var i=n(5861),s=n(4687),r=n.n(s),c=(n(1141),n(4773)),d=n(7773),l=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dht-product-detail-rich-text"},[n("div",{staticClass:"dht-title"},[t._v(t._s(t.$t("图文详情")))]),n("div",{staticClass:"dht-content"},[t._v("\n    "+t._s(t.$t("dht.component.web.product_detail_rich_text.description","富文本字段description中的图片内容"))+"      \n  ")])])};l._withStripped=!0;const o={name:"dht_web_product_detail_rich_text",props:{data:{type:Object,default:function(){return{}}}},data:function(){return{imgHtml:""}},computed:{isSpuMode:function(){}},methods:{},mounted:function(){}};var u=n(1900);const p=(0,u.Z)(o,l,[],!1,null,"e87778c2",null).exports,f={name:"ProductDetailAllBeecraftDisplay",inject:["useInternalNode"],components:{Card:c.default,KeyInfo:d.default,RichText:p},props:{img:{type:String,default:"1"},tag:{type:String,default:"1"},price:{type:String,default:"1"},attr:{type:String,default:"0"},stock:{type:String,default:"1"},showType:{type:String,default:"1"},selectedFields:{type:Array,default:function(){return[]}}},data:function(){return{}},created:function(){},methods:{init:function(){return(0,i.Z)(r().mark((function t(){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})))()},mockData:function(){}},mounted:function(){}};const h=(0,u.Z)(f,a,[],!1,null,null,null).exports},7773:(t,e,n)=>{n.r(e),n.d(e,{default:()=>l});var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dht-product-detail-key-info-display"},[n("div",{staticClass:"dht-title"},[t._v(t._s(t.$t("dht.component.web.product_detail_key_info.title","参数信息")))]),n("div",{staticClass:"dht-content"},t._l(t.cFields,(function(e,a){return n("div",{key:e.key,staticClass:"dht-item",class:{"dht-double":"2"===t.showType}},[n("div",{staticClass:"dht-field"},[n("span",{staticClass:"dht-text dht-name"},[t._v(t._s(e.name))]),n("span",{staticClass:"dht-text dht-val"},[t._v(t._s(e.val||"--"))])])])})),0)])};a._withStripped=!0;var i=n(5861),s=n(4687),r=n.n(s),c=n(1141);const d={inject:["useInternalNode"],props:{objectContext:{type:Object,default:function(){return{}}},showType:{type:String,default:"1"},selectedFields:{type:Array,default:function(){return["name"]}}},computed:{cFields:function(){var t=this.selectedFields||[],e=this.allFields;return t.map((function(t){var n=e[t]||{};return{key:t,name:n.label_r||n.label||"--",val:"--"}}))}},data:function(){return{allFields:[]}},methods:{init:function(){var t=this;return(0,i.Z)(r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,c.Z.fetchObjFields(c.Z.mainObjApiName());case 2:t.allFields=e.sent;case 3:case"end":return e.stop()}}),e)})))()}},created:function(){this.init()}};const l=(0,n(1900).Z)(d,a,[],!1,null,"861248b0",null).exports}}]);