<template>
  <div class="dht-product-detail-simple-cpq-setting" >
      <div class="dht-name">
        {{$t('dht.component.app.product_detail_simple_cpq')}}
      </div>      
  </div>
</template>
<script>
import { dhtBizModel } from '../../utils/model';

export default {
  inject: {
      setProps: {
          from: 'setProps',
          default() {
              return () => { };
          }
      }
  },
  components: {        
  },
  data() {
      return {
          
      };
  },
  computed: {
      mainObjApiName() {
          return dhtBizModel.isSpuMode() ? 'SPUObj' : 'ProductObj';
      }
  },
  methods: {
                
  },
  created() {
      
  },
  mounted () {
  },
};
</script>
<style lang="less">
.dht-product-detail-simple-cpq-setting {
  width: 100%;    
  .dht-name {
    font-size: 16px;
    color:#181C25;
  }
}
</style>
