import { Field, Product } from '../types';

export interface Options {
  // 是否需要懒加载
  isLazyImg?: boolean;
  // 懒加载的父滚动元素，仅在需要懒加载的时候设置
  scrollContainer?: string;
  // 是否显示库存
  isShowStock?: boolean;
  // 是否模糊显示库存
  visibleType?: '2' | '3';
  // 支持客开传入的 自定义商品卡片组件
  pwcCard?: Record<string, any>;
}

export type ActionType = 'CART' | 'SPEC' | 'COLLECTION' | 'DETAIL' | 'BOM' | 'ATTR';

export type ProductKeys = keyof Product;

export type ProductFieldsMap = Record<ProductKeys, Field>;

export interface CardMainInfo {
  tag_apiname: any;
  picture_apiname: ProductKeys;
  name_apiname: ProductKeys;
  price_apiname: ProductKeys;
  show_fields?: ProductKeys[];
}

export interface ProductConfig {
  card_main_info: CardMainInfo;
  title: string;
  header: string;
  is_card_init: boolean;
  is_spu_mode: boolean;
}
