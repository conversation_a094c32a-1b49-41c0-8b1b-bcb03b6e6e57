import DhtAiOrder from '@/components/common/dhtAiOrder/index.vue';

let dhtAiOrderInstance = null;

/**
 * 显示DHT AI订单组件
 * @param {Object} options - 配置选项
 * @param {boolean} [options.visible=true] - 是否显示组件
 * @returns {Object} 返回组件实例及其控制方法
 */
export function showDhtAiOrder(options = {}) {
  // 如果实例已存在，直接显示
  if (dhtAiOrderInstance) {
    dhtAiOrderInstance.visible = options.visible ?? true;
    return dhtAiOrderInstance;
  }

  // 创建组件构造函数
  const DhtAiOrderConstructor = Vue.extend(DhtAiOrder);
  
  // 创建组件实例
  dhtAiOrderInstance = new DhtAiOrderConstructor({
    propsData: {
      visible: options.visible ?? true
    }
  });

  // 监听关闭事件
  dhtAiOrderInstance.$on('close', () => {
    dhtAiOrderInstance.hide();
  });      

  // 挂载组件
  dhtAiOrderInstance.$mount();
  document.body.appendChild(dhtAiOrderInstance.$el);

  // 扩展实例方法
  Object.assign(dhtAiOrderInstance, {
    /**
     * 隐藏组件
     */
    hide() {
      if (dhtAiOrderInstance) {
        dhtAiOrderInstance.visible = false;
      }
    },

    /**
     * 显示组件
     */
    show() {
      if (dhtAiOrderInstance) {
        dhtAiOrderInstance.visible = true;
      }
    },

    /**
     * 销毁组件
     */
    destroy() {
      if (!dhtAiOrderInstance) return;
      
      dhtAiOrderInstance.visible = false;
      dhtAiOrderInstance.$destroy();
      dhtAiOrderInstance.$el.remove();
      dhtAiOrderInstance = null;
    }
  });
  
  return dhtAiOrderInstance;
}

/**
 * 销毁组件实例
 */
function destroyInstance() {
  if (!dhtAiOrderInstance) return;
  
  dhtAiOrderInstance.visible = false;
  dhtAiOrderInstance.$destroy();
  dhtAiOrderInstance.$el.remove();
  dhtAiOrderInstance = null;
}

/**
 * 隐藏DHT AI订单组件
 */
export function hideDhtAiOrder() {
  if (dhtAiOrderInstance) {
    dhtAiOrderInstance.visible = false;
  }
}

/**
 * 销毁DHT AI订单组件
 */
export function destroyDhtAiOrder() {
  destroyInstance();
}