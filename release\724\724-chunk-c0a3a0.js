"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[724],{4724:(t,s,e)=>{e.r(s),e.d(s,{default:()=>h});var i=function(){var t=this.$createElement;return(this._self._c||t)("div",{staticClass:"dhtbiz-product-list",staticStyle:{width:"100%",height:"700px"}})};i._withStripped=!0;var l=e(885),r=e(5927),a=(0,r.XC)("crm-modules/page/shopmall/shopmall"),n=(0,r.Yc)("crm-assets/style/all.css"),o=(0,r.Yc)("crm-assets/style/page.css");const c={name:"dht_web_product_list_all",props:{apiname:{type:String,default:"ShopMall"}},methods:{initTable:function(){var t=this;Promise.all([(0,r.ii)(),n,o,a]).then((function(s){var e=(0,l.Z)(s,4),i=(e[0],e[1],e[2],e[3]);t.isDestroy||$(".dhtbiz-product-list")[0]&&(t.$list=new i({wrapper:$(".dhtbiz-product-list"),apiname:t.apiname}),t.$once("hook:beforeDestroy",(function(){t.$List&&t.$list.destroy(),t.$list=null})),t.$list.render())}))}},created:function(){this.initTable()},beforeDestroy:function(){this.isDestroy=!0,this.$list&&this.$list.destroy(),this.$list=null}};const h=(0,e(1900).Z)(c,i,[],!1,null,"c47375b0",null).exports}}]);