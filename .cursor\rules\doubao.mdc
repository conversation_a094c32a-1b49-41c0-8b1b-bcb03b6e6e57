---
description: 
globs: 
alwaysApply: false
---
使用 chrome-mcp-server 中的tools 自动完成以下操作: 
请在豆包网页（https://www.doubao.com/chat/）中，自动完成以下操作：

# 豆包AI问答助手工作流程

## 1. 导航到豆包网站并等待页面完全加载
```javascript
// 首先导航到豆包网站
mcp_chrome-mcp-server_chrome_navigate({
  url: "https://www.doubao.com/chat/"
});

// 等待页面加载完成（最多等待10秒）
let pageLoaded = false;
let retryCount = 0;
while (!pageLoaded && retryCount < 5) {
  try {
    const content = mcp_chrome-mcp-server_chrome_get_web_content({
      textContent: true
    });
    
    // 检查页面是否包含输入框或其他关键元素
    if (content.data.content[0].text.includes("豆包") && 
        content.data.content[0].text.includes("发消息")) {
      pageLoaded = true;
    } else {
      // 等待2秒后重试
      await new Promise(resolve => setTimeout(resolve, 2000));
      retryCount++;
    }
  } catch (error) {
    // 等待2秒后重试
    await new Promise(resolve => setTimeout(resolve, 2000));
    retryCount++;
  }
}

if (!pageLoaded) {
  return "无法加载豆包网站，请检查网络连接或手动打开豆包网站。";
}
```

## 2. 在输入框中输入问题并发送
```javascript
// 查找输入框
try {
  // 填入用户问题
  mcp_chrome-mcp-server_chrome_fill_or_select({
    selector: "[data-testid='chat_input_input']",
    value: "{用户输入的问题}"
  });
  
  // 发送问题（按回车键）
  mcp_chrome-mcp-server_chrome_keyboard({
    keys: "Enter"
  });
} catch (error) {
  // 如果找不到指定的输入框，尝试其他选择器
  try {
    const elements = mcp_chrome-mcp-server_chrome_get_interactive_elements({
      selector: "[data-testid='chat_input_send_button']",
      // textQuery: "发消息"
    });
    
    // 找到输入框并填入问题
    if (elements && elements.data.content[0].text) {
      const parsedData = JSON.parse(elements.data.content[0].text);
      if (parsedData.elements && parsedData.elements.length > 0) {
        // 找到第一个textarea类型的元素
        const textareaElement = parsedData.elements.find(el => el.type === "textarea");
        if (textareaElement) {
          mcp_chrome-mcp-server_chrome_fill_or_select({
            selector: textareaElement.selector,
            value: "{用户输入的问题}"
          });
          
          mcp_chrome-mcp-server_chrome_keyboard({
            keys: "Enter"
          });
        }
      }
    }
  } catch (innerError) {
    return "无法找到输入框或发送问题，请检查网页结构是否发生变化。";
  }
}
```

## 3. 等待AI回复加载完成
```javascript
// 等待AI回复（最多等待30秒）
let responseReceived = false;
let waitCount = 0;
let originalTitle = "";
let currentTitle = "";

// 首先获取原始标题
try {
  const initialContent = mcp_chrome-mcp-server_chrome_get_web_content({
    textContent: true
  });
  if (initialContent && initialContent.data.content[0].text) {
    const parsedData = JSON.parse(initialContent.data.content[0].text);
    originalTitle = parsedData.title || "";
  }
} catch (error) {
  // 忽略错误，继续执行
}

// 等待标题变化或回复内容出现
while (!responseReceived && waitCount < 15) {
  try {
    // 等待2秒
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 检查页面内容
    const content = mcp_chrome-mcp-server_chrome_get_web_content({
      textContent: true
    });
    
    if (content && content.data.content[0].text) {
      const parsedData = JSON.parse(content.data.content[0].text);
      currentTitle = parsedData.title || "";
      
      // 检查标题是否已经变化（通常表示回复已完成）
      if (originalTitle && currentTitle && originalTitle !== currentTitle && 
          currentTitle !== "新对话 - 豆包" && currentTitle !== "豆包 - 字节跳动旗下 AI 智能助手") {
        responseReceived = true;
        continue;
      }
      
      // 检查页面内容是否包含回复
      if (parsedData.textContent && 
          (parsedData.textContent.includes("message_text_content") || 
           parsedData.textContent.includes("flow-markdown-body"))) {
        responseReceived = true;
        continue;
      }
    }
    
    waitCount++;
  } catch (error) {
    waitCount++;
  }
}

if (!responseReceived) {
  return "等待AI回复超时，请检查网络连接或手动查看回复。";
}

// 额外等待2秒，确保回复完全加载
await new Promise(resolve => setTimeout(resolve, 2000));
```

## 4. 提取AI回复内容
```javascript
// 尝试多种方法提取AI回复内容
let aiResponse = null;

// 方法1：使用message_text_content选择器
try {
  const response = mcp_chrome-mcp-server_chrome_get_web_content({
    selector: "[data-testid='message_text_content']",
    htmlContent: true
  });
  
  if (response && !response.isError && response.data.content[0].text) {
    const htmlContent = JSON.parse(response.data.content[0].text).htmlContent;
    if (htmlContent && htmlContent.includes("message_text_content")) {
      aiResponse = htmlContent;
    }
  }
} catch (error) {
  // 忽略错误，尝试下一种方法
}

// 方法2：使用flow-markdown-body选择器
if (!aiResponse) {
  try {
    const response = mcp_chrome-mcp-server_chrome_get_web_content({
      selector: "[theme-mode='light'].flow-markdown-body",
      htmlContent: true
    });
    
    if (response && !response.isError && response.data.content[0].text) {
      const htmlContent = JSON.parse(response.data.content[0].text).htmlContent;
      if (htmlContent && htmlContent.includes("flow-markdown-body")) {
        aiResponse = htmlContent;
      }
    }
  } catch (error) {
    // 忽略错误，尝试下一种方法
  }
}

// 方法3：获取整个页面内容并解析
if (!aiResponse) {
  try {
    const response = mcp_chrome-mcp-server_chrome_get_web_content({
      htmlContent: true
    });
    
    if (response && !response.isError && response.data.content[0].text) {
      const htmlContent = JSON.parse(response.data.content[0].text).htmlContent;
      
      // 从整个HTML中提取回复内容
      // 这里可以使用正则表达式或其他方法提取回复内容
      const markdownBodyMatch = htmlContent.match(/<div[^>]*class="[^"]*flow-markdown-body[^"]*"[^>]*>([\s\S]*?)<\/div>/i);
      if (markdownBodyMatch && markdownBodyMatch[1]) {
        aiResponse = markdownBodyMatch[0];
      }
    }
  } catch (error) {
    // 忽略错误
  }
}

if (!aiResponse) {
  return "无法提取AI回复内容，请手动查看回复。";
}
```

## 5. 将AI回复内容转换为Markdown格式
```javascript
// 将HTML转换为Markdown
function htmlToMarkdown(html) {
  // 移除HTML标签，保留文本内容和基本格式
  let markdown = html;
  
  // 移除思考过程部分
  markdown = markdown.replace(/<div[^>]*think-collapse-block[^>]*>[\s\S]*?<\/div>/gi, '');
  
  // 替换标题标签
  markdown = markdown.replace(/<h1[^>]*>([\s\S]*?)<\/h1>/gi, '# $1\n\n');
  markdown = markdown.replace(/<h2[^>]*>([\s\S]*?)<\/h2>/gi, '## $1\n\n');
  markdown = markdown.replace(/<h3[^>]*>([\s\S]*?)<\/h3>/gi, '### $1\n\n');
  markdown = markdown.replace(/<h4[^>]*>([\s\S]*?)<\/h4>/gi, '#### $1\n\n');
  
  // 替换段落标签
  markdown = markdown.replace(/<div[^>]*paragraph[^>]*>([\s\S]*?)<\/div>/gi, '$1\n\n');
  markdown = markdown.replace(/<p[^>]*>([\s\S]*?)<\/p>/gi, '$1\n\n');
  
  // 替换列表标签
  markdown = markdown.replace(/<ul[^>]*>([\s\S]*?)<\/ul>/gi, '$1\n');
  markdown = markdown.replace(/<li[^>]*>([\s\S]*?)<\/li>/gi, '- $1\n');
  
  // 替换加粗标签
  markdown = markdown.replace(/<strong[^>]*>([\s\S]*?)<\/strong>/gi, '**$1**');
  
  // 替换换行标签
  markdown = markdown.replace(/<br[^>]*>/gi, '\n');
  
  // 移除其他HTML标签
  markdown = markdown.replace(/<[^>]*>/g, '');
  
  // 清理多余的空行
  markdown = markdown.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  // 解码HTML实体
  markdown = markdown.replace(/&lt;/g, '<');
  markdown = markdown.replace(/&gt;/g, '>');
  markdown = markdown.replace(/&amp;/g, '&');
  markdown = markdown.replace(/&quot;/g, '"');
  markdown = markdown.replace(/&#39;/g, "'");
  
  return markdown.trim();
}

// 转换并返回结果
const markdownResponse = htmlToMarkdown(aiResponse);
return markdownResponse;
```

注意事项：
- 每个步骤添加错误处理和重试机制
- 如果页面结构发生变化，尝试多种选择器定位元素
- 在等待过程中使用适当的时间间隔，避免过于频繁的请求



我的问题是：{用户输入的问题}