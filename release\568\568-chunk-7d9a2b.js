"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[568,452],{2174:(e,i,n)=>{n.r(i),n.d(i,{destroyDhtAiOrder:()=>r,hideDhtAiOrder:()=>s,showDhtAiOrder:()=>o});var l=n(1819),t=null;function o(){var e,i,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(t)return t.visible=null===(i=n.visible)||void 0===i||i,t;var o=Vue.extend(l.default);return(t=new o({propsData:{visible:null===(e=n.visible)||void 0===e||e}})).$on("close",(function(){t.hide()})),t.$mount(),document.body.appendChild(t.$el),Object.assign(t,{hide:function(){t&&(t.visible=!1)},show:function(){t&&(t.visible=!0)},destroy:function(){t&&(t.visible=!1,t.$destroy(),t.$el.remove(),t=null)}}),t}function s(){t&&(t.visible=!1)}function r(){t&&(t.visible=!1,t.$destroy(),t.$el.remove(),t=null)}}}]);