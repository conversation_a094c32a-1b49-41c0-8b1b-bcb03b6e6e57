<template>
  <div v-if="img" class="custom-preview">
    <div class="preview-container">
      <img src="https://a9.fspage.com/FSR/weex/avatar/object_list/images/list_default_icon.png">
      <div class="preview-overlay">
        <span class="zoom-icon">🔍</span>
      </div>
    </div>
    <div class="preview-thumbnails">
      <div class="thumbnail active"></div>
      <div class="thumbnail"></div>
      <div class="thumbnail"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomPreview',
  props: {
    img: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
.custom-preview {
  width: 360px;
  margin-right: 18px;
}

.preview-container {
  position: relative;
  width: 360px;
  height: 360px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.preview-container img {
  max-width: 100%;
  max-height: 100%;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.03);
  opacity: 0;
  transition: opacity 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-container:hover .preview-overlay {
  opacity: 1;
}

.zoom-icon {
  font-size: 24px;
  color: #666;
}

.preview-thumbnails {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.thumbnail {
  width: 60px;
  height: 60px;
  background: #eee;
  border: 2px solid transparent;
  cursor: pointer;
}

.thumbnail.active {
  border-color: #409EFF;
}
</style> 