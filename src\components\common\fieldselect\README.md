# FieldSelector 字段选择器

一个功能强大的字段选择器组件，支持输入框样式触发、弹窗选择、多标签页、三列布局、拖拽排序等功能。

## 功能特性

- 🎯 **输入框样式** - 类似 fx-input 的外观，点击触发弹窗
- 🏷️ **标签显示** - 已选项以标签形式展示，支持删除
- 📑 **多标签页** - 支持多个数据源分类选择
- 📋 **三列布局** - 可选的三列网格布局显示选项
- 🔄 **拖拽排序** - 已选项支持拖拽重新排序
- 🔍 **搜索过滤** - 内置搜索功能快速定位选项
- ✅ **全选功能** - 支持单个标签页全选/取消全选
- 📱 **响应式** - 适配不同屏幕尺寸

## 快速使用

```vue
<template>
  <field-selector
    v-model="selectedItemsList"
    :selector-options="selectorOptions"
    :max-display-count="5"
    placeholder="请选择字段"
    title="字段"
    dialog-title="选择字段"
  />
</template>

<script>
export default {
  data() {
    return {
      selectedItemsList: [
        { id: '1', name: '商品名称', tabId: 'fields' },
        { id: '2', name: '商品编号', tabId: 'fields' }
      ],
      selectorOptions: {
        tabs: [
          {
            id: 'fields',
            title: '字段',
            data: [
              { id: '1', name: '商品名称' },
              { id: '2', name: '商品编号' },
              { id: '3', name: '商品类别' }
            ]
          }
        ],
        enableThreeColumns: true,
        enableDragSort: true
      }
    }
  }
}
</script>
```

## 多标签页用法

```vue
<template>
  <field-selector
    v-model="selectedItems"
    placeholder="请选择分类"
    title="分类"
    dialog-title="选择分类"
    :selector-options="multiTabOptions"
  />
</template>

<script>
export default {
  data() {
    return {
      selectedItems: {
        category1: ['1', '2'],
        category2: ['a', 'b']
      },
      
      multiTabOptions: {
        tabs: [
          {
            id: 'category1',
            title: '一级分类',
            data: [
              { id: '1', name: '分类1' },
              { id: '2', name: '分类2' },
              { id: '3', name: '分类3' }
            ]
          },
          {
            id: 'category2', 
            title: '二级分类',
            data: [
              { id: 'a', name: '子分类A' },
              { id: 'b', name: '子分类B' },
              { id: 'c', name: '子分类C' }
            ]
          }
        ],
        enableThreeColumns: true,
        enableDragSort: true
      }
    }
  }
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| value / v-model | 绑定值，选中项的数组 | Array | [] |
| selector-options | 选择器配置 | Object | {} |
| max-display-count | 输入框最多显示几个标签 | Number | 30 |
| placeholder | 输入框占位符 | String | '请选择' |
| title | 标题 | String | '选项' |
| dialog-title | 弹窗标题 | String | '选择字段' |

### selector-options 配置

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| tabs | 标签页配置数组 | Array | [] |
| enableThreeColumns | 启用三列布局 | Boolean | false |
| enableDragSort | 启用拖拽排序 | Boolean | false |
| showSearch | 显示搜索框 | Boolean | true |

### tabs 配置

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| id | 标签页唯一标识 | String | — |
| title | 标签页标题 | String | — |
| data | 标签页数据 | Array | [] |
| hidden | 是否隐藏标签页 | Boolean | false |

### data 数据项

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| id | 数据项唯一标识 | String/Number | — |
| name | 数据项名称 | String | — |
| description | 数据项描述（可选） | String | — |

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| input | 绑定值变化时触发 | (value: Array) |
| change | 选择变化时触发 | (selectedItems: Array) |
| sort-change | 拖拽排序时触发 | (sortedItems: Array) |
| confirm | 点击确定按钮时触发 | (selectedItems: Array) |
| cancel | 点击取消按钮时触发 | — |

## 注意事项

- 只支持 v-model（数组）作为唯一数据源，所有操作都通过 v-model 同步。
- 拖拽排序、勾选、删除等操作后，顺序和内容会自动同步。
- 不再支持 defaultSelectedItems。

## 高级用法

- 支持多标签页、三列布局、拖拽排序等高级功能，详见 selector-options 配置。

## 样式定制

组件使用 Less 编写样式，支持通过 CSS 变量或覆盖样式类进行定制：

```less
// 自定义输入框样式
.field-select-input {
  width: 500px; // 自定义宽度
  border-color: #409eff; // 自定义边框色
}

// 自定义弹框样式
.field-selector-dialog {
  .dialog-content {
    height: 600px; // 自定义高度
  }
}
```

## 常见问题

### Q: 如何设置默认选中项？
A: 通过 `v-model` 绑定初始值，或在 `selector-options.defaultSelectedItems` 中配置。

### Q: 如何禁用某些选项？
A: 在数据项中添加 `disabled: true` 属性（需要在 list-panel 组件中实现）。

### Q: 如何自定义选项显示内容？
A: 可以通过修改 list-panel 组件的模板来自定义显示内容。

### Q: 拖拽排序不生效？
A: 确保设置了 `enableDragSort: true` 并且正确安装了 fx-draggable 组件。

### Q: 如何限制最大选择数量？
A: 可以在 change 事件中添加数量限制逻辑。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础的字段选择功能
- 支持多标签页
- 支持三列布局
- 支持拖拽排序

## 依赖组件

- fx-dialog
- fx-tabs / fx-tab-pane
- fx-input
- fx-button
- fx-tag
- fx-checkbox
- fx-scrollbar
- fx-draggable (可选，用于拖拽排序)

