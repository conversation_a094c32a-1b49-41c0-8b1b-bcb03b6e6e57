import { hooks } from './hooks';
export default function () {
    return {
        name: 'dht_web_product_list_top_filter',
        displayName: '顶部筛选',
        data: {},
        related: {
            attributeSettings: [
                {
                  name: 'SetterField',
                  data: {
                    setter: {
                      component: () => import('../../common-setting.vue')
                    }
                  }
            }],
            previewDisplay: () => import(
                './display.vue'
            )
        },
        hooks
    };
}
