export const loadCss= cssUrl => {
  return new Promise((resolve, reject) => {
    Fx.async(cssUrl, css => {
      resolve(css);
    });
  });
};

export const loadFxModule = moduleUrl => {
  return new Promise((resolve, reject) => {
    Fx.async(moduleUrl, module => {
      resolve(module);
    });
  });
};

// 封装vcrm中的组件导出方法
export const getVcrmWidget = (widgetName, args) => {
  return new Promise((resolve) => {
      Fx.async(['vcrm/sdk'], function(Sdk) {
          Sdk.widgetService.getWidgetApp(widgetName, {...args}).then((res)=>{
              console.log(res)
              resolve(res)
          })
      })
  })
}


export const getVcrmService =  (widgetName, args) => {
  return new Promise((resolve) => {
      Fx.async(['vcrm/sdk'], function(Sdk) {
          Sdk.widgetService.getService(widgetName, {...args}).then((res)=>{
              console.log(res)
              resolve(res)
          })
      })
  })
}

export const loadnsail = () => {
  let scriptRequestPromise = null;
  let isIntDht = false;

  const getDhtScript = () => {
    if (window.location.host === 'dev.ceshi112.com' || window.location.host === 'local.ceshi112.com') {
      return Promise.resolve();
    }
    if (!scriptRequestPromise) {
      scriptRequestPromise = new Promise((resolve, reject) => {
        let config = window.PAAS_CONFIG
        let dependUrl = Fx.staticPath + '/nsail-dist/' + (config['nsailDhtJsApp'] || 'dht.js');
        Fx.use(dependUrl, (dhtScript) => {
          scriptRequestPromise = null;

          if(!isIntDht) {
            dhtScript.appWillMount().then(() => {
              isIntDht = true;
              resolve();
            }).catch(()=>{
              resolve();
            })
          } else {
            resolve();
          }
        });
      });
    }
    return scriptRequestPromise;
  };
  return getDhtScript();
}
