<template>
  <div class="dht-category-tree-display">
    <div class="dht-category-tabs">
      <!-- 一级分类标签页 -->
      <div class="dht-level1-tabs">
        <div class="dht-tab-label">一级分类</div>
        <div class="dht-tabs-container">
          <div class="dht-tab-item dht-tab-all">全部</div>
          <div
            v-for="(category, index) in categoryList"
            :key="category._id || index"
            class="dht-tab-item"
            :class="{ 'dht-tab-active': selectedCategory === category._id }"
            @click="selectCategory(category._id)"
          >
            <!-- 图片显示 -->
            <img
              v-if="finalLevel1ImgShow === '1' && category.image_url"
              :src="category.image_url"
              :alt="category.name"
              class="dht-category-image"
            />
            <span>{{ category.name }}</span>
          </div>
        </div>
      </div>

      <!-- 二级分类标签页 -->
      <div
        v-if="finalOnlyFirstLevel === '0'"
        class="dht-level2-tabs"
      >
        <div class="dht-tab-label">二级分类</div>
        <div class="dht-tabs-container">
          <div class="dht-tab-item dht-tab-all">全部</div>
          <div
            v-for="(child, childIndex) in currentChildren"
            :key="child._id || childIndex"
            class="dht-tab-item"
          >
            {{ child.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 分类树显示组件
 * 功能：
 * 1. 显示商品分类树形结构
 * 2. 支持控制一级分类图片的显示/隐藏
 * 3. 支持控制二级分类的显示/隐藏
 * 4. 支持从API获取数据或使用模拟数据
 */
export default {
  name: 'dht_web_product_list_category_tree_beecraft_display',
  inject: ['useInternalNode'],
  props: {
    bizContext: {
      type: Object,
      default: () => ({}),
    },
    source: {
      type: String,
      default: ''
    },
    level1ImgShow: {
      type: String,
      default: '1'
    },
    onlyFirstLevel: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      categoryList: [],
      loading: false,
      internalSource: '',
      internalLevel1ImgShow: '1',
      internalOnlyFirstLevel: '0',
      selectedCategory: null
    }
  },
  computed: {
    finalSource() {
      return this.source || this.internalSource;
    },
    finalLevel1ImgShow() {
      return this.level1ImgShow || this.internalLevel1ImgShow;
    },
    finalOnlyFirstLevel() {
      return this.onlyFirstLevel || this.internalOnlyFirstLevel;
    },
    currentChildren() {
      if (!this.selectedCategory) {
        return [];
      }
      const category = this.categoryList.find(cat => cat._id === this.selectedCategory);
      return category ? category.children : [];
    }
  },
  methods: {
    async getCategoryData() {
      this.loading = true;
      try {
        // 始终使用模拟数据
        this.categoryList = this.getMockData();
      } catch (error) {
        console.error('获取分类数据失败:', error);
        this.categoryList = [];
      } finally {
        this.loading = false;
      }
    },

    getMockData() {
      const defaultImage = 'https://a9.fspage.com/FSR/weex/avatar/object_list/images/list_default_icon.png';
      return [
        {
          _id: '1',
          name: '一级分类A',
          image_url: defaultImage,
          children: [
            { _id: '11', name: '一级分类B' },
            { _id: '12', name: '一级分类C' },
            { _id: '13', name: '一级分类D' }
          ]
        },
        {
          _id: '2',
          name: '电缸',
          image_url: defaultImage,
          children: [
            { _id: '21', name: '电动夹爪' },
            { _id: '22', name: '多轴机器人' },
            { _id: '23', name: '电动夹爪用驱动器' },
            { _id: '24', name: '单轴机器人背板' }
          ]
        },
        {
          _id: '3',
          name: '电动夹爪',
          image_url: defaultImage,
          children: [
            { _id: '31', name: '平行夹爪' },
            { _id: '32', name: '角度夹爪' },
            { _id: '33', name: '三指夹爪' }
          ]
        },
        {
          _id: '4',
          name: '多轴机器人',
          image_url: defaultImage,
          children: [
            { _id: '41', name: '六轴机器人' },
            { _id: '42', name: '四轴机器人' },
            { _id: '43', name: '协作机器人' }
          ]
        },
        {
          _id: '5',
          name: '电动夹爪用驱动器',
          image_url: defaultImage,
          children: [
            { _id: '51', name: '伺服驱动器' },
            { _id: '52', name: '步进驱动器' }
          ]
        },
        {
          _id: '6',
          name: '单轴机器人背板',
          image_url: defaultImage,
          children: [
            { _id: '61', name: '标准背板' },
            { _id: '62', name: '定制背板' }
          ]
        }
      ];
    },

    buildCategoryTree(flatList) {
      const categoryMap = {};
      const rootCategories = [];

      // 创建映射
      flatList.forEach(item => {
        categoryMap[item._id] = {
          ...item,
          children: []
        };
      });

      // 构建树形结构
      flatList.forEach(item => {
        if (item.parent_category_id && categoryMap[item.parent_category_id]) {
          categoryMap[item.parent_category_id].children.push(categoryMap[item._id]);
        } else {
          rootCategories.push(categoryMap[item._id]);
        }
      });

      return rootCategories;
    },

    selectCategory(categoryId) {
      this.selectedCategory = categoryId;
    },

    async init() {
      if (this.useInternalNode) {
        // 直接获取最新的数据
        this.updateInternalData();
      }

      await this.getCategoryData();
      // 默认选中第一个分类
      if (this.categoryList.length > 0) {
        this.selectedCategory = this.categoryList[0]._id;
      }
    },

    updateInternalData() {
      if (this.useInternalNode) {
        const { source, level1ImgShow, onlyFirstLevel } = this.useInternalNode(node => {
          return node.data;
        });

        this.internalSource = source || '';
        this.internalLevel1ImgShow = level1ImgShow || '1';
        this.internalOnlyFirstLevel = onlyFirstLevel || '0';

        console.log('Display updated - level1ImgShow:', level1ImgShow, 'onlyFirstLevel:', onlyFirstLevel);
      }
    }
  },
  watch: {
    // 监听props变化，实时更新显示
    source: {
      handler() {
        this.getCategoryData();
      },
      immediate: false
    },
    level1ImgShow: {
      handler(newVal) {
        console.log('level1ImgShow changed:', newVal);
        this.updateInternalData();
      },
      immediate: false
    },
    onlyFirstLevel: {
      handler(newVal) {
        console.log('onlyFirstLevel changed:', newVal);
        this.updateInternalData();
      },
      immediate: false
    }
  },
  created() {
    this.init();
  },

  mounted() {
    // 定期检查数据变化
    this.dataCheckInterval = setInterval(() => {
      this.updateInternalData();
    }, 500);
  },

  beforeDestroy() {
    if (this.dataCheckInterval) {
      clearInterval(this.dataCheckInterval);
    }
  }
}
</script>

<style lang="less" scoped>
.dht-category-tree-display {
  width: 100%;
  background: #fff;
  border: 1px solid #ff8000;
  border-radius: 6px;
  overflow: hidden;

  .dht-loading {
    text-align: center;
    padding: 20px;
    color: #999;
  }

  .dht-category-tabs {
    width: 100%;
  }

  .dht-level1-tabs,
  .dht-level2-tabs {
    display: flex;
    align-items: center;
    min-height: 48px;
    border-bottom: 1px solid #e0e0e0;

    &:last-child {
      border-bottom: none;
    }
  }

  .dht-tab-label {
    flex-shrink: 0;
    width: 80px;
    padding: 0 16px;
    font-size: 14px;
    color: #666;
    background: #f8f9fa;
    border-right: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
  }

  .dht-tabs-container {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 8px 16px;
    gap: 8px;
    flex-wrap: wrap;
  }

  .dht-tab-item {
    padding: 6px 12px;
    font-size: 14px;
    color: #333;
    background: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 6px;

    .dht-category-image {
      width: 16px;
      height: 16px;
      object-fit: cover;
      border-radius: 2px;
      flex-shrink: 0;
    }

    &:hover {
      background: #e8f4ff;
      border-color: #409eff;
      color: #409eff;
    }

    &.dht-tab-active {
      background: #ff8000;
      border-color: #ff8000;
      color: #fff;

      &:hover {
        background: #ff8000;
        border-color: #ff8000;
        color: #fff;
      }
    }

    &.dht-tab-all {
      background: #fff;
      border-color: #d0d0d0;
      color: #666;

      &:hover {
        background: #f0f0f0;
        border-color: #b0b0b0;
      }
    }
  }
}
</style>