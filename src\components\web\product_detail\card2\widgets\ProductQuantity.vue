<template>
  <div v-if="attr === '0'" class="dht-product-detail-input-wrapper">
    <div class="dht-product-detail-main-item">
      <div class="dht-product-detail-main-label">quantity</div>
      <div class="dht-product-detail-main-content sku-input-content">
        <fx-input-number :min="1" :max="100" size="small" :step="1" :value="1" />
        <span style="margin-left: 5px;">pcs</span>
        <div v-if="stock === '1'" class="stock-info">stock: sufficient</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductQuantity',
  props: {
    attr: {
      type: String,
      default: '0'
    },
    stock: {
      type: String,
      default: '1'
    }
  }
}
</script>

<style lang="less" scoped>
@label-color: #999;

.dht-product-detail {
  &-input-wrapper {
    margin: 16px;
    .sku-input-content {
      display: flex;
      align-items: center;
      color: @label-color;
      font-size: 12px;
      .stock-info {
        font-size: 14px;
        margin-left: 10px;
      }
    }
  }
}
</style> 