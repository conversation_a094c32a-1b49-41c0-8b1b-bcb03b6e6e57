const loadWidget = (widgetName) => {
  return new Promise((resolve) => {
      Fx.async(['app-standalone/components/widgets/newwidgets'], function(Wdts) {
        resolve(Wdts[widgetName])
      })
  })
}

export default function () {
  return {
    name: 'dht_web_product_list_all',
    displayName: 'mall list',  // 商城列表
    related: {
      previewDisplay: () => loadWidget('dht_shopmall_allproduct').then(res => res.displayComponent()).then(res => res),
      attributeSettings: [        
        {
          name: 'SetterField',
          data: {
            setter: {
              component: () => loadWidget('dht_shopmall_allproduct').then(res => res.settingComponent()).then(res => res)
            }
          }
        }]
    }
  }
}