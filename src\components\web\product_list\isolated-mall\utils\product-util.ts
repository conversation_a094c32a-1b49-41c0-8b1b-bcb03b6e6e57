import Vue from 'vue';
import {Product} from '@/widgets/types';
import { isMnchip } from './check-ea';

export const convertSpuList = (list: any[]): any[] => {
  list.forEach(item => {
    if (!item.is_spec) {
      const product = item.product_id__ro;
      item.product_id = product._id;
    }
    Vue.set(item, 'price', item.virtual_product_price);
    Vue.set(item, 'quantity', getProductMinNum(item) || 1);
  });
  return list;
};
// TODO if 逻辑优化
// 获取产品或者商品的最小订购量，商品单规格从product_id__ro
export function getProductMinNum(product: Product): any {
  let minNum = 0;
  if ($dht.config.sail.isOpenMiniNum && product) {
    const productInfo = product.product_id__ro ? product.product_id__ro : product;
    // __v 
    if (productInfo.is_multiple_unit && !productInfo.is_common_unit) {
      const curUnitOption = productInfo.multiUnitInfoArr.find((item: { value: any; }) => productInfo.unit === item.value);

      if (curUnitOption && curUnitOption.minimumOrderQuantity) {
        minNum = curUnitOption.minimumOrderQuantity;
      }
    } else if (productInfo.minimum_order_quantity) {
      minNum = productInfo.minimum_order_quantity;
    }
    
  }
  return +minNum;
};

export const isSpuObj = (obj: any) => {
  return obj.object_describe_api_name === 'SPUObj';
};

export function isCommonUnitProduct(product: Product): boolean {
  return !!(product.product_id__ro ? product.product_id__ro.is_common_unit : product.is_common_unit);
}

export function getProductId(product: Product) {
  return product.product_id__ro ? product.product_id__ro._id : product._id;
}

export function getDefaultCurrencyFlag() {
  return isMnchip() ? '' : '￥'
}

