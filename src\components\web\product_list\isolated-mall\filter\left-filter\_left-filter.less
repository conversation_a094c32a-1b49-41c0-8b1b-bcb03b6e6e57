.left-filter {
  width: 100%;
  background: #fff;
  border-radius: 4px;
  
  .filter-section {
    border-bottom: 1px solid #EAEBEE;
    margin-bottom: 24px;
    padding-bottom: 16px;
    
    &:last-child {
      border-bottom: none;
    }
    
    .filter-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px 16px 0;
      
      cursor: pointer;
      
      .filter-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }
      
      .expand-icon {
        font-size: 12px;
        color: #999;
        transition: transform 0.3s;
        user-select: none;
        
        &.expanded {
          transform: rotate(180deg);
        }
      }
    }
    
    .filter-content {
      padding: 0 16px 8px 0;
      box-sizing: border-box;
      
      .filter-option {
        display: block;
        
        &:last-child {
          margin-bottom: 0;
        }        
      }
      
      .filter-input {
        width: 100%;
        
        /deep/ .fx-input__inner {
          font-size: 13px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;
          
          &:focus {
            border-color: #409eff;
          }
        }
      }
    }
  }
  
  // 单选组和多选组的样式
  /deep/ .fx-radio-group,
  /deep/ .fx-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  // 单选和多选项的样式
  /deep/ .fx-radio,
  /deep/ .fx-checkbox {
    margin-right: 0;
    margin-bottom: 0;    
  }

  .filter-currency-range {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
  }
  .filter-currency-input {
    width: 90px;
  }
  .currency-range-separator {
    color: #999;
    font-size: 18px;
    margin: 0 4px;
  }
  .currency-range-error {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 2px;
    margin-left: 2px;
  }
  
}