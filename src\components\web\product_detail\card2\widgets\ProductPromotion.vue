<template>
  <div class="dht-product-detail-policy-wrapper">
    <div class="dht-product-detail-main-item">
      <div class="dht-product-detail-main-label">promotion</div>
      <div class="dht-product-detail-main-content dht-policy-content">
        <div class="dht-policy-name">
          price policy name
        </div>
        <div class="dht-policy-item-line"></div>
        <span class="dht-policy-view">
          view<i class="fx-icon-arrow-down"></i>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductPromotion'
}
</script>

<style lang="less" scoped>
.dht-product-detail {
  &-policy-wrapper {
    padding: 8px 16px;

    .dht-policy-content {
      display: flex;
      align-items: center;
    }

    .dht-policy-name {
      padding: 2px 4px;
      border-radius: 2px;
      border: 1px solid var(--color-danger03);
      background: var(--color-danger01);
      color: var(--color-danger06);
      font-size: 12px;
    }
  }
}

.dht-policy-item {
  font-size: 12px;
  color: #FF522A;
  line-height: 18px;
  border: 1px solid #FFBDA3;
  background-color: #FFF5F0;
  margin-right: 5px;
  border-radius: 2px;
  padding: 1px 4px;
}

.dht-policy-item-line {
  display: inline-block;
  height: 10px;
  width: 1px;
  background-color: #DEE1E6;
  margin-left: 9px;
  margin-right: 9px;
}

.pricepolicy-list-expend {
  color: #545861;
  font-size: 12px;
  line-height: 23px;
}
</style> 