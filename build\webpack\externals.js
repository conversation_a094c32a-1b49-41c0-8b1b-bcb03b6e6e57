const externals = {
    jquery: 'jQuery',
    vue: 'Vue',
    underscore: '_',
    'vue': 'base-vue',
    'vuex': 'base-vuex',
    'seajs': 'base-seajs',
    'moment': 'base-moment',
    'utils': 'base-modules/utils',
    // 'base-uploader': 'base-uploader',
    // 'vue-selector-box': 'vue-selector-box',
    // 'base-parse-contacts': 'base-modules/vue-selector/parse-contacts',
    // 'base-modules/guide/guide': 'base-modules/guide/guide',

    /** 当前项目sdk */
    // 'paas-appcustomization/sdk': 'paas-appcustomization/sdk',
    
    /** 新版设计器，首页相关全网后可删除组件 */
    // 'base-sortable': 'base-sortable',
    // 'crm-widget/select/select': 'crm-widget/select/select',
    // 'base-modules/ui/scrollbar/scrollbar': 'base-modules/ui/scrollbar/scrollbar',

    /** crm 依赖项 */
    // 'crm-widget/table/table': 'crm-widget/table/table',
    'crm-modules/common/util': 'crm-modules/common/util',
    // 'crm-assets/style/all.css': 'crm-assets/style/all.css',
    // 'crm-widget/dropdown/dropdown': 'crm-widget/dropdown/dropdown',
    /** bi洞察设计器改版后才可删除 */
    // 'crm-modules/components/sortlayout/sortlayout': 'crm-modules/components/sortlayout/sortlayout',

    /** checkin 依赖项 */
    // 'app-checkin/map': 'app-checkin/map.js',
    // 'vue-common': 'app-checkin-modules/es6/paas/index',
    // 'app-checkin-assets/style/all.css': 'app-checkin-assets/style/all.css',

    
    /** uipaas 组件库依赖项 */
    'app-standalone': 'app-standalone/components/widgets/newwidgets',
    'app-standalone-mobile': 'app-standalone/components/widgetsMobile/index',
    // 'bi-components': 'bi-modules/components/customization/index',
    // 'bi-mobile-components': 'bi-modules/components/mobile-customization/index',
    // 'app-integral': 'app-integral/app.js',
    // 'v-crm': 'vcrm/sdk',
    // 'paas-tpm/sdk': 'paas-tpm/sdk',
    // 'xx-xxvui/sdk': 'xx-xxvui/sdk',
}

module.exports = (isDevelopment) => {
    // if (isDevelopment) {
    //     delete externals.vue;
    // }

    return externals;
};