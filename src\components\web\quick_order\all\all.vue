<template>
  <div class="dhtbiz-quick-order-all" style="width: 100%; height: 700px;">
  </div>
</template>

<script>

import {loadCss, loadFxModule, loadnsail  } from '@/share/utils/load';

const ShopmallPageLoader = loadFxModule('crm-modules/page/quicklyorder/quicklyorder');
// 依赖crm 中css样式的加载
// crm-dist/assets/style/page-4333aa43ad.css
const crmAllCss = loadCss('crm-assets/style/all.css');
const cssModule = loadCss('crm-assets/style/page.css');

export default {
  name: 'dht_web_quick_order_all',
  props: {
    apiname: {
      type: String,
      default: 'QuicklyOrder',
    },
  },
  methods: {
    initTable() {
      Promise.all([loadnsail(),crmAllCss, cssModule, ShopmallPageLoader]).then(([sail, cssall, csspage, CrmList]) => {
        if (this.isDestroy) return;

        // 改页面可能被隐藏
        if (!$('.dhtbiz-quick-order-all')[0]) {
          return;
        }

        this.$list = new CrmList({
          wrapper: $('.dhtbiz-quick-order-all'),
          apiname: this.apiname,
        });

        this.$once('hook:beforeDestroy', () => {
          this.$List && this.$list.destroy();
          this.$list = null;
        });

        this.$list.render();
      });
    },
  },
  created() {
    this.initTable();
  },
  beforeDestroy() {
    this.isDestroy = true;
    this.$list && this.$list.destroy();
    this.$list = null;
  },
};
</script>

<style lang="less" scoped>
.dht-quick-order-all {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .dht-order-quickly-container .dht-order-quickly-container-wrapper .pickself-box .t-wrap {
    height: 100% !important;
  }
  /deep/ .crm-table {
    .dt-term-batch {
      z-index: 0;
    }
    .dt-caption {
      z-index: 10;
    }
  }
}
</style>
