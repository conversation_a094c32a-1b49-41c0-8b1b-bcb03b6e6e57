export default function () {
  return {
    name: 'dht_web_product_detail_key_info',
    // displayName: $t('商品参数信息'), 
    displayName: $t('dht.component.web.product_detail_key_info', '商品参数信息'),
    data: {
      name: $t('dht.component.web.product_detail_key_info.title', '参数信息'),
      showType: '1',
      selectedFields: []
    },
    $$data: {
      isCanvas: true
    },
    related: {
      previewDisplay: () => import('./display.vue'),
      attributeSettings: [
        /* {
          inject: ['useInternalNode'],          
          components: {
            KeyInfoSetting: () => import('./setting.vue')
          },
        }  ,*/
        {
          name: 'SetterField',
          data: {
            //   label: $t('paasbiz.portal-designer.max-content-set', '参数设置'),
            // label: '参数信息',
            // display: 'block',
            setter: {
              component: () => import('./setting.vue')
            }
          }
        }]
    }
  }
}