# 顶部筛选组件 (TopFilter)

基于 `isolated-mall/filter/left-filter` 组件改造的水平布局筛选组件。

## 功能特性

- 支持多种筛选类型：单选、多选、文本输入、金额区间
- 水平布局，适合顶部筛选栏
- 响应式设计，移动端自动切换为垂直布局
- 内置重置按钮
- 自动构建筛选条件并向父组件传递

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| filter_fields | Array | [] | 需要筛选的字段名数组 |
| objectFields | Object | {} | 字段配置对象，包含字段类型、标签、选项等 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| filter-change | { field, value, allFilters } | 筛选条件变化时触发 |

## 字段类型

- `select_one`: 单选下拉框
- `select_many`: 多选下拉框
- `currency`: 金额区间输入
- `input`/`text`: 文本输入框

## 使用示例

```vue
<template>
  <top-filter
    :filter_fields="['category', 'price', 'brand']"
    :objectFields="fieldConfig"
    @filter-change="handleFilterChange"
  />
</template>

<script>
import TopFilter from './top-filter'

export default {
  components: {
    TopFilter
  },
  data() {
    return {
      fieldConfig: {
        category: {
          label: '商品分类',
          type: 'select_one',
          options: [
            { label: '电子产品', value: 'electronics' },
            { label: '服装', value: 'clothing' }
          ]
        },
        price: {
          label: '价格',
          type: 'currency'
        },
        brand: {
          label: '品牌',
          type: 'input'
        }
      }
    }
  },
  methods: {
    handleFilterChange(filterData) {
      console.log('筛选条件变化:', filterData)
    }
  }
}
</script>
```

## 方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| getFilterValues | - | 获取当前所有筛选值 |
| resetFilters | - | 重置所有筛选条件 |
| setFilterValues | values | 设置筛选值 |

## 样式特点

- 水平布局，每个筛选项占用固定宽度
- 响应式设计，768px 以下自动切换为垂直布局
- 统一的间距和样式
- 支持金额区间的错误提示显示
