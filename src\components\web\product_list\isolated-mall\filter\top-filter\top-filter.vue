<template>
  <div class="top-filter">
    <!-- 筛选项水平排列 -->
    <div class="filter-container">
      <div 
        v-for="filterField in filter_fields" 
        :key="filterField"
        class="filter-item"
        :class="{ 'is-expanded': currentExpandedField === filterField }"
      >
        <div 
          class="filter-label"
          :class="{ 'selected': hasSelectedValue(filterField) }"
          @click="toggleSection(filterField)"
        >
          <span class="label-text">{{ getFieldLabel(filterField) }}</span>
          <span 
            class="expand-icon fx-icon-arrow-down"
            :class="{ 'expanded': currentExpandedField === filterField }"
            >
          </span>
          
          <!-- 已选中内容显示在标签右侧 -->
          <div class="selected-tags" v-if="hasSelectedValue(filterField)">
            <template v-if="getFieldType(filterField) === 'select_one'">
              <span class="selected-tag">{{ getSelectedLabel(filterField) }}</span>
            </template>
            <template v-else-if="getFieldType(filterField) === 'select_many'">
              <template v-for="(label, index) in getDisplayTags(filterField).tags" >
                <span class="selected-tag">{{ label }}</span>
              </template>
              <span 
                v-if="getDisplayTags(filterField).hasMore" 
                class="selected-tag more-tag"
              >
                +{{ getDisplayTags(filterField).moreCount }}
              </span>
            </template>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 展开的筛选控制区域，独立于循环外 -->
    <div 
      v-if="currentExpandedField" 
      class="filter-control-panel"
      :class="`filter-control-${currentExpandedField}`"
    >
      <div class="filter-control-content">
        <!-- 单选筛选项 -->
        <fx-radio-group 
          v-if="getFieldType(currentExpandedField) === 'select_one'"
          v-model="selectedValues[currentExpandedField]"
          @change="onFilterChange(currentExpandedField, $event)"
          class="filter-radio-group"
        >
          <fx-radio 
            v-for="option in getFieldOptions(currentExpandedField)"
            :key="option.value"
            :label="option.value"
            class="filter-option"
          >
            {{ option.label }}
          </fx-radio>
        </fx-radio-group>
        
        <!-- 多选筛选项 -->
        <fx-checkbox-group 
          v-else-if="getFieldType(currentExpandedField) === 'select_many'"
          v-model="selectedValues[currentExpandedField]"
          @change="onFilterChange(currentExpandedField, $event)"
          class="filter-checkbox-group"
        >
          <fx-checkbox 
            v-for="option in getFieldOptions(currentExpandedField)"
            :key="option.value"
            :label="option.value"
            class="filter-option"
          >
            {{ option.label }}
          </fx-checkbox>
        </fx-checkbox-group>

        <!-- 金额区间筛选 -->
        <div v-else-if="getFieldType(currentExpandedField) === 'currency'" class="filter-currency-range">
          <fx-input
            v-model="currencyRange[currentExpandedField].min"
            :placeholder="'\uFFE5 最小值'"
            class="filter-currency-input"
            @change="onCurrencyInput(currentExpandedField)"
            @blur="onCurrencyInput(currentExpandedField)"
            type="number"
            min="0"
          />
          <span class="currency-range-separator">-</span>
          <fx-input
            v-model="currencyRange[currentExpandedField].max"
            :placeholder="'\uFFE5 最大值'"
            class="filter-currency-input"
            @change="onCurrencyInput(currentExpandedField)"
            @blur="onCurrencyInput(currentExpandedField)"
            type="number"
            min="0"
          />
          <div v-if="currencyRange[currentExpandedField].error" class="currency-range-error">{{ currencyRange[currentExpandedField].error }}</div>
        </div>
        <!-- 字段筛选 -->
        <fx-input
          v-else
          v-model="selectedValues[currentExpandedField]"
          :placeholder="`请输入${getFieldLabel(currentExpandedField)}`"
          @change="onInputFieldChange(currentExpandedField, $event)"
          @blur="onInputFieldChange(currentExpandedField, $event)"
          class="filter-input"
        ></fx-input>
      </div>

      <!-- 清空按钮 -->
      <div class="filter-actions" v-if="hasSelectedValue(currentExpandedField)">
         <fx-button 
           size="small" 
           class="dht-clear-btn"
           @click="clearCurrentField"
         >
           清空
         </fx-button>
       </div>
    </div>
  </div>
</template>

<script src="./_top-filter.js"></script>
<style src="./_top-filter.less" lang="less" scoped></style>
