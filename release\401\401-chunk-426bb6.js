"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[401],{8401:(e,t,s)=>{s.r(t),s.d(t,{default:()=>r});var n=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"dht-product-detail-key-info-setting"},[s("div",{staticClass:"dht-name dht-title",on:{click:e.toggleShow}},[s("i",{class:e.show?"el-icon-caret-bottom":"el-icon-caret-right"}),e._v("\n\n      "+e._s(e.$t("dht.component.web.product_detail_key_info.title","参数信息"))+"\n    ")]),s("div",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"show"}],staticClass:"dht-content"},[s("div",{staticClass:"dht-section"},[s("div",{staticClass:"dht-title"},[e._v("\n            "+e._s(e.$t("dht.component.web.product_detail_key_info.show_type","展示样式"))+"\n          ")]),s("div",{staticClass:"dht-types"},e._l(e.showTypes,(function(t,n){return s("div",{key:t.key,staticClass:"dht-cells",class:e.showType==t.val?"dht-selected":"",on:{click:function(s){return s.stopPropagation(),e.showTypeChange(t)}}},[s("div",{staticClass:"dht-box"},[s("i",{staticClass:"dht-icon",class:t.icon})]),s("div",{staticClass:"dht-label"},[e._v(e._s(t.name))])])})),0)]),e.fieldsOptions.length>0?s("div",{staticClass:"dht-section"},[s("div",{staticClass:"dht-title"},[e._v("\n            "+e._s(e.$t("dht.component.web.product_detail_key_info.fields","显示字段"))+"\n          ")]),s("fx-transfer",{attrs:{filterable:"","is-simple":"",data:e.fieldsOptions,draggable:"","target-order0":"push"},on:{change:e.fieldsChange},model:{value:e.selectedFields,callback:function(t){e.selectedFields=t},expression:"selectedFields"}})],1):e._e()])])};n._withStripped=!0;var i=s(5861),a=s(4687),o=s.n(a),l=s(1141),c=["life_status_before_invalid","life_status","_id"];const d={inject:["useInternalNode"],components:{},data:function(){return{show:!0,name:"",showType:"1",selectedFields:[],fieldsOptions:[]}},computed:{showTypes:function(){return[{key:"single",val:"1",name:$t("dht.component.web.product_detail_key_info.single","单列"),icon:"fx-icon-tuliweizhi1"},{key:"double",val:"2",name:$t("dht.component.web.product_detail_key_info.double","双列"),icon:"fx-icon-list-4"}]}},methods:{init:function(){var e=this;return(0,i.Z)(o().mark((function t(){var s,n,i,a;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return s=e.useInternalNode((function(e){return e.data})),n=s.name,i=s.showType,a=s.selectedFields,e.name=n,e.showType=i,e.selectedFields=a,t.next=6,e.formatFields();case 6:case"end":return t.stop()}}),t)})))()},toggleShow:function(){this.show=!this.show},formatFields:function(){var e=this;return(0,i.Z)(o().mark((function t(){var s,n,i,a;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return s=[],t.next=3,l.Z.fetchObjFields(l.Z.mainObjApiName());case 3:for(a in n=t.sent,i={},n)n.hasOwnProperty(a)&&"system"!==(i=n[a]).define_type&&c.indexOf(a)<0&&["object_reference","text","currency","number","date","datetime","email","phone","count"].indexOf(i.type)>=0&&s.push({value:a,key:a,label:i.label_r||i.label});return e.fieldsOptions=s,t.abrupt("return",s);case 9:case"end":return t.stop()}}),t)})))()},nameChange:function(e){this.updateProps("name",e)},showTypeChange:function(e){var t=e.val;this.updateProps("showType",t)},fieldsChange:function(e){this.updateProps("selectedFields",e)},updateProps:function(e,t){this.useInternalNode().actions.setCustom((function(s){s[e]=t})),this.$set(this,e,t)}},created:function(){this.init()},mounted:function(){}};const r=(0,s(1900).Z)(d,n,[],!1,null,null,null).exports}}]);