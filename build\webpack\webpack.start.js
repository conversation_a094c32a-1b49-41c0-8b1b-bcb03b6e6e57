const path = require('path');
const webpackBaseConfig = require('./webpack.common.js');
const webpack = require("webpack");
// const {
//   proxy_target,
//   devDirectory,
//   projectName
// } = require('./project.config.js');
const { merge } = require("webpack-merge");
module.exports = (env, argv) => {
  // argv.mode = "development"
  let webpackConfig = merge(webpackBaseConfig, {
    mode: "development",
    devtool: "inline-source-map",
    entry: {
      app: path.resolve(__dirname, "../public/app.js"),
    },
    output: {
      // filename: '[name].[chunkHash:8].js',
      // chunkFilename: '[name].[chunkHash:8].js',
      path: path.resolve(__dirname, `../../dev`),
      // publicPath: `html/base-biz/`,
      libraryTarget: "global",
      libraryExport: "default",
      library: "dhtbiz",
      umdNamedDefine: true,
    },
    optimization: {
      splitChunks: {
        chunks: "async",
        minSize: 20000,
        minRemainingSize: 0,
        minChunks: 1,
        maxAsyncRequests: 30,
        maxInitialRequests: 30,
        enforceSizeThreshold: 50000,
        cacheGroups: {
          defaultVendors: {
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            reuseExistingChunk: true,
          },
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: false,
            name: "common",
          },
        },
      },
    },
    module: {
      rules: [
        {
          test: /\.(scss|css)$/,
          use: ["style-loader", "css-loader", "sass-loader"],
        },
      ],
    },
    optimization: {
      splitChunks: false,
    },
    devtool: "eval-cheap-module-source-map",
    devServer: {
      // host: "localhost",
      // port: 8080,
      // open: true,
      // // hot: true,
      // noInfo: true,
      // historyApiFallback: true,
      // disableHostCheck: true,
      // https: {
      //   spdy: {
      //     protocols: ["http/1.1"],
      //   },
      // },
      // hotOnly: true,
      // openPage: "XV/Home/Index",
      // headers: {
      //   "Access-Control-Allow-Origin": "*",
      // },
      // proxy: {
      //   "**": {
      //     target: "https://localhost",
      //     secure: false,
      //     changeOrigin: false,
      //     withCredentials: true,
      //     bypass: function (req, res, proxyOptions) {
      //       if (req.url.indexOf("hot-update") > -1) {
      //         return req.url.replace("/XV/Home", "");
      //       }
      //     },
      //   },
      // },
    },
    plugins: [
      new webpack.DefinePlugin({
        _WEBPACK_PRODUCTION: false,
      }),
    ],
  });

  // webpackConfig.plugins.push(
  //   new FriendlyErrorsPlugin({
  //     compilationSuccessInfo: {
  //       messages: [`Your application is running here: https://${webpackConfig.devServer.host}:${webpackConfig.devServer.port}`]
  //     },
  //     onErrors: utils.createNotifierCallback()
  //   })
  // );

  return webpackConfig;
}
