<template>
  <div class="dht-product-detail">
    <CustomPageRuntime pageApiName="" :plugins="plugins"></CustomPageRuntime>
  </div>
</template>

<script>

export default {
  components: {
    // CustomPageRuntime 
    CustomPageRuntime: () => Fx.getBizComponent('paasbiz', 'CustomPageRuntime').then(res => res())
  },
  data() {
    return {
      plugins: []
    }
  },
  created() {
        this.plugins = [
            class Plugin {
                // 待用RelatedList接口 -> data
                // 待用Descirib接口 -> objectDescribe
                // 直接传递 objectApiName
                // 直接传递 objectDataId
            }
        ]
    }
};
</script>

<style lang="less">
  .dhtbiz-list {
      
      .desc {
          padding-top: 30px;
          font-size: 22px;
      }
  }
</style>
