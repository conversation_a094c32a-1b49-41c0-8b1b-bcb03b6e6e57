<template>
  <div :class="'category-level-' + level">
    <div :class="'category-row category-row-' + level">
      <div class="level-label">{{ getLevelLabel }}</div>
      <div class="category-content">
        <div class="items-container" :class="{ expanded: isExpanded }">
          <span
            v-for="(item, index) in items"
            :key="index"
            :class="['level-item', { active: item.active }]"
            @click="handleClick(item)">
            {{ item.name }}
          </span>
        </div>
        <div v-if="needExpand" class="expand-control" @click="toggleExpand">
          {{ isExpanded ? $t('收起') : $t('展开') }}
          <i :class="['el-icon-arrow-right', { 'is-expanded': isExpanded }]"></i>
        </div>
      </div>
    </div>
    <category-tree-item
      v-for="item in items"
      v-if="item.active && hasChildren(item)"
      :key="item.name"
      :items="item.children"
      :level="level + 1"
      :parent-node="item"
      @select="handleChildSelect"
    ></category-tree-item>
  </div>
</template>

<script>
export default {
  name: 'CategoryTreeItem',
  props: {
    items: {
      type: Array,
      default: () => ([])
    },
    level: {
      type: Number,
      default: 1
    },
    parentNode: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      isExpanded: false,
      needExpand: false,
      activeItem: null
    };
  },
  computed: {
    getLevelLabel() {
      const labels = {
        1: $t('first.category'), // 一级分类
        2: $t('second.category'), // 二级分类
        3: $t('third.category'), // 三级分类
      };
      return labels[this.level] || `${this.level}${$t('级')}${$t('分类')}`;
    }
  },
  watch: {
    items: {
      handler() {
        this.$nextTick(() => {
          this.checkNeedExpand();
        });
      },
      deep: true
    }
  },
  mounted() {
    this.checkNeedExpand();
    window.addEventListener('resize', this.checkNeedExpand);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkNeedExpand);
  },
  methods: {
    hasChildren(item) {
      return item.children && item.children.length > 0;
    },

    handleClick(item) {
      if (item.active) {
        this.$set(item, 'active', false);
        this.activeItem = null;
        if (this.hasChildren(item)) {
          this.clearChildrenSelection(item.children);
        }
        return;
      }

      this.items.forEach(i => {
        if (i !== item && i.active) {
          this.$set(i, 'active', false);
          if (this.hasChildren(i)) {
            this.clearChildrenSelection(i.children);
          }
        }
      });

      this.$set(item, 'active', true);
      this.activeItem = item;

      if (this.hasChildren(item)) {
        this.isExpanded = true;
        const firstChild = item.children[0];
        this.$set(firstChild, 'active', true);

        if (this.hasChildren(firstChild)) {
          this.autoSelectFirstChild(firstChild);
        }
      }

      if (item.id !== '1') {
        this.$emit('select', item, this.level);
      } else {
        if (this.parentNode) {
          this.$emit('select', this.parentNode, this.level - 1);
        } else {
          this.$emit('select', null, this.level);
        }
      }
    },

    autoSelectFirstChild(item) {
      if (this.hasChildren(item)) {
        const firstChild = item.children[0];
        this.$set(firstChild, 'active', true);
        this.autoSelectFirstChild(firstChild);
      }
    },

    clearChildrenSelection(children) {
      if (children) {
        children.forEach(child => {
          this.$set(child, 'active', false);
          if (this.hasChildren(child)) {
            this.clearChildrenSelection(child.children);
          }
        });
      }
    },

    handleChildSelect(item, level) {
      this.$emit('select', item, level);
    },

    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },

    checkNeedExpand() {
      this.$nextTick(() => {
        const container = this.$el.querySelector('.items-container');
        if (container) {
          let totalWidth = 0;
          const items = container.querySelectorAll('.level-item');
          // 计算所有子项的实际总宽度（包括内边距和外边距）
          items.forEach((item) => {
            const style = window.getComputedStyle(item);
            const width = item.offsetWidth +
              parseInt(style.marginLeft) +
              parseInt(style.marginRight);
            totalWidth += width;
          });

          const containerWidth = container.clientWidth;

          // 总宽度超过容器宽度，就显示展开按钮
          this.needExpand = totalWidth > containerWidth;
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.category-row {
  display: flex;
  min-height: 40px;
  border-top: 1px solid var(---Special-02, #EEF0F3);

  &.category-row-1 {
    border-top: none;
  }
}

.level-label {
  width: 88px;
  padding: 10px 0 10px 0;
  margin-top: 5px;
  color: var(--color-neutrals11);
  font-size: 14px;
  flex-shrink: 0;
}

.category-content {
  flex: 1;
  position: relative;
  padding: 5px 70px 5px 0;
  width: 0;
  min-width: 0;
}

.items-container {
  min-height: 30px;
  line-height: 30px;
  overflow: hidden;
  padding: 5px 0;
  width: 100%;

  &:not(.expanded) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &.expanded {
    height: auto;
    white-space: normal;
    display: block;
  }

  .level-item {
    display: inline-block;
    padding: 0 12px;
    margin: 0 8px 8px 0;
    cursor: pointer;
    font-size: 14px;
    color: #181C25;
    position: relative;
    border-radius: 6px;
    transition: all .3s ease-in-out;

    &:hover {
      background: #fff7f2;
      color: #f60;
    }

    &.active {
      color: #f60;

      &.active {
        background: #fff7f2;

        &::after {
          background-color: #f60;
        }
      }

      &:hover {
        color: #ff8533;
      }
    }
  }
}

.expand-control {
  position: absolute;
  right: 0;
  top: 16px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 2px;
  transition: all .3s ease-in-out;

  &:hover {
    background: #fff7f2;
    color: #ff8533;
  }

  .el-icon-arrow-right {
    margin-left: 3px;
    transition: all .3s ease-in-out;

    &.is-expanded {
      transform: rotate(90deg);
    }
  }
}
</style> 