<script lang="ts">
import 'reflect-metadata';
import { Component, Vue, Emit } from 'vue-property-decorator';
import { mixins } from 'vue-class-component';

import CardMixin from './mixins';
import { getImageByPath } from '../../utils/image-util';
import ProductTag from '../../product-tag/product-tag.vue';
import { isSpuObj } from '../../utils/product-util';

@Component({
  name: 'BaseCard',
  components: {
    ProductTag,
  }
})
export default class BaseCard extends mixins(CardMixin) {
  get pictureUrl(): string {
    const picture = isSpuObj(this.product) ? this.product.picture : this.product.picture_path;
    const firstPicture = picture && picture.length ? picture[0] : {};
    if (firstPicture.signedUrl) {
      return firstPicture.signedUrl + '&size=350*0'
    }
    return getImageByPath(firstPicture.path, '350*0', firstPicture.ext);
  }

  get isDefaultImg() {
    return this.pictureUrl.lastIndexOf('sail.default.png') !== -1;
  }

  get commodityLabels() {
    let commodityOptions = this.product.commodityOptions || [];
    return commodityOptions.filter((option) => option.value !== 'option1');
  }

  get isNew() {
    return (this.product.commodityOptions || []).findIndex(option => option.value === 'option1') !== -1;
  }

  get stockText() {
    if (this.product.virtual_available_stock) {
      return `${$t('库存')}：${this.product.virtual_available_stock}`;
    }
    return `${$t('库存')}： --`;
  }

  get displayPrice() {
    if (this.isHidePrice) return '***';

    const priceBookPrice = this.product.virtual_price_book_price;
    return priceBookPrice != null ? priceBookPrice : this.product.price;
  }
}
</script>

<template src="./base-card.html"></template>
<style src="../product-card.less" lang="less" scoped></style>
