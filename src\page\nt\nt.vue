<template>
  <div class="test-container">
    <!-- 运行态 -->
    <div class="runtime-panel">
      <div class="panel-header">运行态</div>
      <div class="panel-content">
        <testComp
          v-bind="flattenProps"
          :props="componentProps"
          :objectContext="mockObjectContext"
        />
      </div>
    </div>

    <!-- 预览态和设置态 -->
    <div class="preview-setting-panel">
      <!-- 预览态 -->
      <div class="preview-panel">
        <div class="panel-header">预览态</div>
        <div class="panel-content">
          <component
            :is="previewComponent"
            v-bind="flattenProps"
            :props="componentProps"
            :api_name="compApiName"
            :objectContext="mockObjectContext"
          />
        </div>
      </div>

      <!-- 设置态 -->
      <div class="setting-panel">
        <div class="panel-header">设置态</div>
        <div class="panel-content">
          <div v-for="(setter, index) in attributeSetters" :key="index">
            <component
              :is="setter.component"
              v-bind="setter.props"
              :props="componentProps"
              :api_name="compApiName"
              :objectContext="mockObjectContext"
              @update:props="handlePropsUpdate"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
let testProps = {};
// 参数信息
import testComp from '@components/web/product_detail/key_info/index.js';
testProps = {
  name: '参数信息',
  showType: '1',
  selectedFields: [
    'barcode',
    'price'
  ]
}

// 商城卡片
// import testComp from '@components/web/product_detail/card/index.js';
// testProps = {
//   "img": "1",
//   "tag": "1",
//   "price": "1",
//   "attr": 0,
//   "stock": "1"
// }

/* 商城列表容器
//import testComp from '@components/web/product_list/container/index.js';
//testProps = {
  "img": "1",
  "tag": "1",
  "price": "1",
  "attr": 0,
  "stock": "1"
}
*/

// 商城列表分类组件
// import testComp from '@components/web/product_list/category-tree/index.js';
// testProps = {
//   "source": "1", // 1: 本地数据，2: 接口数据
//   "level1ImgShow": "1",
//   "prshowAllice": "1"
// }

// 商城子列表组件
/* import testComp from '@components/web/product_list/shop-list/index.js';
testProps = {
    "buttons": [],
    "card_main_info": {
        "tag_apiname": [
            "option2",
            "option4",
            "option1"
        ],
        "picture_apiname": "picture_path",
        "show_fields": [
            "virtual_price_book_price"
        ],
        "name_apiname": "name",
        "price_apiname": "virtual_price_book_price",
        "is_tag_show": true
    },
    "related_list_name": "",
    "view_mode": "card",
    "nameI18nKey": "dht.component.product_all",
    "title": "商品子列表",
    "isSticky": false,
    "is_card_init": true,
    "newHeader": "商品子列表",
    "titleName": "商品子列表",
    "api_name": "dht_web_product_list_shop_list",
    "appId": "FSAID_11490c84",
    "unDeletable": true,
    "header": "商品子列表",
    "is_spu_mode": false,
    "default_view": "card",
    "limit": 1,
    "type": "dht_web_product_list_shop_list",
    "filter_fields": [
      "name",
      "price",
      "safety_stock"
    ],
    "filter_layout": "1",
    "sort_fields": [
      "price"
    ],
    "basic_info": {
      "showName": true
    }
} */

// 调试输出
// console.log('Imported testComp:', testComp);
const beecraft = testComp.beecraft();

// console.log('testComp.beecraft:', beecraft);
import {data, describe } from './mockData.js';

// let product = this.isSpuMode ? spuData : skus[0];

// 后台返回的数据
const mockObjectContext = {
  // 对应webdetail中的data
  data,
  // 对应webdetail中的describe
  describe,
  pageData: {
    isSpuMode: false,
    skus: [data],
    spuData: data
  }
};

// Mock数据
testProps.objectContext = mockObjectContext;


export default {
  name: 'TestComponent',

  components: {
    testComp
  },

  provide() {
    return {
      setProps: this.setProps,
      useInternalNode: this.useInternalNode,
      useInternalEditor: this.useInternalEditor
    }
  },

  data() {
    return {
      compApiName: 'test_comp',
      componentProps: testProps,
      mockObjectContext,
      internalNodes: new Map(),
      selectedNodeId: null,
      editorState: {
        enabled: true,
        nodes: new Set(),
        events: {},
        draggedNodeId: null,
        hoveredNodeId: null
      }
    }
  },

  computed: {
    flattenProps() {
      return {
        ...this.componentProps,
        isPreview: true
      };
    },

    previewComponent() {
      console.log('previewDisplay:', beecraft.related.previewDisplay);
      return beecraft.related?.previewDisplay;
    },

    attributeSetters() {
      console.log('testComp:', testComp);
      console.log('beecraft:', beecraft);

      const settings = beecraft.related?.attributeSettings || [];
      console.log('attributeSettings:', settings);

      return settings.map(setting => {
        const component = setting.data?.setter?.component;
        const props = setting.data?.setter?.props || {};

        return {
          component,
          props: {
            ...props,
            setting: setting.data || {}
          }
        };
      }).filter(setter => setter.component);
    }
  },

  methods: {
    handlePropsUpdate(newProps) {
      this.componentProps = {
        ...this.componentProps,
        ...newProps
      };
      this.$forceUpdate();
    },

    setProps(apiName, callback) {
      callback(this.componentProps);
      this.$forceUpdate();

      this.$emit('props-updated', {
        apiName,
        props: { ...this.componentProps }
      });
    },

    // 实现 useInternalNode
    useInternalNode(nodeId) {
      if (!this.internalNodes.has(nodeId)) {
        this.internalNodes.set(nodeId, {
          id: nodeId,
          data: {
            ...this.componentProps
          },
          props: this.componentProps
        });
      }

      const node = this.internalNodes.get(nodeId);

      return {
        id: nodeId,
        data: {
          ...node.data,
          ...this.componentProps
        },
        props: node.props,
        ...this.componentProps,
        actions: {
          setCustom: (callback) => {
            callback(this.componentProps);
            node.data = {
              ...node.data,
              ...this.componentProps
            };
            this.$forceUpdate();
          },
          setProps: (callback ) => {
            callback(this.componentProps)
            node.props = {
              ...node.props,
              ...this.componentProps
            };
            this.$forceUpdate();
          },
          select: () => {
            this.selectedNodeId = nodeId;
            this.$emit('node-selected', nodeId);
          },
          deselect: () => {
            if (this.selectedNodeId === nodeId) {
              this.selectedNodeId = null;
              this.$emit('node-deselected', nodeId);
            }
          },
          isSelected: () => {
            return this.selectedNodeId === nodeId;
          },
          getParent: () => {
            return null;
          },
          getChildren: () => {
            return [];
          }
        }
      };
    },

    // 实现 useInternalEditor
    useInternalEditor() {
      return {
        getNodes: () => {
          const nodes = {};
          this.internalNodes.forEach((node, id) => {
            nodes[id] = node;
          });
          return nodes;
        },

        getSelectedNode: () => {
          return this.selectedNodeId ? this.internalNodes.get(this.selectedNodeId) : null;
        },

        selectNode: (nodeId) => {
          if (this.internalNodes.has(nodeId)) {
            this.selectedNodeId = nodeId;
            this.$emit('editor-node-selected', nodeId);
          }
        },

        clearSelection: () => {
          this.selectedNodeId = null;
          this.$emit('editor-selection-cleared');
        },

        add: (nodeId, initialData = {}) => {
          if (!this.internalNodes.has(nodeId)) {
            this.internalNodes.set(nodeId, {
              id: nodeId,
              data: initialData,
              props: {}
            });
            this.editorState.nodes.add(nodeId);
            this.$emit('editor-node-added', nodeId);
          }
        },

        delete: (nodeId) => {
          if (this.internalNodes.has(nodeId)) {
            this.internalNodes.delete(nodeId);
            this.editorState.nodes.delete(nodeId);
            if (this.selectedNodeId === nodeId) {
              this.selectedNodeId = null;
            }
            this.$emit('editor-node-deleted', nodeId);
          }
        },

        setEditorEnabled: (enabled) => {
          this.editorState.enabled = enabled;
          this.$emit('editor-state-changed', enabled);
        },

        isEditorEnabled: () => {
          return this.editorState.enabled;
        },

        dragAndDrop: {
          startDragging: (nodeId) => {
            this.editorState.draggedNodeId = nodeId;
            this.$emit('editor-drag-start', nodeId);
          },
          stopDragging: () => {
            const draggedNodeId = this.editorState.draggedNodeId;
            this.editorState.draggedNodeId = null;
            this.$emit('editor-drag-end', draggedNodeId);
          },
          getDraggedNode: () => {
            return this.editorState.draggedNodeId ?
              this.internalNodes.get(this.editorState.draggedNodeId) : null;
          }
        },

        events: {
          on: (eventName, handler) => {
            if (!this.editorState.events[eventName]) {
              this.editorState.events[eventName] = new Set();
            }
            this.editorState.events[eventName].add(handler);
          },
          off: (eventName, handler) => {
            if (this.editorState.events[eventName]) {
              this.editorState.events[eventName].delete(handler);
            }
          }
        },

        query: {
          getNodeCount: () => {
            return this.internalNodes.size;
          },
          hasNode: (nodeId) => {
            return this.internalNodes.has(nodeId);
          },
          getNode: (nodeId) => {
            return this.internalNodes.get(nodeId);
          }
        }
      };
    }
  }
}
</script>

<style scoped>
.test-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.runtime-panel {
  height: 40%;
  border-bottom: 1px solid #eee;
  display: flex;
  flex-direction: column;
}

.preview-setting-panel {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.preview-panel {
  width: 60%;
  border-right: 1px solid #eee;
  display: flex;
  flex-direction: column;
}

.setting-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 12px 16px;
  background: #f6f7f9;
  border-bottom: 1px solid #eee;
  font-weight: 500;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

/* 设置面板的特殊样式 */
.setting-panel .panel-content {
  background-color: #f6f7f9;
}

/* 预览面板的特殊样式 */
.preview-panel .panel-content {
  background-color: #fff;
}
</style>
