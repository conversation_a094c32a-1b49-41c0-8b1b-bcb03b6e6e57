<template>
  <card v-bind="$attrs" v-bind:objectContext="objectContext" />
</template>

<script>
import Card from '../cardDisplay.vue';
import WidgetsDetailMixins from '../widgets/widgets-detail-mixins';
export default {

  name: 'ProductDetailCardBeecraftDisplay',
  mixins: [WidgetsDetailMixins],
  inject: ['useInternalNode'],
  components: {
    Card
  },
  data() {
    return {
    }
  },
  methods: {
    mockData() {

      
    }
  },
  mounted() {
    this.mockData();
  }
}
</script>
