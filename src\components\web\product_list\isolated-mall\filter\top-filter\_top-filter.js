// @vue/component
import FilterMixin from '../filter-mixin.js';

export default {
  name: 'TopFilter',

  components: {},

  mixins: [FilterMixin],

  data() {
    return {
      // 当前展开的字段，同一时间只能展开一个
      currentExpandedField: null
    }
  },

  mounted() {
    // 添加点击外部关闭展开面板的功能
    document.addEventListener('click', this.handleClickOutside);
  },

  beforeDestroy() {
    // 移除事件监听器
    document.removeEventListener('click', this.handleClickOutside);
  },

  computed: {
    /**
     * 是否有任何已选中的值
     */
    hasAnySelectedValue() {
      return this.filter_fields.some(field => this.hasSelectedValue(field));
    }
  },

  methods: {    
    /**
     * 切换筛选项的展开/收起状态
     * 同一时间只能展开一个选项
     */
    toggleSection(fieldName) {
      if (this.currentExpandedField === fieldName) {
        // 如果点击的是当前展开的项，则收起
        this.currentExpandedField = null;
      } else {
        // 否则展开新的项，自动收起之前的项
        this.currentExpandedField = fieldName;
      }
    },

    /**
     * 检查指定字段是否有选中值
     */
    hasSelectedValue(filterField) {
      const val = this.selectedValues[filterField];
      if (Array.isArray(val)) return val.length > 0;
      return !!val;
    },

    /**
     * 获取单选字段的选中标签
     */
    getSelectedLabel(filterField) {
      const val = this.selectedValues[filterField];
      const opts = this.getFieldOptions(filterField);
      const found = opts.find(opt => opt.value === val);
      return found ? found.label : '';
    },

    /**
     * 获取多选字段的选中标签数组
     */
    getSelectedLabels(filterField) {
      const vals = this.selectedValues[filterField] || [];
      const opts = this.getFieldOptions(filterField);
      return opts.filter(opt => vals.includes(opt.value)).map(opt => opt.label);
    },

    /**
     * 获取显示的标签（最多显示2个，超过显示省略）
     */
    getDisplayTags(filterField) {
      const labels = this.getSelectedLabels(filterField);
      const maxDisplay = 2;
      
      if (labels.length <= maxDisplay) {
        return { 
          tags: labels, 
          hasMore: false, 
          moreCount: 0 
        };
      }
      
      return {
        tags: labels.slice(0, maxDisplay),
        hasMore: true,
        moreCount: labels.length - maxDisplay
      };
    },

    /**
     * 清空所有筛选条件
     */
    clearAll() {
      this.resetFilters();
      this.currentExpandedField = null;
    },

    /**
     * 清空当前展开字段的选中值
     */
    clearCurrentField() {
      if (!this.currentExpandedField) return;
      
      const fieldType = this.getFieldType(this.currentExpandedField);
      const clearValue = fieldType === 'select_many' ? [] : '';
      
      this.onFilterChange(this.currentExpandedField, clearValue);
    },
    /**
     * 处理点击外部关闭展开面板
     */
    handleClickOutside(event) {
      // 如果没有展开的面板，不需要处理
      if (!this.currentExpandedField) return;
      
      // 检查点击是否在组件内部
      const topFilterEl = this.$el;
      if (topFilterEl && !topFilterEl.contains(event.target)) {
        this.currentExpandedField = null;
      }
    }
  }
}
