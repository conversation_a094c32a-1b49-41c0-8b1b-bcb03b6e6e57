<template>
  <div class="top-shopping-cart" @click.stop="handleShowCart()">
    <div class="cart-icon" >
      <i class="el-icon-shopping-cart-2"></i>
      <span class="cart-count" v-if="showCount == 1 && cartCount > 0">{{ cartCount }}</span>
    </div>
    <div class="cart-text" v-if="showText === '1'">{{ header }}</div>


    <fx-dialog 
      width="450px"
      sliderPanel
      hasScroll
      :visible.sync="showCart" >
      <shopCart />
    </fx-dialog>
  </div>
</template>

<script>
export default {
  name: 'TopShoppingCart',
  props: {
    header: {
      type: String,
      default: $t('购物车')
    },
    cartCount: {
      type: Number,
      default: 0
    },
    showCount: {
      type: String,
      default: '1'
    },
    showText: {
      type: String,
      default: '1'
    }
  },
  components: {
    'shopCart': () => Fx.getBizComponent('dhtbiz', 'shopCart').then(res => res()) 
  },
  data() {
    return {
      showCart: false,
      // showCartCount: 2
    }
  },
  watch: {
  },
  created() {
    // 监听购物车更新事件
    this.$on('cart-update', this.updateCartCount);    
  },
  methods: {
    handleShowCart() {
      this.showCart = true;
    },
    updateCartCount(data) {
      // if (data && typeof data.count === 'number') {
      //   this.cartCount = data.count;
      // } else if (data && Array.isArray(data.items)) {
      //   this.cartCount = data.items.reduce((total, item) => total + (item.quantity || 0), 0);
      // }
    },
    goToCart() {
      console.log('dht_web_top_shopping_cart goToCart click');
      // 跳转到购物车页面
      // 实际项目中应替换为真实的导航逻辑
      // window.location.href = '/cart';
      
      // 或者使用Vue Router
      // this.$router.push('/cart');
      
      // 或者触发事件让容器组件处理
      this.$emit('cart-click');
    }
  }
}
</script>

<style scoped>
.top-shopping-cart {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.cart-icon {
  position: relative;
  font-size: 24px;
}
.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #f56c6c;
  color: white;
  border-radius: 50%;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  font-size: 12px;
  padding: 0 4px;
}
.cart-text {
  margin-left: 5px;
  font-size: 14px;
}
</style>