<template>
  <div v-if="ready" class="dhtbiz-container-product-list" :style="{ height: isPreview ? '700px' : 'auto' }">
    <div v-if="isPreview">
      product list container running
    </div>

    <slot
      :eventBus="eventBus"
      :dhtPageContext="dhtPageContext"
      :dhtContainerApi="dhtContainerApi"
    ></slot>
  </div>
</template>

<script>
import { loadnsail } from '@/share/utils/load';
// import {loadCss, loadFxModule } from '@utils/load';
import PluginService  from '../../../../../plugins/pluginservice';
import { SMEvent } from '../../plugin-events/index';

// 事件类型常量
const DHT_PAGE_EVENT_TYPES = {
  // 分类相关
  CATEGORY_CHANGE: 'category:change',

  // 搜索相关
  SEARCH_CHANGE: 'search:change',

  // 筛选相关
  FILTER_CHANGE: 'filter:change',

  // 排序相关
  SORT_CHANGE: 'sort:change',

  // 列表相关
  LIST_REFRESH: 'list:refresh',
  LIST_LOADING: 'list:loading',
  LIST_ERROR: 'list:error',
  LIST_DATA_UPDATE: 'list:data:update',

  // 全局重置
  GLOBAL_RESET: 'global:reset'
};

export default {
  name: 'dht_web_container_product_list',

  inject: ['useInternalEditor', 'useInternalNode'],

  props: {
    // 初始搜索关键字
    initialKeyword: {
      type: String,
      default: ''
    },    // 分页配置
    pageConfig: {
      type: Object,
      default: () => ({
        pageSize: 20,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '50', '100']
      })
    }
  },

  data() {
    return {
      isPreview: false,  // 是否处于设计器预览模式下
      ready: false,
      // 事件总线
      eventBus: new Vue(),

      // 业务页面上下文数据
      dhtPageContext: {
        // dht配置信息
        config: {
        },
        // 描述信息
        describe: {
        },
        // 页面状态数据
        pageState: {
          // 当前分类信息
          category: {
            id: null,
            name: '',
            path: []
          },

          // 搜索信息
          search: {
            keyword: this.initialKeyword,
            timestamp: 0
          },

          // 筛选条件
          filters: [
            // {
            //   field_name: 'name',
            //   field_values: ['test'],
            //   operator: 'LIKE'
            // }
          ],

          // 列表状态
          list: {
            loading: false,
            // data: [],
            total: 0,
            page: 1,
            pageSize: this.pageConfig.pageSize,
            error: null
          },

          // 排序类型
          orders: [
            // {
            //   fieldName: 'name',
            //   isAsc: false
            // }
          ]
        }
      }
    };
  },

  computed: {
    // 容器方法，供子组件调用
    dhtContainerApi() {
      return {
        // 获取当前查询参数
        // getQueryParams: this.getQueryParams,
        // refreshList: this.refreshList,

        // 暴露必要的函数, 如 加入购物车, 选规格, 刷新票数等
        $dht: window.$dht,
        // 获取事件类型常量
        getEventTypes: () => DHT_PAGE_EVENT_TYPES
      };
    }
  },

  created() {


  },
  mounted() {
    this.checkIsPreview();
    this.initPlugin().then(() => {
      this.initDhtCore().then((isReady) => {
        this.ready = isReady;
        this.setContainerData();        
        this.initEventListeners();
        this.initializeData();        
      });
    })

  },

  beforeDestroy() {
    // 清理事件监听
    this.eventBus?.$destroy?.();
  },

  methods: {
    initDhtCore() {
      return new Promise((resolve, reject) => {
        // 设计器预览模式, 直接进入
        if(this.isPreview) {
          resolve(true);
          return;
        }

        if(!window.$dht) {
          loadnsail().then(() => {
            resolve(true);
          }).catch(reject);
        } else {
         resolve(true);
        }
      });
    },

    initPlugin() {
      // 新建一个插件
      this.pluginService = new PluginService({
        appId: 'dhtbiz.shopmall.appId',
        objectApiName: 'ShopMall',
        api: {}
      });
      // 初始化插件
      return this.pluginService.init().then(() => {
        if (this.pwcPluginName) {
          // 加载pwc插件
          return this.pluginService.run('pwc.init.before', {
            plugins: [{
              client_type: 'web',
              api_name: this.pwcPluginName,
            }]
          });
        }
      }).then(() => {
        // 执行plugin.init.after钩子
        return this.pluginService.run('plugin.init.after', new SMEvent(this));
      });
    },

    // 检查是否处于设计器预览模式下
    checkIsPreview() {
      const { query } = this.useInternalEditor();
      const { enabled } = query.getOptions();
      this.isPreview = enabled;
    },
    /**
     * 初始化事件监听器
     */
    initEventListeners() {
      // 分类变更事件
      this.eventBus.$on(DHT_PAGE_EVENT_TYPES.CATEGORY_CHANGE, this.handleCategoryChange);

      // 搜索变更事件
      this.eventBus.$on(DHT_PAGE_EVENT_TYPES.SEARCH_CHANGE, this.handleSearchChange);

      // 筛选变更事件
      this.eventBus.$on(DHT_PAGE_EVENT_TYPES.FILTER_CHANGE, this.handleFilterChange);

      // 排序变更事件
      this.eventBus.$on(DHT_PAGE_EVENT_TYPES.SORT_CHANGE, this.handleSortChange);

      // 列表刷新事件
      this.eventBus.$on(DHT_PAGE_EVENT_TYPES.LIST_REFRESH, this.handleListRefresh);

      // 全局重置事件
      this.eventBus.$on(DHT_PAGE_EVENT_TYPES.GLOBAL_RESET, this.handleGlobalReset);
    },

    setContainerData() {
      const { actions } = this.useInternalEditor();
      const { id } = this.useInternalNode();
      actions.history.ignore().setProp(id, ($$data) => {
        this.dhtPageContext.pluginService = this.pluginService;
        $$data.contextNode.data.dhtPageContext = this.dhtPageContext;
        $$data.contextNode.data.dhtContainerApi = this.dhtContainerApi;
      });
    },

    /**
     * 初始化数据
     */
    initializeData() {
      this.pluginService.run('list.render.before', {gg: 'haha'});
      // 如果有初始分类或搜索词，触发列表加载
      if (this.dhtPageContext.pageState.category.id || this.dhtPageContext.pageState.search.keyword) {
        this.$nextTick(() => {
          this.refreshList();
        });
      }
    },

    // 设置dhtPageContext.pageState
    setDhtPageContext(data, key, merge = true) {
      console.log('Container: 设置dhtPageContext.pageState', data, key);

      // 如果 key 为空，则设置整个 pageState
      if (!key) {
        this.dhtPageContext.pageState = merge ? { ...this.dhtPageContext.pageState, ...data } : { ...data };
        const { id } = this.useInternalNode();
        const { actions } = this.useInternalEditor();
        actions.history.ignore().setProp(id, ($$data) => {
            $$data.contextNode.data.dhtPageContext.pageState = this.dhtPageContext.pageState;
        });
        return;
      }

      let setData;
      if (data && typeof data === 'object' && !Array.isArray(data)) {
        // data 是纯对象，合并
        setData = {
          ...(merge ? this.dhtPageContext.pageState[key] : {}),
          ...data
        };
      } else {
        // data 是数组、字符串、数字、布尔值等，直接赋值
        setData = data;
      }
      console.log('Container: 设置dhtPageContext.pageState', setData);
      this.dhtPageContext.pageState[key] = setData;
      const { id } = this.useInternalNode();
      const { actions } = this.useInternalEditor();
      actions.history.ignore().setProp(id, ($$data) => {
          $$data.contextNode.data.dhtPageContext.pageState[key] = setData;
      });
    },

    /**
     * 处理分类变更
     * @param {Object} categoryData - 分类数据 { id, name, path }
     */
    handleCategoryChange(categoryData) {
      console.log('Container: 处理分类变更', categoryData);

      this.setDhtPageContext(categoryData, 'category', false);

      // 重置筛选条件和分页
      this.resetFiltersAndPagination();

      // 刷新列表
      this.refreshList();
    },

    /**
     * 处理搜索变更
     * @param {Object} searchData - 搜索数据 { keyword }
     */
    handleSearchChange(searchData) {
      console.log('Container: 处理搜索变更', searchData);
      this.setDhtPageContext(searchData, 'search');

      // 重置筛选条件和分页
      this.resetFiltersAndPagination();

      // 刷新列表
      this.refreshList();
    },

    /**
     * 处理筛选变更
     * @param {Object} filterData - 筛选数据
     */
    handleFilterChange(filterData) {
      console.log('Container: 处理筛选变更', filterData);
      this.setDhtPageContext(filterData, 'filters');

      // 重置分页到第一页
      this.dhtPageContext.pageState.list.page = 1;

      // 刷新列表
      this.refreshList();
    },

    /**
     * 处理排序变更
     * @param {Object} sortData - 排序数据
     */
    handleSortChange(sortData) {
      console.log('Container: 处理排序变更', sortData);
      this.setDhtPageContext(sortData, 'orders', false);

      // 刷新列表
      this.refreshList();
    },

    /**
     * 处理列表刷新
     * @param {Object} options - 刷新选项
     */
    handleListRefresh(options = {}) {
      console.log('Container: 处理列表刷新', options);

      // 更新分页信息
      if (options.page) {
        this.dhtPageContext.pageState.list.page = options.page;
      }
      if (options.pageSize) {
        this.dhtPageContext.pageState.list.pageSize = options.pageSize;
      }

      // 刷新列表
      this.refreshList();
    },

    /**
     * 处理全局重置
     */
    handleGlobalReset() {
      console.log('Container: 处理全局重置');

      // 重置所有数据到初始状态
      this.dhtPageContext.pageState.category = {
        id: null,
        name: '',
        path: []
      };

      this.dhtPageContext.pageState.search = {
        keyword: '',
        timestamp: 0
      };

      this.resetFiltersAndPagination();

      // 清空列表
      this.dhtPageContext.pageState.list.data = [];
      this.dhtPageContext.pageState.list.total = 0;
    },

    /**
     * 重置分页
     */
    resetFiltersAndPagination() {
      // 重置筛选条件
      this.dhtPageContext.pageState.filters = {
        price: { min: null, max: null },
        brand: [],
        attributes: {},
        custom: {}
      };

      // 重置分页
      this.dhtPageContext.pageState.list.page = 1;
    },

    /**
     * 刷新列表数据
     */
    async refreshList() {
      try {
        // 设置加载状态
        this.dhtPageContext.pageState.list.loading = true;
        this.dhtPageContext.pageState.list.error = null;

        // 通知列表组件开始加载
        this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.LIST_LOADING, true);

        // 构建查询参数
        const queryParams = this.getQueryParams();

        console.log('Container: 刷新列表', queryParams);

        // 这里应该调用实际的API
        // const response = await this.callListApi(queryParams);

        // 模拟API调用
        const response = await this.mockApiCall(queryParams);

        // 更新列表数据
        this.dhtPageContext.pageState.list.data = response.data || [];
        this.dhtPageContext.pageState.list.total = response.total || 0;

        // 通知列表组件数据更新
        this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.LIST_DATA_UPDATE, {
          data: this.dhtPageContext.pageState.list.data,
          total: this.dhtPageContext.pageState.list.total,
          page: this.dhtPageContext.pageState.list.page,
          pageSize: this.dhtPageContext.pageState.list.pageSize
        });

      } catch (error) {
        console.error('Container: 列表刷新失败', error);
        this.dhtPageContext.pageState.list.error = error.message || '加载失败';
        this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.LIST_ERROR, error);
      } finally {
        this.dhtPageContext.pageState.list.loading = false;
        this.eventBus.$emit(DHT_PAGE_EVENT_TYPES.LIST_LOADING, false);
      }
    },

    /**
     * 获取当前查询参数
     * @returns {Object} 查询参数对象
     */
    getQueryParams() {
      const { pageState } = this.dhtPageContext;
      return {
        // 分类参数
        categoryId: pageState.category.id,

        // 搜索参数
        keyword: pageState.search.keyword,

        // 筛选参数
        filters: pageState.filters,

        // 分页参数
        page: pageState.list.page,
        pageSize: pageState.list.pageSize,

        // 排序参数
        orders: pageState.orders
      };
    },


    /**
     * 模拟API调用（实际项目中应该替换为真实API）
     * @param {Object} params - 查询参数
     * @returns {Promise} API响应
     */
    async mockApiCall(params) {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 模拟返回数据
      return {
        data: [
          { id: 1, name: '商品1', price: 100, category: params.categoryId },
          { id: 2, name: '商品2', price: 200, category: params.categoryId },
          { id: 3, name: '商品3', price: 300, category: params.categoryId }
        ].filter(item => {
          // 模拟搜索过滤
          if (params.keyword) {
            return item.name.includes(params.keyword);
          }
          return true;
        }),
        total: 100,
        page: params.page,
        pageSize: params.pageSize
      };
    }
  }
};
</script>


<style lang="less" scoped>
.dht-container-product-list {
  width: 100%;
}
</style>
