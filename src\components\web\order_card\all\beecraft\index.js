const loadWidget = (widgetName) => {
  return new Promise((resolve) => {
      Fx.async(['app-standalone/components/widgets/newwidgets'], function(Wdts) {
        resolve(Wdts[widgetName])
      })
  })
}

// dht_product_list

export default function () {
  return {
    name: 'dht_web_order_card',
    displayName: 'my order',
    related: {
      previewDisplay: () => loadWidget('dht_order_card').then(res => res.displayComponent()).then(res => res),
      attributeSettings: [        
        {
          name: 'SetterField',
          data: {
            setter: {
              component: () => loadWidget('dht_order_card').then(res => res.settingComponent()).then(res => res)
            }
          }
        }]
    }
  }
}