<template>
  <div class="dht-product-detail-card-running">
    <DhtProductDetail v-bind="{...$attrs, ...$props}" :product="product" :objectContext="objectContext"  v-on="$listeners" />
  </div>
</template>

<script>
/* 
  基于Sdk.widgetService.getWidgetApp('goodDetail') 做封装,通过插件化实现组件的客开替换
*/  
import widgetsDetailMixins from './widgets/widgets-detail-mixins';
const DhtProductDetail = function(args) {
    return new Promise((resolve) => {
        Fx.async(['vcrm/sdk'], function(Sdk) {
            Sdk.widgetService.getWidgetApp('goodDetail', {...args}).then((res)=>{
                console.log(res)
                // resolve(res.default)
                resolve(res)
            })
        })
    })
}

export default {
  name: 'dht_web_product_detail_card',
  mixins: [widgetsDetailMixins],
  components: {
    DhtProductDetail
  },
  props: {
    img: {
      type: String,
      default: ''
    },
    tag: {
      type: String,
      default: ''
    },
    price: {
      type: String,
      default: ''
    },
    attr: {
      type: String,
      default: ''
    },
    stock: {
      type: String,
      default: ''
    },
    product: {
      type: Object,
      default: {}
    }
  },
  computed: {
    
  },
  data() {
    return {
      // objfields: dhtBizModel.getObjFields(),
    }
  },
  methods: {
    init() {
    },
  },
  mounted() {
    this.init();
  },
  created() {
    console.log('card2 product', this.product)
  }
};
</script>

<style lang="less" scoped>

.dht-product-detail-card-running {
  font-size: 14px;  
}
</style>
