<template>
  <div class="dht-product-detail-product-preview">
    <div class="dht-product-preview">
      <div class="dht-product-preview-poster one-picture">
        <img src="https://a9.fspage.com/FSR/weex/avatar/object_list/images/list_default_icon.png">            
      </div>          
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductPreview',
  props: {
  }
}
</script>

<style lang="less" scoped>
.dht-product-detail {
  &-product-preview {
    width: 360px;
    flex: none;
    margin-right: 18px;
    background-color: #F7F8FA;
  }

  .dht-product-preview {
    position: relative;
    width: 360px;

    &-poster {
      position: relative;
      width: 360px;
      height: 360px;
      margin-bottom: 20px;
      text-align: center;
      line-height: 358px;
      box-sizing: border-box;
      &.one-picture {
        margin-bottom: 0;
      }

      img {
        width: 180px;
        height: 180px;
        display: inline-block;
        max-width: 100%;
        max-height: 100%;
        vertical-align: middle;
      }
    }
  }
}
</style> 