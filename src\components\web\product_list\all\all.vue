<template>
  <div class="dhtbiz-product-list" style="width: 100%; height: 700px;">
  </div>
</template>

<script>

import {loadCss, loadFxModule, loadnsail  } from '@/share/utils/load';

const ShopmallPageLoader = loadFxModule('crm-modules/page/shopmall/shopmall');
// 依赖crm 中css样式的加载
// crm-dist/assets/style/page-4333aa43ad.css
// crm-dist/assets/style/all
const crmAllCss = loadCss('crm-assets/style/all.css');
const cssModule = loadCss('crm-assets/style/page.css');

export default {
  name: 'dht_web_product_list_all',
  props: {
    apiname: {
      type: String,
      default: 'ShopMall',
    },
  },
  methods: {
    initTable() {
      Promise.all([loadnsail(), crmAllCss, cssModule, ShopmallPageLoader]).then(([sail, crmall, crmPage, CrmList]) => {
        if (this.isDestroy) return;

        // 改页面可能被隐藏
        if (!$('.dhtbiz-product-list')[0]) {
          return;
        }

        this.$list = new CrmList({
          wrapper: $('.dhtbiz-product-list'),
          apiname: this.apiname,
        });

        this.$once('hook:beforeDestroy', () => {
          this.$List && this.$list.destroy();
          this.$list = null;
        });

        this.$list.render();
      });
    },
  },
  created() {
    this.initTable();
  },
  beforeDestroy() {
    this.isDestroy = true;
    this.$list && this.$list.destroy();
    this.$list = null;
  },
};
</script>

<style lang="less" scoped>
.dht-product-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
  /deep/ .crm-table {
    .dt-term-batch {
      z-index: 0;
    }
    .dt-caption {
      z-index: 10;
    }
  }
}
</style>
