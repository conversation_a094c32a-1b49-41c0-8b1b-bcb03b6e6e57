"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[182,401,464],{8182:(t,e,i)=>{i.r(e),i.d(e,{default:()=>c});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dhtbiz dht-product-detail-all-setting"},[i("card-setting"),i("div",{staticClass:"dht-divider-line"}),i("key-info-setting"),i("div",{staticClass:"dht-divider-line"}),i("div",{staticClass:"dht-plugin-select-wrapper"},[i("div",{staticClass:"dht-name dht-title"},[t._v("\n        "+t._s(t.$t("dht.component.web.product_detail_all.custom_plugin","客开插件"))+"\n      ")]),i("div",{staticClass:"dht-plugin-select-content"},[i("fx-select",{staticClass:"dht-plugin-select-select",attrs:{size:"small",options:t.pluginOptions,clearable:"",filterable:""},on:{change:t.handlePluginChange},model:{value:t.pluginApiName,callback:function(e){t.pluginApiName=e},expression:"pluginApiName"}})],1)])],1)};n._withStripped=!0;var s=i(1141),a=i(9464),o=i(8401);const d={name:"WebProductDetailAllSetting",inject:["useInternalNode"],components:{CardSetting:a.default,KeyInfoSetting:o.default},data:function(){return{objectContext:{},pluginOptions:[],pluginApiName:""}},computed:{spuMode:function(){return s.Z.isSpuMode()},mainObjApiName:function(){return s.Z.isSpuMode()?"SPUObj":"ProductObj"}},methods:{init:function(){var t=this.useInternalNode((function(t){return t.data})),e=t.img,i=t.tag,n=t.price,s=t.attr,a=t.stock,o=t.name,d=t.showType,c=t.selectedFields,l=t.pluginApiName;t.objectContext;this.img=e,this.tag=i,this.price=n,this.attr=s+"",this.stock=a,this.name=o,this.showType=d,this.selectedFields=c,this.pluginApiName=l,this.getPluginOptions()},getPluginOptions:function(){var t=this;return new Promise((function(e,i){CRM.util.FHHApi({url:"/EM1HCompBuild/Plugin/queryByTypes",data:{pageSize:20,pageNumber:1,type:"dht_product_detail_plugin",isAll:!0,client:"web"},success:function(i){if(0===i.Result.StatusCode){var n=i.Value.plugins.filter((function(t){return"未发布"!==t.buildResult&&1==t.status})).map((function(t){return{label:t.name,value:t.apiName}}));t.pluginOptions=n,e(n)}else e({})}})}))},handlePluginChange:function(t){this.updateProps("pluginApiName",t)},updateProps:function(t,e){this.useInternalNode().actions.setCustom((function(i){i[t]=e})),this.$set(this,t,e)}},created:function(){this.init()},mounted:function(){}};const c=(0,i(1900).Z)(d,n,[],!1,null,null,null).exports},9464:(t,e,i)=>{i.r(e),i.d(e,{default:()=>d});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dht-product-detail-main-setting"},[i("div",{staticClass:"dht-name dht-title dht-title-card",on:{click:t.toggleShow}},[i("i",{class:t.show?"el-icon-caret-bottom":"el-icon-caret-right"}),t._v("\n      "+t._s(t.$t("dht.component.web.product_detail_card"))+"\n    ")]),i("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"show"}],staticClass:"dht-content"},[i("div",{staticClass:"dht-section"},[i("div",{staticClass:"dht-title"},[t._v(t._s(t.$t("dht.component.web.product_detail_card.img")))]),i("div",{staticClass:"dht-radio-group dht-row-radios"},[i("fx-radio",{attrs:{value:t.img,label:"1"},on:{change:function(e){return t.imgChange("1")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.img.display","显示")))]),i("fx-radio",{attrs:{value:t.img,label:"0"},on:{change:function(e){return t.imgChange("0")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.img.hidden","隐藏")))])],1)]),i("div",{staticClass:"dht-section"},[i("div",{staticClass:"dht-title"},[t._v(t._s(t.$t("dht.component.web.product_detail_card.tag")))]),i("div",{staticClass:"dht-radio-group dht-row-radios"},[i("fx-radio",{attrs:{value:t.tag,label:"1"},on:{change:function(e){return t.tagChange("1")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.tag.display","显示")))]),i("fx-radio",{attrs:{value:t.tag,label:"0"},on:{change:function(e){return t.tagChange("0")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.tag.hidden","隐藏")))])],1)]),i("div",{staticClass:"dht-section"},[i("div",{staticClass:"dht-title"},[t._v("\n              "+t._s(t.$t("dht.component.web.product_detail_card.stock"))+"\n              "),i("fx-link",{attrs:{href:"",target:"_blank",title:t.$t("dht.component.web.product_detail_card.stock.tips")}})],1),i("div",{staticClass:"dht-radio-group dht-row-radios"},[i("fx-radio",{attrs:{value:t.stock,label:"1"},on:{change:function(e){return t.stockChange("1")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.stock.display","显示")))]),i("fx-radio",{attrs:{value:t.stock,label:"0"},on:{change:function(e){return t.stockChange("0")}}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.stock.hidden","隐藏")))])],1)])])])};n._withStripped=!0;var s=i(1141),a=["life_status_before_invalid","life_status","_id"];const o={name:"WebProductDetailCardSetting",inject:["useInternalNode"],components:{},data:function(){return{show:!0,img:"1",tag:"1",price:"1",attr:"0",stock:"1",moreFieldsOptions:[],objectContext:{}}},computed:{spuMode:function(){return s.Z.isSpuMode()},mainObjApiName:function(){return s.Z.isSpuMode()?"SPUObj":"ProductObj"}},methods:{init:function(){var t=this.useInternalNode((function(t){return t.data})),e=t.img,i=t.tag,n=t.price,s=t.attr,a=t.stock;t.objectContext;this.img=e,this.tag=i,this.price=n,this.attr=s+"",this.stock=a},formatFields:function(){var t,e,i=[],n=null===(t=this.objectContext)||void 0===t||null===(e=t.describe)||void 0===e?void 0:e.fields,s={};for(var o in n)n.hasOwnProperty(o)&&"system"!==(s=n[o]).define_type&&a.indexOf(o)<0&&["object_reference","text","currency","number","date","datetime","email","phone","count"].indexOf(s.type)>=0&&i.push({value:o,key:o,label:s.label_r||s.label});return this.moreFieldsOptions=i,i},toggleShow:function(){this.show=!this.show},imgChange:function(t){this.updateProps("img",t)},tagChange:function(t){this.updateProps("tag",t)},priceChange:function(t){this.updateProps("price",t)},bomStyleChange:function(t){this.updateProps("attr",t)},stockChange:function(t){this.updateProps("stock",t)},moreFieldsChange:function(t,e,i){},updateProps:function(t,e){this.useInternalNode().actions.setCustom((function(i){i[t]=e})),this.$set(this,t,e)}},created:function(){this.init()},mounted:function(){}};const d=(0,i(1900).Z)(o,n,[],!1,null,null,null).exports},8401:(t,e,i)=>{i.r(e),i.d(e,{default:()=>r});var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dht-product-detail-key-info-setting"},[i("div",{staticClass:"dht-name dht-title",on:{click:t.toggleShow}},[i("i",{class:t.show?"el-icon-caret-bottom":"el-icon-caret-right"}),t._v("\n\n      "+t._s(t.$t("dht.component.web.product_detail_key_info.title","参数信息"))+"\n    ")]),i("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"show"}],staticClass:"dht-content"},[i("div",{staticClass:"dht-section"},[i("div",{staticClass:"dht-title"},[t._v("\n            "+t._s(t.$t("dht.component.web.product_detail_key_info.show_type","展示样式"))+"\n          ")]),i("div",{staticClass:"dht-types"},t._l(t.showTypes,(function(e,n){return i("div",{key:e.key,staticClass:"dht-cells",class:t.showType==e.val?"dht-selected":"",on:{click:function(i){return i.stopPropagation(),t.showTypeChange(e)}}},[i("div",{staticClass:"dht-box"},[i("i",{staticClass:"dht-icon",class:e.icon})]),i("div",{staticClass:"dht-label"},[t._v(t._s(e.name))])])})),0)]),t.fieldsOptions.length>0?i("div",{staticClass:"dht-section"},[i("div",{staticClass:"dht-title"},[t._v("\n            "+t._s(t.$t("dht.component.web.product_detail_key_info.fields","显示字段"))+"\n          ")]),i("fx-transfer",{attrs:{filterable:"","is-simple":"",data:t.fieldsOptions,draggable:"","target-order0":"push"},on:{change:t.fieldsChange},model:{value:t.selectedFields,callback:function(e){t.selectedFields=e},expression:"selectedFields"}})],1):t._e()])])};n._withStripped=!0;var s=i(5861),a=i(4687),o=i.n(a),d=i(1141),c=["life_status_before_invalid","life_status","_id"];const l={inject:["useInternalNode"],components:{},data:function(){return{show:!0,name:"",showType:"1",selectedFields:[],fieldsOptions:[]}},computed:{showTypes:function(){return[{key:"single",val:"1",name:$t("dht.component.web.product_detail_key_info.single","单列"),icon:"fx-icon-tuliweizhi1"},{key:"double",val:"2",name:$t("dht.component.web.product_detail_key_info.double","双列"),icon:"fx-icon-list-4"}]}},methods:{init:function(){var t=this;return(0,s.Z)(o().mark((function e(){var i,n,s,a;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=t.useInternalNode((function(t){return t.data})),n=i.name,s=i.showType,a=i.selectedFields,t.name=n,t.showType=s,t.selectedFields=a,e.next=6,t.formatFields();case 6:case"end":return e.stop()}}),e)})))()},toggleShow:function(){this.show=!this.show},formatFields:function(){var t=this;return(0,s.Z)(o().mark((function e(){var i,n,s,a;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=[],e.next=3,d.Z.fetchObjFields(d.Z.mainObjApiName());case 3:for(a in n=e.sent,s={},n)n.hasOwnProperty(a)&&"system"!==(s=n[a]).define_type&&c.indexOf(a)<0&&["object_reference","text","currency","number","date","datetime","email","phone","count"].indexOf(s.type)>=0&&i.push({value:a,key:a,label:s.label_r||s.label});return t.fieldsOptions=i,e.abrupt("return",i);case 9:case"end":return e.stop()}}),e)})))()},nameChange:function(t){this.updateProps("name",t)},showTypeChange:function(t){var e=t.val;this.updateProps("showType",e)},fieldsChange:function(t){this.updateProps("selectedFields",t)},updateProps:function(t,e){this.useInternalNode().actions.setCustom((function(i){i[t]=e})),this.$set(this,t,e)}},created:function(){this.init()},mounted:function(){}};const r=(0,i(1900).Z)(l,n,[],!1,null,null,null).exports}}]);