"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[79],{5079:(t,s,i)=>{i.r(s),i.d(s,{default:()=>h});var e=function(){var t=this.$createElement;return(this._self._c||t)("div",{staticClass:"dhtbiz-product-list",staticStyle:{width:"100%",height:"700px"}})};e._withStripped=!0;var l=i(885),r=i(5927),n=(0,r.XC)("crm-modules/page/shopmall/shopmall"),a=(0,r.Yc)("crm-assets/style/page.css");const o={name:"dht_web_product_list_all",props:{apiname:{type:String,default:"ShopMall"}},methods:{initTable:function(){var t=this;Promise.all([(0,r.ii)(),a,n]).then((function(s){var i=(0,l.Z)(s,3),e=(i[0],i[1],i[2]);t.isDestroy||$(".dhtbiz-product-list")[0]&&(t.$list=new e({wrapper:$(".dhtbiz-product-list"),apiname:t.apiname}),t.$once("hook:beforeDestroy",(function(){t.$List&&t.$list.destroy(),t.$list=null})),t.$list.render())}))}},created:function(){this.initTable()},beforeDestroy:function(){this.isDestroy=!0,this.$List&&this.$list.destroy(),this.$list=null}};const h=(0,i(1900).Z)(o,e,[],!1,null,"615f2907",null).exports}}]);