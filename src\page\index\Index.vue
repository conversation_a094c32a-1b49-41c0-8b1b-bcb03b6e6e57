<template>
    <div class="index">
<!--        <app-link to="index" class="xx" target="_blank">Home</app-link>-->
<!--        <app-link to="list">List</app-link>-->
        <div class="con">welcome to use the application template!</div>
    </div>
</template>

<script>
// import AppLink from '@components/TheLink/TheLink.vue';

export default {
    data() {
        return {

        }
    },
    components: {
        // AppLink
    },


    methods: {
        beforeRouteLeave(to, from, next) {
            console.log('leave the route', to, from);
            next();
        },

        beforeRouteUpdate() {
            console.log('parameters', this.$fxRoute.params);
            console.log('only the parameters are changed, it will be triggered');
        }
    }
}
</script>

<style lang="less">
    .index {
        width: 500px;
        padding-top: 200px;
        margin: 0 auto;
        text-align: center;
    }
    .some {
        color: red;
    }
    .con {
        padding: 30px;
        font-size: 22px;
    }
</style>
