const path = require('path');
const webpack = require('webpack');
const { merge } = require('webpack-merge');
const common = require('./webpack.common.js');

let webpackConfig = merge(common, {
    mode: 'development',

    devtool: 'source-map', 
    // devtool: 'eval-cheap-module-source-map',

    output: {
        path: path.resolve(__dirname, '../../dev'),
    },
    externals: require('./externals')(true),

    module: {
        rules: [
            {
                test: /\.(css|less)$/,
                use: [
                    'vue-style-loader',
                    {
                        loader: 'css-loader',
                        options: {
                            esModule: false,
                            sourceMap: true
                        }
                    },
                    {
                        loader: 'postcss-loader',
                        options: {
                            sourceMap: true
                        }
                    },
                    {
                        loader: 'less-loader',
                        options: {
                            sourceMap: true,
                            lessOptions: {
                                javascriptEnabled: true
                            }
                        }
                    },
                    {
                        loader: 'style-resources-loader',
                        options: {
                            patterns: [path.resolve(__dirname, '../../src/assets/css/mixins/*.less')]
                        }
                    }
                ]
            }
        ],
    },

    optimization: {
        chunkIds: 'named',
    },

    performance: {
        maxAssetSize: 250000,
        maxEntrypointSize: 150000,
    },

    plugins: [
        new webpack.DefinePlugin({
            _WEBPACK_PRODUCTION: false,
        })
    ],
});

module.exports = webpackConfig;
