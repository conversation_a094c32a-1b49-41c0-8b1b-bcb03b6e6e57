<div class="dht-card-base-content">
  <div
    class="dht-card-item-img"
    :class="{'dht-card-item-img-default': isDefaultImg}"
    @click="onAction('DETAIL', product)">
    <fx-image
      v-if="options.isLazyImg"
      class="img"
      :src="displayPicture"
      fit="contain"
      :scroll-container="options.scrollContainer"
      @click.stop="onAction('DETAIL', product)"
      :lazy="true">
      <div slot="error" class="dht-lazy-load-error dht-absolute-center">
        <i class="el-icon-picture-outline"></i>
      </div>
    </fx-image>
    <img v-else :src="displayPicture" class="img">
  </div>
  <div class="dht-card-item-info" :class="{'dht-card-jinbei': isJinbei}">
    <fx-popover
        placement="top-start"
        trigger="hover"
        :content="displayName"
        width="200"
        class="item item-display-name dht-card-ellipsis2"
    >
        <span
            class="dht-card-ellipsis2"
            slot="reference"
        >{{ displayName }}</span>
    </fx-popover>
    <div class="item item-price">
      <span class="price-prefix">{{ product.mc_currency__r || defaultCurrencyFlag }}</span>
      <span>{{ displayPrice }}</span>
      <span
        v-if="isShowPriceUnit"
        class="price-unit dht-card-ellipsis"
        :title="priceUnitName"
      >
        /{{ priceUnitName }}
      </span>
    </div>
    <ul class="field-list">
      <li
        class="field-item"
        v-for="field in showFields"
        :key="field.api_name"
      >
        <fx-popover
            placement="top-start"
            trigger="hover"
            :content="field.label"
            width="150"
            class="field-item-label dht-card-ellipsis"
        >
            <span
                class="dht-card-ellipsis"
                slot="reference"
            >{{ field.label }}</span>
        </fx-popover>
        <span class="field-item-symbol">: </span>
        <fx-popover
            placement="top-start"
            trigger="hover"
            :content="field.value"
            width="150"
            class="field-item-value dht-card-ellipsis"
        >
            <span
                class="dht-card-ellipsis"
                slot="reference"
            >{{ field.value }}</span>
        </fx-popover>
      </li>
    </ul>
    <div
      v-if="productConfig.card_main_info.is_tag_show"
      class="item item-tag"
    >
      <product-tag
        v-if="product.hasPricePolicy"
        :style="{color: '#ff8000'}">
        {{ $t('促销') }}
      </product-tag>
      <product-tag
        v-for="option in commodityLabels"
        :key="option.value"
        :style="{color: option.font_color}">
        {{option.label}}
      </product-tag>
    </div>
  </div>
  <div
    v-if="productConfig.card_main_info.is_tag_show && displayTag.length > 0"
    class="dht-left-tag"
  >
    <span 
      v-for="(displayTag, index) in displayTag"
      class="dht-left-tag-text-new"
      :style="{ backgroundColor: displayTag.font_color }"
    >{{ displayTag.label }}</span>
    <!-- <i
      class="dht-left-tag-icon"
      :style="{ backgroundColor: displayTag.font_color }"
    ></i> -->
  </div>
</div>

