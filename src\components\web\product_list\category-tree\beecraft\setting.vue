<template>
  <div class="dht-category-tree-setting">
      <!-- 数据来源 -->
      <div class="dht-section">
        <div class="dht-title">数据来源</div>
        <fx-input
          v-model="source"
          placeholder="请输入数据来源"
          @change="sourceChange"
        />
      </div>

      <!-- 展示图片 -->
      <div class="dht-section">
        <div class="dht-title">一级分类展示图片</div>
        <fx-checkbox
          v-model="level1ImgShowBool"
          @change="level1ImgShowChange"
        >
          显示图片
        </fx-checkbox>
      </div>

      <!-- 仅显示一级分类 -->
      <div class="dht-section">
        <div class="dht-title">仅显示一级分类</div>
        <fx-checkbox
          v-model="onlyFirstLevelBool"
          @change="onlyFirstLevelChange"
        >
          仅显示一级分类
        </fx-checkbox>
      </div>   
  </div>
</template>

<script>
/**
 * 分类树设置组件
 * 功能：
 * 1. 配置数据来源
 * 2. 控制一级分类图片的显示/隐藏
 * 3. 控制是否仅显示一级分类（隐藏二级分类）
 */
export default {
  name: 'dht_web_product_list_category_tree_beecraft_setting',
  inject: ['useInternalNode'],
  props: {
    bizContext: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      show: true,
      source: '',
      level1ImgShow: '1',
      onlyFirstLevel: '0'
    };
  },
  computed: {
    level1ImgShowBool: {
      get() {
        return this.level1ImgShow === '1';
      },
      set(val) {
        this.level1ImgShow = val ? '1' : '0';
      }
    },
    onlyFirstLevelBool: {
      get() {
        return this.onlyFirstLevel === '1'; // 勾选表示"仅显示一级分类"
      },
      set(val) {
        this.onlyFirstLevel = val ? '1' : '0'; // 勾选时为'1'（仅显示一级），不勾选时为'0'（显示全部）
      }
    }
  },
  methods: {
    async init() {
      if (this.useInternalNode) {
        const { source, level1ImgShow, onlyFirstLevel } = this.useInternalNode(node => {
          return node.data;
        });

        this.source = source || '';
        this.level1ImgShow = level1ImgShow || '1';
        this.onlyFirstLevel = onlyFirstLevel || '0';
      }
    },

    toggleShow() {
      this.show = !this.show;
    },

    sourceChange(val) {
      this.updateProps('source', val);
    },

    level1ImgShowChange(val) {
      const stringVal = val ? '1' : '0';
      this.updateProps('level1ImgShow', stringVal);
    },

    onlyFirstLevelChange(val) {
      const stringVal = val ? '1' : '0'; // 勾选为'1'，不勾选为'0'
      this.updateProps('onlyFirstLevel', stringVal);
    },

    updateProps(key, val) {
      const { actions } = this.useInternalNode();

      actions.setCustom((data) => {
        data[key] = val;
      });
      this.$set(this, key, val);
    }
  },
  created() {
    this.init();
  }
}
</script>

<style lang="less" scoped>
.dht-category-tree-setting {
  display: flex;
  flex-direction: column;
  width: 100%;

  .dht-title {
    font-size: 14px;
    color: #181c25;
    margin-bottom: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      transition: transform 0.2s;
    }
  }

  .dht-content {
    padding-left: 20px;
  }

  .dht-section {
    margin-bottom: 20px;

    .dht-title {
      font-size: 12px;
      color: #91959e;
      margin-bottom: 8px;
      cursor: default;

      i {
        display: none;
      }
    }
  }
}
</style>