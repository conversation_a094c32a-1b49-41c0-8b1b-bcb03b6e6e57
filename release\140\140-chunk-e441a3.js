"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[140],{4140:(t,e,i)=>{i.r(e),i.d(e,{default:()=>a});var d=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dhtbiz dht-product-detail-all"},[t.ready?i("goodDetail",{ref:"goodDetail",staticClass:"dht-goodDetail-container",attrs:{isSpuMode:t.isSpuMode,isUseByModal:t.isUseByModal,product:t.product,pageConfig:t.pageConfig,pluginService:t.pluginService}}):t._e()],1)};d._withStripped=!0;var n=i(5927);const o={name:"dht_web_product_detail_all",components:{goodDetail:function(){return Fx.getBizComponent("dhtbiz","goodDetail").then((function(t){return t()}))}},props:{_id:{type:String,default:""},spu_id:{type:String,default:""},pluginService:{type:String,default:function(){}}},computed:{isSpuMode:function(){return window.$dht.config.sail.isSpuMode||!1},isUseByModal:function(){return!1}},data:function(){return{ready:!1,pageConfig:{},editorOptions:{},product:{_id:this._id,spu_id:this.spu_id||""}}},created:function(){var t=this;this.getPageConfig(),window.$dht?this.ready=!0:(0,n.ii)().then((function(){t.product._id=t._id||"62ce853a928dcb000160a157",t.spu_id&&(t.product.spu_id=t.spu_id),t.ready=!0}))},methods:{getPageConfig:function(){var t={};try{(t=localStorage.getItem("dhtbiz_dht_web_product_detail_all")||{})&&(t=JSON.parse(t))}catch(e){t={}}return this.pageConfig=t,t}},mounted:function(){}};const a=(0,i(1900).Z)(o,d,[],!1,null,"0c49d6e2",null).exports}}]);