<template>
  <div :class="className" class="dht-ai-order-progress">
    <div class="dht-label">{{$t('i18n.dhtbiz.dht_ai_order_progress.ai_recognizing'/*AI智能识别中...*/)}}</div>
    <fx-progress :percentage="percentage"></fx-progress>
  </div>
</template>

<script>
export default {
  name: 'DhtAiOrderProgress',
  props: {
    percentage: {
      type: Number,
      required: true
    },
    className: {
      type: String,
      default: ''
    }
  }
};
</script>

<style lang="less" scoped>
.dht-ai-order-progress {
  display: flex;
  justify-content: center;
  width: 50%;

  .dht-label {
    font-size: 13px;
    color: var(--color-neutrals19);
    text-align: center;
    margin-bottom: 10px;
  }
}
</style> 