export default function () {
  return {
    name: 'dht_web_product_detail_all',
    // displayName: 'product detail',
    related: {
      previewDisplay: () => import('./display.vue'),
      attributeSettings: [        
        {
          name: 'SetterField',
          data: {
            //   label: $t('paasbiz.portal-designer.max-content-set', '参数设置'),
            // label: '参数设置',
            // display: 'block',
            setter: {
              component: () => import('./setting.vue')
            }
          }
        }]
    }
  }
}