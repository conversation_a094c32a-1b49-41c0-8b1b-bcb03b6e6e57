<template>
  <div v-if="attr === '0'" class="dht-product-detail-input-wrapper">
    <div class="dht-product-detail-main-item">
      <div class="dht-product-detail-main-label">{{ $t('vcrm.number	','数量') }}</div>
      <div class="dht-product-detail-main-content sku-input-content">
        <fx-input-number :min="1" :max="100" size="small" :step="1" :value="1" />
        <span style="margin-left: 5px;">{{ $t('dht.component.web.product_detail_card.quantity.unit', '只') }}</span>
        <div v-if="stock === '1'" class="stock-info">{{ $t('dht.component.web.product_detail_card.stock', '库存') }}: {{ $t('充足') }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductQuantity',
  props: {
    attr: {
      type: String,
      default: '0'
    },
    stock: {
      type: String,
      default: '1'
    }
  }
}
</script>

<style lang="less" scoped>
@label-color: #999;

.dht-product-detail {
  &-input-wrapper {
    margin: 16px;
    .sku-input-content {
      display: flex;
      align-items: center;
      color: @label-color;
      font-size: 12px;
      .stock-info {
        font-size: 14px;
        margin-left: 10px;
      }
    }
  }
}
</style> 