(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[768],{9510:(t,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>n});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fx-object-input-autocomplete-container"},[a("fx-autocomplete",{staticClass:"fx-object-input-autocomplete",class:{"is-error":t.hasError},attrs:{size:t.size,"fetch-suggestions":t.querySearch,placeholder:t.$t("i18n.dhtbiz.common.please_input")},on:{select:t.handleSelect,blur:t.handleBlur},model:{value:t.showVal,callback:function(e){t.showVal=e},expression:"showVal"}},[a("i",{staticClass:"el-input__icon fx-icon-add-2",attrs:{slot:"suffix"},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.handlePickDate.apply(null,arguments)}},slot:"suffix"})]),t.hasError?a("div",{staticClass:"error-tip"},[a("i",{staticClass:"fx-icon-warning"}),t._v("\n    "+t._s(t.$t("i18n.dhtbiz.common.no_match_data"))+"\n  ")]):t._e()],1)};r._withStripped=!0;const i={name:"fx-object-input-autocomplete",props:{objectApiName:{type:String,default:"AccountObj"},value:{type:Object,default:function(){return{}}},size:{type:String,default:"small"}},data:function(){return{inputValue:{},showVal:"",objDataList:[],hasError:!1}},watch:{value:{handler:function(t){this.setShowVal(t)},deep:!0}},mounted:function(){var t=this;this.querySearch(this.value.name,(function(e){var a=e.find((function(e){return e.name===t.value.name}));!t.value._id&&a?t.setShowVal(a):t.setShowVal(t.value),t.checkError()}))},methods:{setShowVal:function(t){t.value=t.display_name||t.name,this.showVal=t.value,this.inputValue=t,this.checkError()},checkError:function(){var t=this;this.showVal?-1===this.objDataList.findIndex((function(e){return e._id===t.inputValue._id&&e.value===t.showVal}))?this.showError():this.clearError():this.clearError()},showError:function(){this.hasError=!0},clearError:function(){this.hasError=!1},handleBlur:function(){this.checkError(),this.hasError&&(this.inputValue={name:this.showVal,value:this.showVal})},querySearch:function(t,e){var a=this,r={associated_object_describe_api_name:this.objectApiName,search_query_info:'{"limit":10,"offset":0,"filters":[{"field_name":"name","field_values":["'.concat(t,'"],"operator":"LIKE"}]}'),include_describe:!1,include_layout:!1,client:"web"};CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/".concat(this.objectApiName,"/controller/RelatedList"),data:r,success:function(t){if(0===t.Result.StatusCode){var r=t.Value.dataList.map((function(t){return t.value=t.display_name||t.name,t}));a.objDataList=r,e(r)}else e([])}})},handlePickDate:function(){var t=this,e=[];this.inputValue._id&&e.push({_id:this.inputValue._id,name:this.inputValue.name}),CRM.api.pick_data({apiName:this.objectApiName,data:e,single:!0,hideAdd:!0,methods:{select:function(e){var a=e.selected;a&&(a.value=a.display_name||a.name,t.objDataList=[a],t.setShowVal(a))}}})},handleSelect:function(t){this.setShowVal(t)},getInputValue:function(){return this.inputValue}}};const n=(0,a(1900).Z)(i,r,[],!1,null,"38a034f3",null).exports},1819:(t,e,a)=>{"use strict";a.r(e),a.d(e,{default:()=>v});var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("fx-dialog",{attrs:{visible:t.dialogVisible,hasScroll:"",showHeader:"",noHeaderBorderBottom:"","custom-class":"dht-ai-order-dialog",width:"85%"},on:{"update:visible":function(e){t.dialogVisible=e},close:t.closeDialog}},[a("div",{staticClass:"dialog-header-title",attrs:{slot:"title"},slot:"title"},[a("span",{staticClass:"fx-icon-AIgongju"}),a("span",{staticClass:"dialog-title-label"},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.title")))])]),t.percentage<100?a("div",{staticClass:"tabs-container flex-col-fill"},[a("fx-tabs",{model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("fx-tab-pane",{staticClass:"flex-col-fill",attrs:{label:t.$t("i18n.dhtbiz.dht_ai_order.tab_excel"),name:"excel"}},[a("div",{staticClass:"tab-content excel-recognition flex-col-fill"},[a("p",{staticClass:"upload-desc"},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.excel_upload_desc1"))+" \n            "),a("fx-popover",{attrs:{placement:"bottom-start",width:"520",trigger:"hover","popper-class":"dht-excel-demo-popper"},model:{value:t.showExcelDemo,callback:function(e){t.showExcelDemo=e},expression:"showExcelDemo"}},[a("div",{staticClass:"excel-table-container"},[a("table",{staticClass:"excel-table"},[a("thead",[a("tr",[a("th",[t._v("商品编码")]),a("th",[t._v("商品名称")]),a("th",[t._v("规格")]),a("th",[t._v("数量")]),a("th",[t._v("单位")])])]),a("tbody",[a("tr",[a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})])]),a("tr",[a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})])]),a("tr",[a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})]),a("td",[a("div",{staticClass:"skeleton"})])])])]),a("div",{staticClass:"excel-table-tip"},[t._v("\n                  AI会自动识别Excel/xls文件中的商品名称、编码、数量等信息\n                ")])]),a("fx-link",{attrs:{slot:"reference",type:"standard"},slot:"reference"},[t._v("查看示例")])],1)],1),a("div",{staticClass:"main-upload-section flex-col-fill"},[t.percentage>0&&t.percentage<100?a("div",{staticClass:"progress-section flex-col-fill"},[a("fx-progress",{attrs:{percentage:t.percentage}})],1):a("div",{staticClass:"upload-section flex-col-fill"},[a("fx-upload",{class:[{"no-file-list":t.noFile},"upload-component"],attrs:{drag:"",url:t.uploadUrl,"on-remove":t.handleFileRemove,"before-upload":t.handleFileBeforeUpload,"on-success":t.handleFileUploadSuccess,"on-error":t.handleFileUploadError,accept:".xlsx, .xls",limit:1}},[a("i",{staticClass:"fx-icon-upload"}),a("div",{staticClass:"fx-upload__text"},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.upload_text")))]),a("div",{staticClass:"fx-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.file_upload_tip")))])])],1)])])]),a("fx-tab-pane",{staticClass:"flex-col-fill",attrs:{label:t.$t("i18n.dhtbiz.dht_ai_order.tab_image"),name:"image"}},[a("div",{staticClass:"tab-content image-recognition flex-col-fill"},[a("div",{staticClass:"image-examples"},[a("div",{staticClass:"example-list"},[a("div",{staticClass:"example-card example-title-card"},[a("span",{staticClass:"example-title"},[t._v("常用示例")])]),a("div",{staticClass:"example-card"},[a("img",{attrs:{src:t.paperImg,alt:"纸质单据"}}),a("div",{staticClass:"example-label"},[t._v("纸质单据")])]),a("div",{staticClass:"example-card"},[a("img",{attrs:{src:t.handImg,alt:"手写清单"}}),a("div",{staticClass:"example-label"},[t._v("手写清单")])]),a("div",{staticClass:"example-card"},[a("img",{attrs:{src:t.chatImg,alt:"聊天截图"}}),a("div",{staticClass:"example-label"},[t._v("聊天截图")])]),a("div",{staticClass:"example-card"},[a("img",{attrs:{src:t.elecImg,alt:"电子单据"}}),a("div",{staticClass:"example-label"},[t._v("电子单据")])])])]),a("div",{staticClass:"main-upload-section image-upload-section"},[t.percentage>0&&t.percentage<100?a("div",{staticClass:"progress-section flex-col-fill"},[a("fx-progress",{attrs:{percentage:t.percentage}})],1):a("fx-upload",{attrs:{"list-type":"picture",multiple:"",limit:5,url:t.uploadUrl,accept:t.imgAccept,"before-upload":t.handleBeforeUpload,"on-preview":t.handlePictureCardPreview,"on-remove":t.handleImageRemove,"on-success":t.handleImageUploadSuccess,"on-error":t.handleImageUploadError,"file-list":t.imageList}},[a("i",{staticClass:"el-icon-plus",attrs:{slot:"default"},slot:"default"}),a("div",{staticClass:"fx-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.image_upload_tip")))])]),a("fx-dialog",{attrs:{visible:t.imagePreviewVisible,"append-to-body":""},on:{"update:visible":function(e){t.imagePreviewVisible=e}}},[a("img",{attrs:{width:"100%",src:t.imagePreviewUrl,alt:""}})])],1)])]),a("fx-tab-pane",{staticClass:"flex-col-fill",attrs:{label:t.$t("i18n.dhtbiz.dht_ai_order.tab_text"),name:"text"}},[a("div",{staticClass:"tab-content text-recognition flex-col-fill"},[a("p",{staticClass:"text-desc"},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.text_desc")))]),a("div",{staticClass:"main-upload-section textarea-section flex-col-fill"},[t.percentage>0&&t.percentage<100?a("div",{staticClass:"progress-section flex-col-fill"},[a("fx-progress",{attrs:{percentage:t.percentage}})],1):a("fx-input",{staticClass:"textarea-input flex-col-fill",attrs:{type:"textarea",rows:12,placeholder:t.$t("i18n.dhtbiz.dht_ai_order.text_placeholder")},model:{value:t.textContent,callback:function(e){t.textContent=e},expression:"textContent"}})],1)])])],1),a("div",{staticClass:"button-container"},[a("fx-button",{attrs:{type:"primary"},on:{click:t.handleIdentify}},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.start_identify")))])],1)],1):t._e(),t.percentage>=100?a("div",{staticClass:"results-container"},[a("div",{staticClass:"results-header"},[a("div",{staticClass:"header-left"},[a("span",{staticClass:"back-link",on:{click:t.handleBack}},[a("i",{staticClass:"fx-icon-arrow-left"}),t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.back")))]),a("span",{staticClass:"header-title"},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.match_result")))])]),a("div",{staticClass:"customer-input-section"},[a("label",{staticClass:"required-label"},[a("span",{staticClass:"required-label-red"},[t._v("*")]),t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.customer_name")))]),a("fx-object-input-autocomplete",{ref:"customerInput",attrs:{"object-api-name":"AccountObj",size:"micro",value:t.customer}})],1)]),a("fx-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData,height:"300"}},[a("fx-table-column",{attrs:{prop:"keyword",label:t.$t("i18n.dhtbiz.dht_ai_order.keyword1"),sortable:""}}),a("fx-table-column",{attrs:{prop:"keyword2",label:t.$t("i18n.dhtbiz.dht_ai_order.keyword2"),sortable:""}}),a("fx-table-column",{attrs:{prop:"product",label:t.$t("i18n.dhtbiz.dht_ai_order.product"),width:"180",sortable:""},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row._id?a("fx-link",{attrs:{type:"standard",size:"small"},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.handleProductDetail(e.row)}}},[t._v(t._s(e.row.display_name||e.row.name||"-"))]):a("span",[t._v("-")])]}}],null,!1,**********)}),a("fx-table-column",{attrs:{prop:"product_spec",label:t.$t("i18n.dhtbiz.dht_ai_order.spec"),sortable:""}}),a("fx-table-column",{attrs:{prop:"product_code",label:t.$t("i18n.dhtbiz.dht_ai_order.code"),sortable:""}}),a("fx-table-column",{attrs:{prop:"quantity",label:t.$t("i18n.dhtbiz.dht_ai_order.quantity"),sortable:""}}),a("fx-table-column",{attrs:{prop:"status",label:t.$t("i18n.dhtbiz.dht_ai_order.match_status"),sortable:""},scopedSlots:t._u([{key:"default",fn:function(e){return[1===e.row.status?a("span",{staticClass:"success-status"},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.match_success")))]):a("span",{staticClass:"fail-status"},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.match_fail")))])]}}],null,!1,493104991)}),a("fx-table-column",{attrs:{label:t.$t("i18n.dhtbiz.dht_ai_order.operation"),width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"action-buttons"},[a("fx-link",{attrs:{type:"standard",size:"small"},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.handleEditRow(e.row)}}},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.change")))]),a("fx-link",{attrs:{type:"standard",size:"small"},on:{click:function(a){return a.stopPropagation(),a.preventDefault(),t.handleDeleteRow(e.row)}}},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.delete")))])],1)]}}],null,!1,991406877)})],1),a("div",{staticClass:"results-footer"},[a("div",{staticClass:"footer-left recognize-summary"},[a("span",{staticClass:"result-count"},[t._v(t._s(t.successCount))]),a("span",[t._v(" "+t._s(t.$t("i18n.dhtbiz.dht_ai_order.success_count",{count:""})))]),t._v("，\n\n        "),a("span",{staticClass:"result-count"},[t._v(t._s(t.failCount))]),a("span",[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.fail_count",{count:""})))])]),a("div",{staticClass:"footer-right"},[a("fx-button",{attrs:{type:"primary"},on:{click:t.handleGenerateOrder}},[t._v(t._s(t.$t("i18n.dhtbiz.dht_ai_order.generate_order")))])],1)])],1):t._e()])};r._withStripped=!0;var i=a(4942),n=a(2982),o=a(5861),s=a(4687),l=a.n(s),c=a(9510);const u=a.p+"assets/img/dht-paper-docs.f2b94a.png",d=a.p+"assets/img/dht-hand-written.c29539.png",p=a.p+"assets/img/dht-chat-screenshots.a331fe.png",h=a.p+"assets/img/dht-electronic-docs.272f8f.png";function f(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function m(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?f(Object(a),!0).forEach((function(e){(0,i.Z)(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):f(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}const _={name:"DhtAiOrder",components:{FxObjectInputAutocomplete:c.default},props:{visible:{type:Boolean,default:!1}},computed:{uploadUrl:function(){var t="";return window.$dht&&(t="?appid=FSAID_11490c84"),"/FSC/EM/File/UploadByForm"+t},noFile:function(){var t;return!(null!==(t=this.excelFile)&&void 0!==t&&t.fileName)},dialogVisible:{get:function(){return this.visible},set:function(t){this.$emit("update:visible",t)}},imgAccept:function(){return"image/*"},orderImgDemo:function(){var t=FS_STATIC_PATH.replace("/html","");return"".concat(t,"/assets/dht/web/ai-order-img-demo.png")}},data:function(){return{paperImg:u,handImg:d,chatImg:p,elecImg:h,activeTab:"excel",showExcelDemo:!1,customer:{name:""},tableData:[],currentPage:1,pageSize:10,excelFile:{},imageList:[],imagePreviewVisible:!1,imagePreviewUrl:"",textContent:"",percentage:0,successCount:0,failCount:0,aiResProducts:[],searchKey:"name",useSearchKey:!1,productList:[]}},methods:{closeDialog:function(){this.dialogVisible=!1,this.percentage=0,this.$emit("close")},handleFileBeforeUpload:function(t){return!!(t[0].size/1048576<2)||(CRM.util.alert($t("i18n.dhtbiz.dht_ai_order.upload_excel_first")),!1)},handleFileUploadSuccess:function(t,e,a){this.excelFile={fileName:e.name,fileNpath:t.TempFileName,size:e.size}},handleFileUploadError:function(t,e,a){CRM.util.alert($t("i18n.dhtbiz.dht_ai_order.excel_upload_error"))},handleFileRemove:function(t,e){this.excelFile={}},handleBeforeUpload:function(t){return!!(t[0].size/1048576<10)||(CRM.util.alert($t("i18n.dhtbiz.dht_ai_order.image_size_limit")),!1)},handleImageUploadSuccess:function(t,e,a){this.imageList=a},handleImageUploadError:function(t,e,a){CRM.util.alert($t("i18n.dhtbiz.dht_ai_order.image_upload_error"))},handleImageRemove:function(t,e){this.imageList=e},handlePictureCardPreview:function(t){this.imagePreviewUrl=t.url,this.imagePreviewVisible=!0},showPercentage:function(){var t=this;this.percentage>0&&this.percentage<100||(this.percentage=0,this.progressTimer=setInterval((function(){t.percentage<98?t.percentage+=2:(clearInterval(t.progressTimer),t.progressTimer=null)}),100))},clearLoading:function(){this.progressTimer&&(clearInterval(this.progressTimer),this.progressTimer=null,this.percentage=0),CRM.util.hideLoading_tip()},handleIdentify:function(){var t=this;if(!(this.percentage>0&&this.percentage<100)){var e={excel:function(){return t.identify_excel()},image:function(){return t.identify_image()},text:function(){return t.identify_text()}}[this.activeTab];e&&e()}},fetchAiIdentify:function(t){var e=this,a={};return Array.isArray(t)?t.forEach((function(t){a[t.name]=t.value})):a=t,a.maxTokens=4096,new Promise((function(t,r){CRM.util.FHHApi({url:"/EM1HDHT/API/v1/object/agent/service/prompt_completions",data:a,success:function(a){try{if(0===a.Result.StatusCode){var i,n=null===(i=a.Value)||void 0===i?void 0:i.message;n&&(n=n.replace(/^```json\n/,"").replace(/\n```$/,""));var o=[],s=n?JSON.parse(n):{};s.errMsg?r(s.errMsg):(o=s.productList||[],s.customerName&&(e.customer.name=s.customerName),s.searchKey?e.searchKey=s.searchKey:e.searchKey="name"),e.aiResProducts=o,t(o)}}catch(t){r(t)}}})}))},fetchProductData:function(t){var e=this;if(t.length<=0)return Promise.reject($t("i18n.dhtbiz.dht_ai_order.no_product_info"));var a=!1;"name"!==e.searchKey&&t.length>0&&function(t){if(!Array.isArray(t)||0===t.length)return!1;var e=0,a=0;return t.forEach((function(t){t.hasOwnProperty("prdCode")&&(e++,t.prdCode&&a++)})),e>0&&a/e>=.6}(t)&&(a=!0),e.useSearchKey=a;var r="name";a&&(r=e.searchKey);var i=t.map((function(t){return'"'.concat(a?t.prdCode:t.name,'"')})).join(","),n={serializeEmpty:!1,extractExtendInfo:!1,object_describe_api_name:"ProductObj",include_describe:!1,include_layout:!1,need_tag:!1,search_template_type:"default",ignore_scene_record_type:!0,search_query_info:'{"limit":200,"offset":0,"filters":[{"field_name":"'.concat(r,'","field_values":[').concat(i,'],"operator":"IN"}],"orders":[{"fieldName":"last_modified_time","isAsc":false}]}'),pageSizeOption:[200]};return new Promise((function(t,a){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/ProductObj/controller/List",data:n,success:function(a){if(0===a.Result.StatusCode){var r=a.Value.dataList;e.productList=r,t(r)}}})}))},updateCount:function(t,e){var a=this;"delete"===e?(1===t.status?this.successCount--:this.failCount--,this.aiResProducts=this.aiResProducts.filter((function(e){return a.prdCode?e.prdCode!==t.prdCode:e.name!==t.name}))):"select"===e&&1!=t.status&&(this.successCount++,this.failCount--)},updateTableData:function(t){var e=this;this.failCount=0,this.successCount=0;var a=e.aiResProducts,r={},i=[];a.forEach((function(a){e.useSearchKey?(r.keyword=a.prdCode,r.keyword2=a.name):r.keyword=a.name;var n=t.find((function(t){return e.useSearchKey?t[e.searchKey]===a.prdCode:t.name===a.name}));n?(r.status=1,e.successCount++,Object.assign(r,n)):(r.status=0,e.failCount++),r.quantity=a.num,i.push(r),r={}})),e.tableData=i},identify_common:function(t){var e=this;this.showPercentage(),CRM.util.showLoading_tip($t("AI识别中...")),this.fetchAiIdentify(t).then((function(t){return e.fetchProductData(t)})).then((function(t){e.updateTableData(t),e.percentage=100})).catch((function(t){e.percentage=0,CRM.util.alert(t),e.clearLoading()})).finally((function(){CRM.util.hideLoading_tip()}))},identify_excel:function(){var t=this;return(0,o.Z)(l().mark((function e(){var a,r;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if((a=t).excelFile.fileName){e.next=4;break}return CRM.util.alert("请先上传excel文件"),e.abrupt("return");case 4:r=[{name:"apiName",type:"String",value:"prompt_content_from_excel__c"},{name:"sceneVariables",type:"Map",value:{fileName:a.excelFile.fileName,fileNpath:a.excelFile.fileNpath,sheet:"Sheet1"}}],a.identify_common(r);case 6:case"end":return e.stop()}}),e)})))()},identify_image:function(){var t=this;if(t.imageList.length<=0)CRM.util.alert($t("i18n.dhtbiz.dht_ai_order.upload_image_first"));else{var e=[{name:"apiName",type:"String",value:"prompt_product_info_from_image__c"},{name:"supportImage",type:"Boolean",value:!0},{name:"imageStrings",type:"Array",value:t.imageList.map((function(t){return t.response.TempFileName}))},{name:"imageSetting",type:"Map",value:{variableType:2,objectApiName:"",objectFieldName:"",sceneVariableName:"imgNpath"}}];t.identify_common(e)}},identify_text:function(){var t=this;if(t.textContent){var e=[{name:"apiName",type:"String",value:"prompt_extract_product_info_from_text__c"},{name:"sceneVariables",type:"Map",value:{inputText:t.textContent}}];t.identify_common(e)}else CRM.util.alert($t("i18n.dhtbiz.dht_ai_order.input_text_first"))},handleExport:function(){},handleGenerateOrder:function(){var t=this,e={};if(t.failCount>0)CRM.util.alert($t("i18n.dhtbiz.dht_ai_order.match_fail_tip",{count:t.failCount}));else{var a=t.$refs.customerInput.getInputValue();if(a&&a._id){e.account_id=a._id,e.account_id__r=a.name;var r=this.tableData;e.addMdData={product:(0,n.Z)(r),recordType:"default__c"};var i={apiname:"SalesOrderObj",source:"quickorder",showDetail:!0,noPreCalculate:!0,isSubmitAndCreate:!1,data:e,success:function(t,e,a,r){}};CRM.api.add(i)}else CRM.util.alert($t("i18n.dhtbiz.dht_ai_order.select_customer_first"))}},handleProductDetail:function(t){var e=t._id;e&&CRM.api.show_crm_detail({apiName:"ProductObj",id:e})},handleEditRow:function(t){var e,a=this,r=[];t._id&&r.push({_id:t._id,name:t.display_name||t.name}),CRM.api.pick_data({apiName:"ProductObj",data:r,single:!0,hideAdd:!0,methods:{select:(e=(0,o.Z)(l().mark((function e(r){var i,n;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(i=r.selected)&&(a.updateCount(t,"select"),-1!==(n=a.tableData.findIndex((function(e){return e._id===t._id})))&&a.$set(a.tableData,n,m(m(m({},t),i),{},{status:1})));case 3:case"end":return e.stop()}}),e)}))),function(t){return e.apply(this,arguments)})}})},handleDeleteRow:function(t){this.tableData=this.tableData.filter((function(e){return e._id!==t._id})),this.updateCount(t,"delete")},handleSizeChange:function(t){this.pageSize=t},handleCurrentChange:function(t){this.currentPage=t},handleBack:function(){this.percentage=0}}};const v=(0,a(1900).Z)(_,r,[],!1,null,"35d89319",null).exports},7061:(t,e,a)=>{var r=a(8698).default;function i(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t.exports=i=function(){return a},t.exports.__esModule=!0,t.exports.default=t.exports;var e,a={},n=Object.prototype,o=n.hasOwnProperty,s=Object.defineProperty||function(t,e,a){t[e]=a.value},l="function"==typeof Symbol?Symbol:{},c=l.iterator||"@@iterator",u=l.asyncIterator||"@@asyncIterator",d=l.toStringTag||"@@toStringTag";function p(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{p({},"")}catch(e){p=function(t,e,a){return t[e]=a}}function h(t,e,a,r){var i=e&&e.prototype instanceof y?e:y,n=Object.create(i.prototype),o=new I(r||[]);return s(n,"_invoke",{value:P(t,a,o)}),n}function f(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(t){return{type:"throw",arg:t}}}a.wrap=h;var m="suspendedStart",_="suspendedYield",v="executing",g="completed",b={};function y(){}function x(){}function C(){}var w={};p(w,c,(function(){return this}));var k=Object.getPrototypeOf,E=k&&k(k(D([])));E&&E!==n&&o.call(E,c)&&(w=E);var S=C.prototype=y.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(e){p(t,e,(function(t){return this._invoke(e,t)}))}))}function z(t,e){function a(i,n,s,l){var c=f(t[i],t,n);if("throw"!==c.type){var u=c.arg,d=u.value;return d&&"object"==r(d)&&o.call(d,"__await")?e.resolve(d.__await).then((function(t){a("next",t,s,l)}),(function(t){a("throw",t,s,l)})):e.resolve(d).then((function(t){u.value=t,s(u)}),(function(t){return a("throw",t,s,l)}))}l(c.arg)}var i;s(this,"_invoke",{value:function(t,r){function n(){return new e((function(e,i){a(t,r,e,i)}))}return i=i?i.then(n,n):n()}})}function P(t,a,r){var i=m;return function(n,o){if(i===v)throw Error("Generator is already running");if(i===g){if("throw"===n)throw o;return{value:e,done:!0}}for(r.method=n,r.arg=o;;){var s=r.delegate;if(s){var l=L(s,r);if(l){if(l===b)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===m)throw i=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=v;var c=f(t,a,r);if("normal"===c.type){if(i=r.done?g:_,c.arg===b)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(i=g,r.method="throw",r.arg=c.arg)}}}function L(t,a){var r=a.method,i=t.iterator[r];if(i===e)return a.delegate=null,"throw"===r&&t.iterator.return&&(a.method="return",a.arg=e,L(t,a),"throw"===a.method)||"return"!==r&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+r+"' method")),b;var n=f(i,t.iterator,a.arg);if("throw"===n.type)return a.method="throw",a.arg=n.arg,a.delegate=null,b;var o=n.arg;return o?o.done?(a[t.resultName]=o.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=e),a.delegate=null,b):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,b)}function $(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach($,this),this.reset(!0)}function D(t){if(t||""===t){var a=t[c];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,n=function a(){for(;++i<t.length;)if(o.call(t,i))return a.value=t[i],a.done=!1,a;return a.value=e,a.done=!0,a};return n.next=n}}throw new TypeError(r(t)+" is not iterable")}return x.prototype=C,s(S,"constructor",{value:C,configurable:!0}),s(C,"constructor",{value:x,configurable:!0}),x.displayName=p(C,d,"GeneratorFunction"),a.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===x||"GeneratorFunction"===(e.displayName||e.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,C):(t.__proto__=C,p(t,d,"GeneratorFunction")),t.prototype=Object.create(S),t},a.awrap=function(t){return{__await:t}},j(z.prototype),p(z.prototype,u,(function(){return this})),a.AsyncIterator=z,a.async=function(t,e,r,i,n){void 0===n&&(n=Promise);var o=new z(h(t,e,r,i),n);return a.isGeneratorFunction(e)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},j(S),p(S,d,"Generator"),p(S,c,(function(){return this})),p(S,"toString",(function(){return"[object Generator]"})),a.keys=function(t){var e=Object(t),a=[];for(var r in e)a.push(r);return a.reverse(),function t(){for(;a.length;){var r=a.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},a.values=D,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!t)for(var a in this)"t"===a.charAt(0)&&o.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var a=this;function r(r,i){return s.type="throw",s.arg=t,a.next=r,i&&(a.method="next",a.arg=e),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i],s=n.completion;if("root"===n.tryLoc)return r("end");if(n.tryLoc<=this.prev){var l=o.call(n,"catchLoc"),c=o.call(n,"finallyLoc");if(l&&c){if(this.prev<n.catchLoc)return r(n.catchLoc,!0);if(this.prev<n.finallyLoc)return r(n.finallyLoc)}else if(l){if(this.prev<n.catchLoc)return r(n.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return r(n.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var n=i?i.completion:{};return n.type=t,n.arg=e,i?(this.method="next",this.next=i.finallyLoc,b):this.complete(n)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),O(a),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var r=a.completion;if("throw"===r.type){var i=r.arg;O(a)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,a,r){return this.delegate={iterator:D(t),resultName:a,nextLoc:r},"next"===this.method&&(this.arg=e),b}},a}t.exports=i,t.exports.__esModule=!0,t.exports.default=t.exports},8698:t=>{function e(a){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(a)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},4687:(t,e,a)=>{var r=a(7061)();t.exports=r;try{regeneratorRuntime=r}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}},5861:(t,e,a)=>{"use strict";function r(t,e,a,r,i,n,o){try{var s=t[n](o),l=s.value}catch(t){return void a(t)}s.done?e(l):Promise.resolve(l).then(r,i)}function i(t){return function(){var e=this,a=arguments;return new Promise((function(i,n){var o=t.apply(e,a);function s(t){r(o,i,n,s,l,"next",t)}function l(t){r(o,i,n,s,l,"throw",t)}s(void 0)}))}}a.d(e,{Z:()=>i})},2982:(t,e,a)=>{"use strict";a.d(e,{Z:()=>n});var r=a(907);var i=a(181);function n(t){return function(t){if(Array.isArray(t))return(0,r.Z)(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||(0,i.Z)(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}}}]);