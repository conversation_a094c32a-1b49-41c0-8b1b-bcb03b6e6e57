<svg width="1100" height="812" viewBox="0 0 1100 812" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2332_7968)">
<rect width="1100" height="812" fill="#FBFCFF"/>
<g filter="url(#filter0_f_2332_7968)">
<ellipse cx="440.725" cy="-189" rx="137.667" ry="138.672" fill="#9BF6FC"/>
</g>
<g filter="url(#filter1_f_2332_7968)">
<ellipse cx="658.333" cy="-189" rx="137.667" ry="138.672" fill="#F694FF"/>
</g>
</g>
<defs>
<filter id="filter0_f_2332_7968" x="-96.9424" y="-727.672" width="1075.33" height="1077.34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_2332_7968"/>
</filter>
<filter id="filter1_f_2332_7968" x="120.666" y="-727.672" width="1075.33" height="1077.34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_2332_7968"/>
</filter>
<clipPath id="clip0_2332_7968">
<rect width="1100" height="812" fill="white"/>
</clipPath>
</defs>
</svg>
