!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("Dhtbiz",[],e):"object"==typeof exports?exports.Dhtbiz=e():t.Dhtbiz=e()}(self,(()=>(()=>{var t,e,n,r,i={3711:(t,e,n)=>{"use strict";n.r(e),n.d(e,{AiOrder:()=>r,default:()=>i});var r=function(){return Promise.all([n.e(768),n.e(568)]).then(n.bind(n,2174))};const i={AiOrder:r}},4400:(t,e,n)=>{"use strict";n.d(e,{Z:()=>i});var r=function(){return Promise.all([n.e(773),n.e(748)]).then(n.bind(n,4773))};r.beecraft=function(){return{name:"dht_web_product_detail_card",displayName:$t("dht.component.web.product_detail_card","商品卡片"),data:{name:$t("dht.component.web.product_detail_card","商品卡片")},$$data:{isCanvas:!0},rules:{canDrag:function(){return!1}},related:{previewDisplay:function(){return Promise.all([n.e(773),n.e(880)]).then(n.bind(n,7525))},attributeSettings:[{name:"SetterField",data:{display:"block",setter:{component:function(){return Promise.all([n.e(141),n.e(464)]).then(n.bind(n,9464))}}}}]}}};const i=r},5927:(t,e,n)=>{"use strict";n.d(e,{XC:()=>i,Yc:()=>r,ii:()=>o});var r=function(t){return new Promise((function(e,n){Fx.async(t,(function(t){e(t)}))}))},i=function(t){return new Promise((function(e,n){Fx.async(t,(function(t){e(t)}))}))},o=function(){var t=null,e=!1;return"dev.ceshi112.com"===window.location.host||"local.ceshi112.com"===window.location.host?Promise.resolve():(t||(t=new Promise((function(n,r){var i=window.PAAS_CONFIG,o=Fx.staticPath+"/nsail-dist/"+(i.nsailDhtJsApp||"dht.js");Fx.use(o,(function(r){t=null,e?n():r.appWillMount().then((function(){e=!0,n()})).catch((function(){n()}))}))}))),t)}},4042:(t,e,n)=>{"use strict";n.r(e),n.d(e,{default:()=>o});var r=function(t,e){return(0,e._c)("a",{staticClass:"test-link",attrs:{href:"#project/dhtbiz/"+e.props.to}},[e._v("\n    link test\n    "),e._t("default")],2)};r._withStripped=!0;const i={name:"TheLink"};const o=(0,n(1900).Z)(i,r,[],!0,null,"2321a956",null).exports},1900:(t,e,n)=>{"use strict";function r(t,e,n,r,i,o,a,c){var u,s="function"==typeof t?t.options:t;if(e&&(s.render=e,s.staticRenderFns=n,s._compiled=!0),r&&(s.functional=!0),o&&(s._scopeId="data-v-"+o),a?(u=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),i&&i.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(a)},s._ssrRegister=u):i&&(u=c?function(){i.call(this,(s.functional?this.parent:this).$root.$options.shadowRoot)}:i),u)if(s.functional){s._injectStyles=u;var l=s.render;s.render=function(t,e){return u.call(e),l(t,e)}}else{var d=s.beforeCreate;s.beforeCreate=d?[].concat(d,u):[u]}return{exports:t,options:s}}n.d(e,{Z:()=>r})},2305:(t,e,n)=>{n.p=Fx.staticPath+"/dhtbiz/"},9691:t=>{function e(t){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}e.keys=()=>[],e.resolve=e,e.id=9691,t.exports=e},4328:(t,e,n)=>{var r={"./index.js":3711};function i(t){var e=o(t);return n(e)}function o(t){if(!n.o(r,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return r[t]}i.keys=function(){return Object.keys(r)},i.resolve=o,t.exports=i,i.id=4328},907:(t,e,n)=>{"use strict";function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}n.d(e,{Z:()=>r})},4942:(t,e,n)=>{"use strict";n.d(e,{Z:()=>i});var r=n(2881);function i(t,e,n){return(e=(0,r.Z)(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}},885:(t,e,n)=>{"use strict";n.d(e,{Z:()=>i});var r=n(181);function i(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,c=[],u=!0,s=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(c.push(r.value),c.length!==e);u=!0);}catch(t){s=!0,i=t}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return c}}(t,e)||(0,r.Z)(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},2881:(t,e,n)=>{"use strict";function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}function i(t){var e=function(t,e){if("object"!=r(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,e||"default");if("object"!=r(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==r(e)?e:e+""}n.d(e,{Z:()=>i})},181:(t,e,n)=>{"use strict";n.d(e,{Z:()=>i});var r=n(907);function i(t,e){if(t){if("string"==typeof t)return(0,r.Z)(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?(0,r.Z)(t,e):void 0}}}},o={};function a(t){var e=o[t];if(void 0!==e)return e.exports;var n=o[t]={exports:{}};return i[t](n,n.exports,a),n.exports}a.m=i,a.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return a.d(e,{a:e}),e},a.d=(t,e)=>{for(var n in e)a.o(e,n)&&!a.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},a.f={},a.e=t=>Promise.all(Object.keys(a.f).reduce(((e,n)=>(a.f[n](t,e),e)),[])),a.u=t=>(({43:"test",93:"list",150:"orderlist",167:"detail",187:"detail2",269:"ts",598:"design"}[t]||t)+"/"+({43:"test",93:"list",150:"orderlist",167:"detail",187:"detail2",269:"ts",598:"design"}[t]||t)+"-chunk-"+{43:"bd4892",79:"891301",82:"c14cc4",93:"ae939c",140:"e441a3",141:"5361e6",150:"66f09e",164:"7ba9ff",167:"2236da",182:"7c97f0",187:"993cdf",242:"ef6f34",269:"8e6183",401:"426bb6",452:"e8aede",464:"02924f",510:"f7cbd9",568:"7d9a2b",598:"e42bdf",602:"db64f2",630:"969296",724:"c0a3a0",748:"21af42",768:"a5e107",773:"bbb2a2",880:"ef0a6d",905:"758ed1",927:"01ad5f"}[t]+".js"),a.miniCssF=t=>(({43:"test",93:"list",150:"orderlist",167:"detail",187:"detail2",269:"ts",598:"design"}[t]||t)+"/"+({43:"test",93:"list",150:"orderlist",167:"detail",187:"detail2",269:"ts",598:"design"}[t]||t)+"-"+{43:"092fdd",79:"6444c5",82:"32357f",93:"1445a0",150:"d83c94",164:"435d60",167:"c67b0c",182:"5e269e",187:"c67b0c",242:"b408ab",269:"31d6cf",401:"1cd067",452:"af5ca5",464:"97232a",510:"92ef6c",568:"af5ca5",598:"2d6aed",630:"061608",724:"e45d20",748:"9219bd",880:"9219bd",905:"c8e623"}[t]+".css"),a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),t={},e="Dhtbiz:",a.l=(n,r,i,o)=>{if(t[n])t[n].push(r);else{var c,u;if(void 0!==i)for(var s=document.getElementsByTagName("script"),l=0;l<s.length;l++){var d=s[l];if(d.getAttribute("src")==n||d.getAttribute("data-webpack")==e+i){c=d;break}}c||(u=!0,(c=document.createElement("script")).charset="utf-8",c.timeout=120,a.nc&&c.setAttribute("nonce",a.nc),c.setAttribute("data-webpack",e+i),c.src=n),t[n]=[r];var f=(e,r)=>{c.onerror=c.onload=null,clearTimeout(p);var i=t[n];if(delete t[n],c.parentNode&&c.parentNode.removeChild(c),i&&i.forEach((t=>t(r))),e)return e(r)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:c}),12e4);c.onerror=f.bind(null,c.onerror),c.onload=f.bind(null,c.onload),u&&document.head.appendChild(c)}},a.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{var t;a.g.importScripts&&(t=a.g.location+"");var e=a.g.document;if(!t&&e&&(e.currentScript&&(t=e.currentScript.src),!t)){var n=e.getElementsByTagName("script");n.length&&(t=n[n.length-1].src)}if(!t)throw new Error("Automatic publicPath is not supported in this browser");t=t.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),a.p=t})(),n=t=>new Promise(((e,n)=>{var r=a.miniCssF(t),i=a.p+r;if(((t,e)=>{for(var n=document.getElementsByTagName("link"),r=0;r<n.length;r++){var i=(a=n[r]).getAttribute("data-href")||a.getAttribute("href");if("stylesheet"===a.rel&&(i===t||i===e))return a}var o=document.getElementsByTagName("style");for(r=0;r<o.length;r++){var a;if((i=(a=o[r]).getAttribute("data-href"))===t||i===e)return a}})(r,i))return e();((t,e,n,r)=>{var i=document.createElement("link");i.rel="stylesheet",i.type="text/css",i.onerror=i.onload=o=>{if(i.onerror=i.onload=null,"load"===o.type)n();else{var a=o&&("load"===o.type?"missing":o.type),c=o&&o.target&&o.target.href||e,u=new Error("Loading CSS chunk "+t+" failed.\n("+c+")");u.code="CSS_CHUNK_LOAD_FAILED",u.type=a,u.request=c,i.parentNode.removeChild(i),r(u)}},i.href=e,document.head.appendChild(i)})(t,i,e,n)})),r={143:0},a.f.miniCss=(t,e)=>{r[t]?e.push(r[t]):0!==r[t]&&{43:1,79:1,82:1,93:1,150:1,164:1,167:1,182:1,187:1,242:1,269:1,401:1,452:1,464:1,510:1,568:1,598:1,630:1,724:1,748:1,880:1,905:1}[t]&&e.push(r[t]=n(t).then((()=>{r[t]=0}),(e=>{throw delete r[t],e})))},(()=>{var t={143:0};a.f.j=(e,n)=>{var r=a.o(t,e)?t[e]:void 0;if(0!==r)if(r)n.push(r[2]);else if(/^(452|748)$/.test(e))t[e]=0;else{var i=new Promise(((n,i)=>r=t[e]=[n,i]));n.push(r[2]=i);var o=a.p+a.u(e),c=new Error;a.l(o,(n=>{if(a.o(t,e)&&(0!==(r=t[e])&&(t[e]=void 0),r)){var i=n&&("load"===n.type?"missing":n.type),o=n&&n.target&&n.target.src;c.message="Loading chunk "+e+" failed.\n("+i+": "+o+")",c.name="ChunkLoadError",c.type=i,c.request=o,r[1](c)}}),"chunk-"+e,e)}};var e=(e,n)=>{var r,i,[o,c,u]=n,s=0;if(o.some((e=>0!==t[e]))){for(r in c)a.o(c,r)&&(a.m[r]=c[r]);if(u)u(a)}for(e&&e(n);s<o.length;s++)i=o[s],a.o(t,i)&&t[i]&&t[i][0](),t[i]=0},n=self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[];n.forEach(e.bind(null,0)),n.push=e.bind(null,n.push.bind(n))})();var c={};return(()=>{"use strict";a.d(c,{default:()=>Z});a(2305);var t=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"index"},[n("app-link",{staticClass:"xx",attrs:{to:"index",target:"_blank"}},[t._v("Home")]),n("app-link",{attrs:{to:"list"}},[t._v("List")]),n("div",{staticClass:"con"},[t._v("welcome to use the application template!")])],1)};t._withStripped=!0;const e={data:function(){return{}},components:{AppLink:a(4042).default},methods:{beforeRouteLeave:function(t,e,n){n()},beforeRouteUpdate:function(){}}};const n=(0,a(1900).Z)(e,t,[],!1,null,null,null).exports;var r=a(4942);var i=function(){return a.e(630).then(a.bind(a,9630))};i.beecraft=function(){return{name:"dht_web_product_detail_key_info",displayName:$t("dht.component.web.product_detail_key_info","商品参数信息"),data:{name:$t("dht.component.web.product_detail_key_info.title","参数信息"),showType:"1",selectedFields:[]},$$data:{isCanvas:!0},related:{previewDisplay:function(){return Promise.all([a.e(141),a.e(82)]).then(a.bind(a,7773))},attributeSettings:[{name:"SetterField",data:{setter:{component:function(){return Promise.all([a.e(141),a.e(401)]).then(a.bind(a,8401))}}}}]}}};const o=i;var u=a(4400);var s=function(){return a.e(140).then(a.bind(a,4140))};s.beecraft=function(){return{name:"dht_web_product_detail_all",related:{previewDisplay:function(){return Promise.all([a.e(141),a.e(773),a.e(905)]).then(a.bind(a,2064))},attributeSettings:[{name:"SetterField",data:{setter:{component:function(){return Promise.all([a.e(141),a.e(182)]).then(a.bind(a,8182))}}}}]}}};const l=s;var d=function(t){return new Promise((function(e){Fx.async(["app-standalone/components/widgets/newwidgets"],(function(n){e(n[t])}))}))};var f=function(){return a.e(724).then(a.bind(a,4724))};f.beecraft=function(){return{name:"dht_web_product_list_all",displayName:"mall list",related:{previewDisplay:function(){return d("dht_shopmall_allproduct").then((function(t){return t.displayComponent()})).then((function(t){return t}))},attributeSettings:[{name:"SetterField",data:{setter:{component:function(){return d("dht_shopmall_allproduct").then((function(t){return t.settingComponent()})).then((function(t){return t}))}}}}]}}};const p=f;var h=function(t){return new Promise((function(e){Fx.async(["app-standalone/components/widgets/newwidgets"],(function(n){e(n[t])}))}))};var m=a(885),b=a(5927);function y(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}var v=function(t,e){return new Promise((function(n,i){Promise.all([new Promise((function(t){window.$dht?t():(0,b.ii)().then((function(){t()}))})),new Promise((function(t){Fx.async(["vcrm/sdk"],(function(e){t(e)}))}))]).then((function(o){(0,m.Z)(o,2)[1].widgetService.getWidgetApp(t,function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?y(Object(n),!0).forEach((function(e){(0,r.Z)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},e)).then((function(t){n(t)})).catch(i)})).catch(i)}))},_=function(t){return v("productList",t)};_.beecraft=function(){return{name:"dht_product_list",displayName:"mail list component",related:{previewDisplay:function(){return h("dht_product_list").then((function(t){return t.displayComponent()})).then((function(t){return t}))},attributeSettings:[{name:"SetterField",data:{setter:{component:function(){return h("dht_product_list").then((function(t){return t.settingComponent()})).then((function(t){return t}))}}}}]}}};const w=_;var g=function(t){return new Promise((function(e){Fx.async(["app-standalone/components/widgets/newwidgets"],(function(n){e(n[t])}))}))};var O=function(){return a.e(79).then(a.bind(a,5079))};O.beecraft=function(){return{name:"dht_web_order_card",displayName:"my order",related:{previewDisplay:function(){return g("dht_order_card").then((function(t){return t.displayComponent()})).then((function(t){return t}))},attributeSettings:[{name:"SetterField",data:{setter:{component:function(){return g("dht_order_card").then((function(t){return t.settingComponent()})).then((function(t){return t}))}}}}]}}};const P=O;var S=function(){return a.e(164).then(a.bind(a,8164))};S.beecraft=function(){return{name:"dht_web_shopping_cart_all",displayName:"shopping cart",related:{previewDisplay:function(){return a.e(602).then(a.bind(a,602))},attributeSettings:[{name:"SetterField",data:{setter:{label:"test",display:"block"}}}]}}};const j=S;var k=function(){return a.e(242).then(a.bind(a,9242))};k.beecraft=function(){return{name:"dht_web_quick_order_all",displayName:"quick order list",related:{previewDisplay:function(){return a.e(927).then(a.bind(a,3927))},attributeSettings:[{name:"SetterField",data:{setter:{setter:{label:"test",display:"block"}}}}]}}};const D=k;function C(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function x(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?C(Object(n),!0).forEach((function(e){(0,r.Z)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):C(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var E=function(t){return new Promise((function(e){Fx.async(["vcrm/sdk"],(function(n){n.widgetService.getWidget(t).then((function(t){e(t)}))}))}))};const F={fxObjectInputAutocomplete:function(){return a.e(510).then(a.bind(a,9510))},DhtAiOrder:function(){return Promise.all([a.e(768),a.e(452)]).then(a.bind(a,1819))},cartQuantityInput:function(){return E("cartQuantityInput")},quantityInput:function(){return E("quantityInput")},orderQuickluQuantityInput:function(){return E("orderQuickluQuantityInput")},productList:function(){return E("productList")},sortSelect:function(){return E("sortSelect")},recordType:function(){return E("recordType")},cartRecordType:function(){return E("cartRecordType")},cartFooter:function(){return E("cartFooter")},shopCart:function(){return E("shopCart")},goodDetail:function(){return E("goodDetail")},spuDetail:function(){return E("spuDetail")},skuDetail:function(){return E("skuDetail")},productDetailMeta:function(){return E("productDetailMeta")},attachPreview:function(){return E("attachPreview")},splitScreen:function(){return E("splitScreen")},singleSelect:function(){return E("singleSelect")},selectConfirm:function(){return E("selectConfirm")},hotZoneEdit:function(){return E("hotZoneEdit")},dhtModules:function(){return E("dhtModules")},TheLink:function(){return Promise.resolve().then(a.bind(a,4042))},DhtProductDetail:function(t){return new Promise((function(e){Fx.async(["vcrm/sdk"],(function(n){n.widgetService.getWidgetApp("goodDetail",x({},t)).then((function(t){e(t)}))}))}))},dht_web_product_detail_key_info:o,dht_web_product_detail_card:u.Z,dht_web_product_detail_all:l,DhtCrmProductList:function(t){return new Promise((function(e){Fx.async(["crm-modules/page/shopmall/shopmall.js"],(function(n){var r=Vue.extend({name:"DhtCrmProductList",data:function(){return{pageView:null}},mounted:function(){this.pageView=new n(x({el:this.$el},t))},beforeDestroy:function(){this.pageView&&this.pageView.remove&&this.pageView.remove()},template:'<div class="dht-crm-product-list-wrapper"></div>'});e(r)}))}))},dht_shopmall_allproduct:p,dht_web_product_list_all:p,dht_web_shopping_cart_all:j,dht_web_quick_order_all:D,dht_product_list:w,dht_web_order_card:P};var A={},T={},N=function(t){return t.keys().map(t)};N(a(4328)).map((function(t){var e=t;e&&Object.keys(e).forEach((function(t){if("default"!==t){if(A[t])throw new Error("api ".concat(t," duplicate"));A[t]=e[t]}}))})),N(a(9691)).map((function(t){var e=t;e&&Object.keys(e).forEach((function(t){if("default"!==t){if(T[t])throw new Error("actions ".concat(t," duplicate"));T[t]=e[t]}}))}));const Z={isVue:!0,template:'<div class="dhtbiz" style="height: 100%;"></div>',setup:function(){},breforeEach:function(t,e,n){n()},afterEach:function(t,e){},routes:function(){return{"index(/=/*param)":{beforeEnter:function(t,e,n){n()},component:n},design:function(){return Promise.all([a.e(141),a.e(598)]).then(a.bind(a,6738))},list:function(){return a.e(93).then(a.bind(a,7890))},detail:function(){return a.e(167).then(a.bind(a,9714))},detail2:function(){return a.e(187).then(a.bind(a,3153))},test:function(){return a.e(43).then(a.bind(a,990))},ts:function(){return a.e(269).then(a.bind(a,9846))},orderlist:function(){return Promise.all([a.e(768),a.e(150)]).then(a.bind(a,4656))}}},destroy:function(){$(".dhtbiz").off(),$(".dhtbiz").remove()},api:A,actions:T,components:F}})(),c=c.default})()));