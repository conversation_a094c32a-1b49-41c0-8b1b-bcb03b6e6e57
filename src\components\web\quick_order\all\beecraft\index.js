const loadWidget = (widgetName) => {
  return new Promise((resolve) => {
      Fx.async(['app-standalone/components/widgets/newwidgets'], function(Wdts) {
        resolve(Wdts[widgetName])
      })
  })
}

// dht_product_list

export default function () {
  return {
    name: 'dht_web_quick_order_all',
    displayName: 'quick order list',  //快速下单列表
    related: {
      previewDisplay: () => import('./display.vue'),
      attributeSettings: [        
        {
          name: 'SetterField',
          data: {
            setter: {
              setter: {
                label: 'test',
                display: 'block',
              }
            }
          }
        }]
    }
  }
}