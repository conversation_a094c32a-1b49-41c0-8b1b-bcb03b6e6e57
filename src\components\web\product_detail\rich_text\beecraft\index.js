export default function () {
  return {
    name: 'dht_web_product_detail_rich_text',
    //   displayName: $t('商品参数信息'), 
    data: {
      name: 'rich text',
      showType: '1',
      selectedFields: []
    },
    $$data: {
      isCanvas: true
    },
    related: {
      previewDisplay: () => import('./display.vue'),
      attributeSettings: [
        {
          name: 'SetterField',
          data: {
            //   label: $t('paasbiz.portal-designer.max-content-set', '参数设置'),
            // label: '参数设置',
            // display: 'block',
            setter: {
              component: () => import('./setting.vue')
            }
          }
        }]
    }
  }
}