<template>
  <div class="dht-product-detail-operate-wrapper">
    <div class="add-cart-panel">
      <fx-button type="primary" size="small">add to cart</fx-button>
      <div class="icon-wrapper">
        <span class="icon fx-icon-collect">collect</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductOperate'
}
</script>

<style lang="less" scoped>
.dht-product-detail {
  &-operate-wrapper {
    border-top: 1px solid #F2F3F5;
    padding: 16px;
    padding-left: 0;

    .add-cart-panel {
      display: flex;
      align-items: center;

      .icon-wrapper {
        margin-left: 16px;
      }
    }
  }
}
</style> 