<template>
  <div class="top-shopping-cart-setting">
    todo not finish setting
    1. 修改名称和图标
    <fx-form>
      <fx-form-item label="显示数量">
        <fx-switch
          v-model="showCount"
          active-value="1"
          inactive-value="0"
          active-text="是"
          inactive-text="否">
        </fx-switch>
      </fx-form-item>
      <fx-form-item label="显示文字">
        <fx-switch
          v-model="showText"
          active-value="1"
          inactive-value="0"
          active-text="是"
          inactive-text="否">
        </fx-switch>
      </fx-form-item>
    </fx-form>
  </div>
</template>

<script>
export default {
  name: 'dht_web_top_shopping_cart_setting',
  props: {
    
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // showCount: this.value.showCount || '1',
      // showText: this.value.showText || '1'
    }
  },
  watch: {
    // showCount(val) {
    //   this.updateValue();
    // },
    // showText(val) {
    //   this.updateValue();
    // }
  },
  methods: {
    // updateValue() {
    //   this.$emit('input', {
    //     ...this.value,
    //     showCount: this.showCount,
    //     showText: this.showText
    //   });
    // }
  }
}
</script>

<style scoped>
.top-shopping-cart-setting {
}
</style>