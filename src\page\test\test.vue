<template>
  <div class="test-container">
    <!-- 展示面板 -->
    <div class="test-display">
      <displayComponent v-bind="flattenProps"  :props="componentProps"
      :api_name="compApiName" ></displayComponent>
    </div>

    <!-- 设置面板 -->
    <div class="test-setting">
      <settingComponent v-bind="$attrs" :props="componentProps"  :api_name="compApiName" ></settingComponent>
    </div>
  </div>
</template>

<script>
// 商品卡片
import testComp from '@components/web/product_detail/card/index.js';
const testProps = {
  img: '1',
  tag: '1',
  price: '1',
  attr: 0,
  stock: '1',
  fields: {},        
}

// testComp.attr = 
// 参数信息
// import testComp from '@components/web/product_detail/key_info/index.js';
// const testProps = {
//   name: '参数信息',
//   showType: '1',
//   fields: {},
//   selectedFields: [],
// }


// 商城列表
// app-standalone = app-standalone/components/widgets/newwidgets 
// import appStandaloneComponents from 'app-standalone';
// import appStandaloneComponents from 'app-standalone';
// const testComp = appStandaloneComponents.dht_shopmall_allproduct;
// const testProps = {
//     buttons: [],
//     card_main_info: {
//         tag_apiname: '',
//         picture_apiname: '',
//         show_fields: [],
//         name_apiname: '',
//         price_apiname: '',
//         is_tag_show: true
//     },
//     // 940新增
//     sortFields: [],
//     filterFields: [],
//     related_list_name: '',
//     view_mode: 'card',
//     nameI18nKey: 'dht.component.product_all',
//     type: 600,
//     is_card_init: false,
//     api_name: 'dht_shopmall_allproduct',
//     limit: 1,
//     unDeletable: true,
//     header: '全部商品',
//     is_spu_mode: false,
//     grayLimit: 1,
//     default_view: 'card'
// }


const {settingComponent, displayComponent} = testComp;

export default {
  components: {
    settingComponent,
    displayComponent
  },

  // 提供数据和方法给子组件
  provide() {
    return {
      // 提供修改props的方法
      setProps: this.setProps,
    }
  },
  computed: {
    flattenProps() {
      return {...this.componentProps};
    }
  },

  data() {
    return {
      compApiName: 'test_comp',
      // 组件配置数据
      // 后台下发的 externalProps
      componentProps: testProps
    }
  },

  methods: {
    // 更新props的方法
    setProps(apiName, callback) {
      // 调用回调函数修改数据
      callback(this.componentProps)
      // 触发更新
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.test-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
}
.test-setting {
  padding: 16px 12px;
  width: 400px;
  height: 100%;
  overflow-y: auto;
  background-color: #f6f7f9;
}
.test-display {
  max-width: 65%;
  flex: 1;
  height: 100%;
  background-color: #fff;
}
</style>
