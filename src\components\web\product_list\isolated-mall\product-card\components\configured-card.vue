<script lang="ts">
import 'reflect-metadata';
import { Component, Prop } from 'vue-property-decorator';
import { mixins } from 'vue-class-component';

import CardMixin from './mixins';
import { getImageByPath } from '../../utils/image-util';
import ProductTag from '../../product-tag/product-tag.vue';
import { ProductConfig, ProductFieldsMap, ProductKeys } from '../options';
import { Field } from '../../types/Field';
import { isJinbei } from '@/widgets/utils/check-ea';

const DEFAULT_VALUE = '--';
const fieldValueGetter = $dht.services.metaRender.fieldValueGetter;

@Component({
  name: 'ConfiguredCard',
  components: {
    ProductTag,
  }
})
export default class ConfiguredCard extends mixins(CardMixin) {
  @Prop({default: () => ({ card_main_info: {} })}) readonly productConfig!: ProductConfig;
  @Prop({default: () => ({})}) readonly productFields!: ProductFieldsMap;

  isJinbei = isJinbei();

  get commodityLabels() {
    let commodityOptions = this.product.commodityOptions || [];
    return commodityOptions.filter(
      (option) => option.value !== this.productConfig.card_main_info.tag_apiname
    );
  }

  get isDefaultImg() {
    return this.displayPicture?.lastIndexOf('sail.default.png') !== -1;
  }

  get displayTag() {
    let arr:any = [];
    let commodityOptions = this.product.commodityOptions || [];
    let tag_apiname:any = [];

    if (typeof(this.productConfig.card_main_info.tag_apiname) === 'string') {
      tag_apiname = [this.productConfig.card_main_info.tag_apiname];
    } else {
      tag_apiname = this.productConfig.card_main_info.tag_apiname;
    }
    commodityOptions.forEach((a) => {
      tag_apiname.forEach((b:any) => {
        if(b === a.value) {
            arr.push(a);
        }
      });
    });
    return arr;
  }

  get displayPicture() {
    const picture_apiname = this.productConfig.card_main_info.picture_apiname;
    const pictures = this.product[picture_apiname];
    const firstPicture = pictures && pictures.length ? pictures[0] : {};
    if (firstPicture.signedUrl) {
      return firstPicture.signedUrl + '&size=350*0';
    }
    return getImageByPath(firstPicture.path, '350*0', firstPicture.ext);
  }

  get displayName() {
    const nameApiName = this.productConfig.card_main_info.name_apiname;
    const languageStr = (this.product as any)[`${nameApiName}__r`];
    if (languageStr && typeof languageStr === 'string') {
        // 显示多语 name__r
        return languageStr;
    }
    return this.product[nameApiName] || DEFAULT_VALUE;
  }

  get displayPrice() {
    if (this.isHidePrice) return '***';

    const price_apiname = this.productConfig.card_main_info.price_apiname;

    return this.product[price_apiname] || DEFAULT_VALUE;
  }

  get showFields() {
    const { card_main_info } = this.productConfig;
    const { show_fields } = card_main_info;
    const showFields: Record<string, string>[] = [];
    show_fields?.forEach(item => {
      const field = this.productFields[item];
      if (field) {
        showFields.push({
          api_name: item,
          label: field.label,
          value: this.getFormatFieldValue(field, item),
        });
      }
    });

    return showFields;
  }

  /**
   * 获取格式化字段值
   * @param field 字段
   * @param api_name 字段api_name
   * @return string
   */
  getFormatFieldValue(field: Field, api_name: ProductKeys): string {
    const value = this.product[api_name];
    const defaultValue = this.productFields[api_name].default_value;
    const getter = fieldValueGetter[field.type];
    const formatValue = getter && getter(field, value || defaultValue, this.product, {});

    return _.isEmpty(formatValue) ? DEFAULT_VALUE : formatValue;
  }
}
</script>

<template src="./configured-card.html"></template>
<style src="../product-card.less" lang="less" scoped></style>
