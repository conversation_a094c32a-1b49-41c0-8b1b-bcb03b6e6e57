/**
 * @desc: 商城分类业务字段组件 插件-Web
 * @author: wangmh
 */
import Base from "plugin_base";

export default class Test extends Base {
  constructor(pluginService, pluginParam) {
    super(...arguments);
    this.pluginService = pluginService;
  }

  apply() {
    return [
      {
        event: 'list.render.before',
        functional: this._listRenderBefore.bind(this),
      },
      {
        event: 'list.render.after',
        functional: this._listRenderAfter.bind(this),
      },
    ];
  }

  // getHook() {
  //
  // }

  async _listRenderBefore(plugin, param) {
    console.log('_listRenderBefore', plugin, param);
    return {aa: '_listRenderBefore'}
  }

  async _listRenderAfter(plugin, actions) {
    console.log('_formRenderBefore')
    return {bb: '_formRenderBefore'};
  }

  destroy() {}
}
