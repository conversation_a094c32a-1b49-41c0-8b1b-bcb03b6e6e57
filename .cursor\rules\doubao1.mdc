---
description: 
globs: 
alwaysApply: false
---
使用 chrome-mcp-server 中的tools 自动完成以下操作: 
请在豆包网页（https://www.doubao.com/chat/）中，自动完成以下操作：

1. 导航到豆包网站并等待页面完全加载
   - 使用 chrome_navigate 打开豆包网站
   - 使用 chrome_get_web_content 检查页面是否加载完成，如果未完成则
   等待

2. 在输入框中输入问题并发送
   - 使用 chrome_fill_or_select 定位输入框 
   [data-testid="chat_input_input"]
   - 填入用户问题
   - 触发 [data-testid="chat_input_send_button"]按钮 提交问题
   - 或使用 chrome_keyboard 发送 Enter 键提交问题

3. 等待AI回复加载完成
   - 使用 chrome_get_web_content 定期检查页面，直到发现回复内容
   - 通过检查页面标题变化（从"新对话"到包含问题相关内容）判断回复是否
   完成

4. 提取AI回复内容
   - 首先尝试使用 chrome_get_web_content 配合 selector="
   [data-testid='message_text_content']" 获取回复
   - 如果上述方法失败，尝试使用 selector="[theme-mode='light'].
   flow-markdown-body" 获取回复
   - 最后尝试获取整个页面内容并解析   - 


注意事项：
- 每个步骤添加错误处理和重试机制
- 如果页面结构发生变化，尝试多种选择器定位元素
- 在等待过程中使用适当的时间间隔，避免过于频繁的请求


我的问题是：{用户输入的问题}