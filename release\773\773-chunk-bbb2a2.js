"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[773],{4773:(t,e,i)=>{i.r(e),i.d(e,{default:()=>S});var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dht-product-detail-card-display"},[i("div",{staticClass:"dht-product-detail dht-product-detail-content"},["1"==t.img?i(t.widgets.preview||"product-preview",t._b({tag:"component"},"component",t.$attrs,!1)):t._e(),i("div",{staticClass:"dht-product-detail-main"},t._l(t.compNames,(function(e){return i(t.widgets[e]||e,t._b({key:e,tag:"component",attrs:{tag:t.tag,price:t.price,attr:t.attr,stock:t.stock,objectContext:t.objectContext}},"component",t.$attrs,!1))})),1)],1)])};a._withStripped=!0;var r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dht-product-commodity-labels-warpper"},["1"===t.tag?t._l(t.commodityLabels,(function(e){return i("div",{key:e.value,staticClass:"dht-product-commodity-labels",style:{backgroundColor:e.font_color}},[t._v("\n      "+t._s(e.label)+"\n    ")])})):t._e(),i("div",{staticClass:"dht-product-detail-name"},[t._v(" "+t._s(t.$t("商品名称"))+" ")])],2)};r._withStripped=!0;const c={name:"ProductTitle",props:{tag:{type:Boolean,default:!1}},computed:{commodityLabels:function(){return[{value:"option1",label:$t("vcrm.recommended","推荐"),font_color:"#624027"},{value:"option1",label:$t("新品"),font_color:"#E0975E"},{value:"option3",label:$t("促销"),font_color:"#E0975E"}]}}};var s=i(1900);const n=(0,s.Z)(c,r,[],!1,null,"436fc506",null).exports;var d=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dht-product-detail-price-wrapper"},[i("div",{staticClass:"dht-product-detail-main-item"},[i("div",{staticClass:"dht-product-detail-main-label"},[t._v(t._s(t.$t("价格")))]),i("div",{staticClass:"dht-product-detail-main-content dht-price-content-wrapper"},[i("div",{staticClass:"price-flag"},[t._v("￥")]),t._m(0),t.cPrice?t._e():i("div",{staticClass:"dht-between-line"},[t._v("~")]),t.cPrice?t._e():i("div",{staticClass:"price-content"},[i("div",[t._v("139")]),i("div",{staticClass:"dht-price-decimal-point"},[t._v(".90")])]),i("div",{staticClass:"dht-price-normal-wrap"},[t.cPrice?i("div",{staticClass:"dht-price-normal"},[t._v("￥49.90")]):i("div",{staticClass:"dht-price-normal"},[t._v("¥22.50~45.00")])])])])])};d._withStripped=!0;const o={name:"ProductPrice",props:{price:{type:String,default:"1"}},computed:{cPrice:function(){return"1"===this.price}}};const l=(0,s.Z)(o,d,[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"price-content"},[i("div",[t._v("39")]),i("div",{staticClass:"dht-price-decimal-point"},[t._v(".90")])])}],!1,null,"0325284e",null).exports;var p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dht-product-detail-policy-wrapper"},[i("div",{staticClass:"dht-product-detail-main-item"},[i("div",{staticClass:"dht-product-detail-main-label"},[t._v(t._s(t.$t("促销")))]),i("div",{staticClass:"dht-product-detail-main-content dht-policy-content"},[i("div",{staticClass:"dht-policy-name"},[t._v("\n        "+t._s(t.$t("dht.component.web.product_detail_card.promotion.name","价格政策名称"))+"\n      ")]),i("div",{staticClass:"dht-policy-item-line"}),i("span",{staticClass:"dht-policy-view"},[t._v("\n        "+t._s(t.$t("查看"))),i("i",{staticClass:"fx-icon-arrow-down"})])])])])};p._withStripped=!0;const u={name:"ProductPromotion"};const v=(0,s.Z)(u,p,[],!1,null,"ee3e708e",null).exports;var m=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"dht-product-detail-attr-wrapper"},[i("div",{staticClass:"dht-product-detail-main-item"},[i("div",{staticClass:"dht-product-detail-main-label"},[t._v(t._s(t.$t("规格")))]),i("div",{staticClass:"dht-product-detail-main-content"},[i("fx-button",{attrs:{type:"primary",size:"mini",plain:"plain"}},[t._v(t._s(t.$t("示例"))+"1")]),i("fx-button",{attrs:{type:t.attr2Checked,size:"mini",plain:"plain"}},[t._v(t._s(t.$t("示例"))+"2")]),i("fx-button",{attrs:{type:"default",size:"mini",plain:"plain"}},[t._v(t._s(t.$t("示例"))+"3")])],1)])]),"0"!==t.attr?i("div",{staticClass:"dht-product-detail-attr-rows-wrapper"},[i("div",{staticClass:"dht-product-detail-main-item dht-detail-rows"},t._l(2,(function(e){return i("div",{key:"dht_row_"+e,staticClass:"dht-row"},[t._l(4,(function(e){return i("div",{key:"dht_cell_"+e,staticClass:"dht-cell-middle-line"},[t._v(" ")])})),i("fx-input-number",{attrs:{min:1,max:100,size:"micro",step:1,value:1}})],2)})),0)]):t._e()])};m._withStripped=!0;const _={name:"ProductSpec",props:{attr:{type:String,default:"0"}},computed:{attr2Checked:function(){return"2"===this.attr?"primary":"default"}}};const h=(0,s.Z)(_,m,[],!1,null,"2558941b",null).exports;var f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return"0"===t.attr?i("div",{staticClass:"dht-product-detail-input-wrapper"},[i("div",{staticClass:"dht-product-detail-main-item"},[i("div",{staticClass:"dht-product-detail-main-label"},[t._v(t._s(t.$t("vcrm.number\t","数量")))]),i("div",{staticClass:"dht-product-detail-main-content sku-input-content"},[i("fx-input-number",{attrs:{min:1,max:100,size:"small",step:1,value:1}}),i("span",{staticStyle:{"margin-left":"5px"}},[t._v(t._s(t.$t("dht.component.web.product_detail_card.quantity.unit","只")))]),"1"===t.stock?i("div",{staticClass:"stock-info"},[t._v(t._s(t.$t("dht.component.web.product_detail_card.stock","库存"))+": "+t._s(t.$t("充足")))]):t._e()],1)])]):t._e()};f._withStripped=!0;const C={name:"ProductQuantity",props:{attr:{type:String,default:"0"},stock:{type:String,default:"1"}}};const w=(0,s.Z)(C,f,[],!1,null,"55401641",null).exports;var b=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dht-product-detail-operate-wrapper"},[i("div",{staticClass:"add-cart-panel"},[i("fx-button",{attrs:{type:"primary",size:"small"}},[t._v(t._s(t.$t("加入购物车")))]),i("div",{staticClass:"icon-wrapper"},[i("span",{staticClass:"icon fx-icon-collect"},[t._v(t._s(t.$t("收藏")))])])],1)])};b._withStripped=!0;const y={name:"ProductOperate"};const $=(0,s.Z)(y,b,[],!1,null,"477ca3a3",null).exports;var g=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)};g._withStripped=!0;const P={name:"ProductPreview",props:{}};const x={name:"dht_web_product_detail_card",components:{ProductPrice:l,ProductPromotion:v,ProductSpec:h,ProductQuantity:w,ProductTitle:n,ProductOperate:$,ProductPreview:(0,s.Z)(P,g,[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"dht-product-detail-product-preview"},[e("div",{staticClass:"dht-product-preview"},[e("div",{staticClass:"dht-product-preview-poster one-picture"},[e("img",{attrs:{src:"https://a9.fspage.com/FSR/weex/avatar/object_list/images/list_default_icon.png"}})])])])}],!1,null,"3bfd9f54",null).exports},props:{widgets:{type:Object,default:function(){return{}}},img:{type:String,default:""},tag:{type:String,default:""},price:{type:String,default:""},attr:{type:String,default:"0"},stock:{type:String,default:""}},computed:{compNames:function(){return["product-title","product-price","product-promotion","product-spec","product-quantity","product-operate"]}},watch:{},data:function(){return{}},methods:{init:function(){}},mounted:function(){this.init()}};const S=(0,s.Z)(x,a,[],!1,null,"70856cbb",null).exports}}]);