// @vue/component
import FilterMixin from '../filter-mixin.js';

export default {
  name: 'LeftFilter',

  components: {},

  mixins: [FilterMixin],

  data () {
    return {
      // selectedValues 从 FilterMixin 继承
    }
  },

  computed: {},

  watch: {
    // 监听 filter_fields 变化，初始化展开状态
    filter_fields: {
      handler(newFields) {
        this.initExpandedSections(newFields);
        // initSelectedValues 在 FilterMixin 中处理
      },
      immediate: true
    }
  },

  created () {
    console.log('left-filter created, filter_fields:', this.filter_fields);
    this.initExpandedSections(this.filter_fields);
    // initSelectedValues 在 FilterMixin 中处理
  },

  methods: {

    /**
     * 初始化展开状态，默认第一个筛选项展开
     */
    initExpandedSections(fields) {
      const expandedSections = {};
      fields.forEach((field, index) => {
        expandedSections[field] = index === 0; // 默认第一个展开
      });
      this.expandedSections = expandedSections;
    },

    // 以下方法从 FilterMixin 继承：
    // - getFieldLabel
    // - getFieldType
    // - getFieldOptions
    // - onFilterChange
    // - getFilterValues
    // - resetFilters
    // - setFilterValues

  }
}