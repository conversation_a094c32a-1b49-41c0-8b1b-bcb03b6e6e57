const CRMUtil = CRM.util;

export class DhtBizModel {
    constructor() {
        this.config = null; // 订货通config 和 售中CrmConfig的合集
        this.objFields = {};
        this.configPromise = null;
        this.objFieldsPromise = null;
    }
    getObjFields() {
        return this.objFields;
    }
    setObjFields(fields) {
        this.objFields = fields;
    }
    init() {
        this.config = null;
        this.configPromise = null;
        this.getDhtConfig();
    }
    detailInit() {
        this.objFields = {};
        this.objFieldsPromise = null;
        this.fetchObjFields();
    }
    // 获取对象字段
    async fetchObjFields(objectApiName) {
        if (!this.objFieldsPromise) {
            this.objFieldsPromise = new Promise((resolve, reject) => {
                CRMUtil.FHHApi({
                    url: '/EM1HDHT/API/v1/object/dht_describe/service/get_simple_describe',
                    data: {
                        objectApiName,
                    },
                    success(res) {
                        const fields =  JSON.parse(JSON.stringify(res.Value.objectDescribe.fields));
                        this.objFields = fields;
                        resolve(fields);
                    }
                })
            });
        }
        return this.objFieldsPromise;
    }

    async getDhtConfig() {
        if (this.config) {
            this.configPromise = Promise.resolve(this.config);
        } else {
            const keys = [
                'dht_product_list_mode',
                'dht_order_list_display_picture',
                'dht_minimum_order_quantity',
                'dht_order_supports_select_price_list',
                'is_open_dht_product_list_tab',
                'dht_product_list_tab_data',
                'multi_unit_show_type',
                'multi_unit_linkage',
                'multiple_unit',
                'category_model_type',
                'shop_category_model_type',
                'enable_shop_category_model_type',
                'has_first_category_picture',
                'shopping_mall_mode',
                'spu',
            ];

            // const CRMKeys = ['shopping_mall_mode', 'spu'];
            // this.openPriceList = CRM._cache.openPriceList
            this.configPromise = Promise.all([
                this.getConfigValues(keys),
                // 不用调用crm接口, 后台已合并到getConfigValues中
                // this.getCRMConfigValues(CRMKeys)
            ]).then((res) => {
                const data = _.extend({}, res[0]);
                this.config = data;
                return data;
            });
        }

        return this.configPromise;
    }

    // 是否spu模式
    isSpuMode() {
        return CRM._cache.productOpenSpu;
    }

    mainObjApiName() {
        return this.isSpuMode() ? 'SPUObj' : 'ProductObj';
    }

    /**
     * 获取多单位设置单位列表
     */
    getUnitTypeConfig() {
        return new Promise((resolve, reject) => {
            CRMUtil.FHHApi({
                url: '/EM1HNCRM/API/v1/object/lazyLoadOptions/service/getUnitTypeConfig',
                data: {},
                success: (res) => {
                    if (res.Result.StatusCode === 0 && res.Value) {
                        resolve(res.Value.unitTypeList);
                        return;
                    }
                    CRMUtil.error(res.Result.FailureMessage);
                    reject(res);
                },
            });
        });
    }

    getConfigValues(keys) {
        // 配置key的默认数据（没有设置过的key，后台不会返回该数据，需要前端兼容默认逻辑）
        let configKeyDefalutVal = {
            online_pay_switch: '2', // '1'：开启，'2'：关闭
            offline_pay_switch: '2', // '1'：开启，'2'：关闭
            multi_spec_order_type: '1', // '2'：多规格可同时加入，'1'：单规格加入
            dht_shopping_cart_custom_field_config: '',
        };
        return new Promise((resolve, reject) => {
            CRMUtil.FHHApi({
                url: '/EM1HDHT/API/v1/object/dht_config/service/get_config_values',
                data: {
                    isAllConfig: false,
                    keys: keys,
                },
                success: (res) => {
                    if (res.Result.StatusCode === 0) {
                        let map = {}; // 接口返回的数据整理成map
                        let kesMap = {}; // 请求参数的map
                        let list = res.Value.values;
                        _.each(list, (a) => {
                            map[a.key] = a.value;
                        });
                        _.each(keys, (a) => {
                            kesMap[a] = _.has(map, a) ? map[a] : configKeyDefalutVal[a];
                        });

                        resolve(kesMap);
                        return;
                    }
                    CRMUtil.error(res.Result.FailureMessage);
                    reject(res);
                },
            });
        });
    }
    setConfigValues(data) {
        return new Promise((resolve, reject) => {
            CRMUtil.FHHApi({
                url: '/EM1HDHT/API/v1/object/dht_config/service/set_config_values',
                data: {
                    ConfigInfoList: data,
                },
                success: (res) => {
                    if (res.Result.StatusCode === 0) {
                        resolve(res.Value);
                        return;
                    }
                    CRMUtil.error(res.Result.FailureMessage || $t('设置失败请稍后再试'));
                    reject(res);
                },
            });
        });
    }
    // 原则上不再使用, 合并入getConfigValues中
    getCRMConfigValues(keys) {
        // 配置key的默认数据（没有设置过的key，后台不会返回该数据，需要前端兼容默认逻辑）
        let configKeyDefalutVal = {};
        return new Promise((resolve, reject) => {
            CRMUtil.FHHApi({
                url: '/EM1HNCRM/API/v1/object/biz_config/service/get_config_values',
                data: {
                    isAllConfig: false,
                    keys: keys,
                },
                success: (res) => {
                    if (res.Result.StatusCode === 0) {
                        let map = {}; // 接口返回的数据整理成map
                        let kesMap = {}; // 请求参数的map
                        let list = res.Value.values;
                        _.each(list, (a) => {
                            map[a.key] = a.value;
                        });
                        _.each(keys, (a) => {
                            kesMap[a] = _.has(map, a) ? map[a] : configKeyDefalutVal[a];
                        });

                        resolve(kesMap);
                        return;
                    }
                    CRMUtil.error(res.Result.FailureMessage);
                    reject(res);
                },
            });
        });
    }
    setCRMConfigValues(data) {
        return new Promise((resolve, reject) => {
            CRMUtil.FHHApi({
                url: '/EM1HNCRM/API/v1/object/biz_config/service/set_config_value',
                data: data,
                success: (res) => {
                    if (res.Result.StatusCode === 0) {
                        resolve(res.Value);
                        return;
                    }
                    CRMUtil.error(res.Result.FailureMessage || $t('设置失败请稍后再试'));
                    reject(res);
                },
            });
        });
    }
}

export const dhtBizModel = new DhtBizModel();

// export default {
//   Api,
//   dhtBizModel,
// }
