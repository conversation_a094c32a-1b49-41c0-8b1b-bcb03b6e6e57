<template>
  <div class="top-shopping-cart-preview">
    <div class="cart-icon">
      <i class="el-icon-shopping-cart-2"></i>
      <span class="cart-count" v-if="showCount === '1'">{{ count }}</span>
    </div>
    <div class="cart-text" v-if="showText === '1'">购物车</div>
  </div>
</template>

<script>
export default {
  name: 'dht_web_top_shopping_cart_display',
  props: {
    showCount: {
      type: String,
      default: '1'
    },
    showText: {
      type: String,
      default: '1'
    },
    count: {
      type: Number,
      default: 0
    }
  }
}
</script>

<style scoped>
.top-shopping-cart-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}
.cart-icon {
  position: relative;
  font-size: 24px;
}
.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #f56c6c;
  color: white;
  border-radius: 50%;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  font-size: 12px;
  padding: 0 4px;
}
.cart-text {
  margin-left: 5px;
  font-size: 14px;
}
</style>