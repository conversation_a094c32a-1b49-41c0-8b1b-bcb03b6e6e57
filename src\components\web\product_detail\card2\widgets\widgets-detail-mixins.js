export default {
  props: {
    objectContext: {
      type: Object,
      default: () => ({
        data: {},
        describe: {
          fields: {
          }
        }
      })
    }
  },
  computed: {
    isSpuMode() {
      return this.objectContext.pageData.isSpuMode;
    },
    product() {
      let { skus, spuData } = this.objectContext.pageData;
      let product = this.isSpuMode ? spuData : skus[0];
      if(!product) {
        product = this.objectContext.data;
      }
      return product;
    },    
  },
  methods: {
    getCommodityLabels(objectContext) {
      return objectContext.describe.fields.commodityLabels;
    }
  }
}