<script lang="ts">

import { getVcrmService } from '@/share/utils/load';


import 'reflect-metadata';
import { Component, Prop, Vue, Emit, Ref } from 'vue-property-decorator';
import {Product, Unit} from '../types';
import { getImageByPath } from '../utils/image-util';
import InputNumber from '../input-number/input-number.vue';
import UnitSelector from '../unit-selector/unit-selector.vue';
import ProductTag from '../product-tag/product-tag.vue';
import { Options, ActionType, ProductConfig, ProductFieldsMap } from './options';
import { isSpuObj, getProductMinNum } from '../utils/product-util';

const collectionService = getVcrmService('collectionService');
// import { collectionService } from '../services/CollectionService';
import BaseCard from './components/base-card.vue';
import ConfiguredCard from './components/configured-card.vue';

@Component({
  name: 'ProductCard',
  components: {
    ProductTag,
    InputNumber,
    UnitSelector,
    BaseCard,
    ConfiguredCard,
  }
})
export default class ProductCard extends Vue {
  @Ref() readonly fkInput!: InputNumber;

  @Prop({default: () => ({})}) readonly product!: Product;
  @Prop({default: () => ({})}) readonly options!: Options;
  @Prop({default: 2}) readonly precision!: number;
  @Prop({default: () => ({ card_main_info: {} })}) readonly productConfig!: ProductConfig;
  @Prop({default: false}) readonly isConfigured!: boolean;
  @Prop({default: () => ({})}) readonly productFields!: ProductFieldsMap;

  isShow = false;
  isShopMallMode = $dht.config.sail.isShopMallMode;
  isOpenMiniNum = $dht.config.sail.isOpenMiniNum;

  get pictureUrl(): string {
    const picture = isSpuObj(this.product) ? this.product.picture : this.product.picture_path;
    const firstPicture = picture && picture.length ? picture[0] : {};
    return getImageByPath(firstPicture.path, '350*350', firstPicture.ext);
  }

  get isDefaultImg() {
    return this.pictureUrl.lastIndexOf('sail.default.png') !== -1;
  }

  get commodityLabels() {
    let commodityOptions = this.product.commodityOptions || [];
    return commodityOptions.filter((option) => option.value !== 'option1');
  }

  get isNew() {
    return (this.product.commodityOptions || []).findIndex(option => option.value === 'option1') !== -1;
  }

  get priceUnitName() {
    return this.product.unit__r;
  }

  get stockText() {
    if (this.product.virtual_available_stock) {
      return `${$t('库存')}：${this.product.virtual_available_stock}`;
    }
    return `${$t('库存')}： --`;
  }

  get isShowPriceUnit() {
    return this.product.is_multiple_unit && !this.product.is_common_unit;
  }

  get displayPrice() {
    const priceBookPrice = this.product.virtual_price_book_price;
    return priceBookPrice != null ? priceBookPrice : this.product.price;
  }

  get productMinNum() {
   return getProductMinNum(this.product);
  }
  
  created () {
    // collectionService.updateProductCollectionStatus(this.product);
  }

  @Emit('on-action')
  onAction(type: ActionType, product: Product) {
    if (type === 'CART') {
      return { type,  product, position: this.fkInput.$el.getBoundingClientRect() };
    }
    return { type,  product };
  }

  @Emit('on-show')
  showElem(show: boolean) {
    this.isShow = show;
  }
}
</script>

<template src="./product-card.html"></template>
<style src="./product-card.less" lang="less" scoped></style>
