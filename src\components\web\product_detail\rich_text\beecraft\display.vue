<template>
  <div class="dht-product-detail-rich-text" >
    <div class="dht-title">{{ $t('图文详情') }}</div>
    <div class="dht-content">
      {{ $t('dht.component.web.product_detail_rich_text.description', '富文本字段description中的图片内容') }}      
    </div>
  </div>
</template>
<script>
export default {
  name: 'dht_web_product_detail_rich_text',
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      imgHtml: ''
    }
  },
  computed: {
    isSpuMode() {
      // return this.data.isSpuMode;
    },
  },
  methods: {
   
  },
  mounted() {
    // 获取图文详情
    // let des = this.product.description;
    // this.imgHtml = this.convertImgHtml(des);
  }
}

</script>
<style lang="less" scoped>
.dht-product-detail-rich-text {
  padding: 16px;

.dht-title {
  font-size: 14px;
  color: #181c25;
  margin-bottom: 16px;
}

.dht-content {  
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 100%; 
}
    
}
</style>