/**
 * 获取vcrm组件和服务
 * @param services
 * @param widgets
 */
export function getVcrmComponents(services: string[], widgets: string[]): Promise<any> {
  return new Promise((resolve, reject) => {
    Fx.async(['vcrm/sdk'], (vcrmSdk: any) => {
      const promiseList: Promise<any>[] = [];
      if (services) {
        services.forEach((item: string) => {
          promiseList.push(vcrmSdk.widgetService.getService(item));
        });
      }
      if (widgets) {
        widgets.forEach((item: string) => {
          promiseList.push(vcrmSdk.widgetService.getWidget(item));
        });
      }
      Promise.all(promiseList).then((result: any[]) => {
        resolve(result);
      }).catch(reject);
    });
  });
}
