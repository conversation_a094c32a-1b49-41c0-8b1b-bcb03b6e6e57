//
// jsconfig.json源于tsconfig.json，是TypeScript的配置文件
// 文件所在目录是javascript 项目的根目录
//
{
    "compilerOptions": {
        "target": "es5",
        "module": "esnext",
        "baseUrl": "./",
        "moduleResolution": "node",
        "jsx": "preserve",
        "paths": {
            "@/*": ["src/*"],
            "@img/*": ["src/assets/img/*"],
            "@css/*": ["src/assets/css/*"],
            "@assets/*": ["src/assets/*"],
            "@components/*": ["src/components/*"],
            "@api/*": ["src/api/*"],
            "@utils/*": ["src/utils/*"],
            "@config/*": ["src/config/*"],
            "@page/*": ["src/page/*"]
        },
        "lib": ["esnext", "dom", "dom.iterable", "scripthost"]
    },
    "include": ["src/**/*"]
}
