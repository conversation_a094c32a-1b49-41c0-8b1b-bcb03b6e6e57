<template>
  <div class="custom-price">
    <div class="dht-product-detail-main-item">
      <div class="dht-product-detail-main-label">自定义价格</div>
      <div class="price">
        <span class="amount">¥99.99</span>
        <span class="original">¥199.99</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomPrice'
}
</script>

<style scoped>
.custom-price {
  padding: 16px;
  background: #f5f5f5;
}
.price {
  display: flex;
  align-items: center;
  gap: 8px;
}
.amount {
  color: #f00;
  font-size: 20px;
  font-weight: bold;
}
.original {
  color: #999;
  text-decoration: line-through;
}
</style> 