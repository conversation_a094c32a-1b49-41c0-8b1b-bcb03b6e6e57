const ERUpstreamEa = $.cookie('ERUpstreamEa');

export function isJingYi() { // 警翼
  return ['pwitne001','804322_sandbox', '808615_sandbox', '84148'].includes(ERUpstreamEa);
}

export function isZd() {
  return ['744767_sandbox', 'zdylyp'].includes(ERUpstreamEa);
}

export function isJinbei() {
    return ['78437', 'fktest045', 'jbdg168'].includes(ERUpstreamEa);
}

export function isSiGe() {
    return ['67000037_sandbox', '78437', 'sigexinnengyuan'].includes(ERUpstreamEa);
}

export function isEast() {
    return ['777375_sandbox', 'east2020'].includes(ERUpstreamEa);
}

export function isEastProduct(name: string) {
    const upsList = [
        'EA890',
        'EA850HD',
        'EA890HD',
        'EA90',
        'EA99',
        'EA66-25K',
        'EA66-50K',
        'EA66-100K',
        'EA80',
        'EA600',
        'EA660-25K模块机', //l-18nIgnore
        'EA660-50K模块机', //l-18nIgnore
        'EA660-100K模块机', //l-18nIgnore
        'EA990高频塔机', //l-18nIgnore
        'EA900高频塔机', //l-18nIgnore
        'EA890工频机', //l-18nIgnore
        'EA890HD工频机', //l-18nIgnore
        'EA850HD工频机', //l-18nIgnore
        'EA800工频机', //l-18nIgnore
        'EA600后备机', //l-18nIgnore
        'EA200-EA600后备机', //l-18nIgnore
        'EA200后备机', //l-18nIgnore
        'EA300后备机', //l-18nIgnore
    ];
    const mcList = ['MC2000', '集装箱骨架车 888 型'] //l-18nIgnore
    const isTargetProduct = mcList.includes(name) || upsList.includes(name);
    return isTargetProduct && isEast();
}

export function isMnchip() {
   return ['mnchip2019', '83050'].includes($.cookie('ERUpstreamEa'));
}
