<template>
  <div class="filter-example">
    <h3>左侧筛选组件示例</h3>
    
    <!-- 筛选组件 -->
    <left-filter
      :filter_fields="filterFields"
      :objectFields="objectFields"
      @filter-change="onFilterChange"
      ref="leftFilter"
    ></left-filter>
    
    <!-- 操作按钮 -->
    <div class="actions">
      <fx-button @click="resetFilters">重置筛选</fx-button>
      <fx-button type="primary" @click="getFilterValues">获取筛选值</fx-button>
    </div>
    
    <!-- 显示当前筛选值 -->
    <div class="current-filters">
      <h4>当前筛选值：</h4>
      <pre>{{ JSON.stringify(currentFilters, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import LeftFilter from './left-filter.vue'

export default {
  name: 'FilterExample',
  
  components: {
    LeftFilter
  },
  
  data() {
    return {
      // 需要筛选的字段列表
      filterFields: ['surface_treatment', 'memory', 'storage', 'keyword_search'],
      
      // 字段配置对象
      objectFields: {
        surface_treatment: {
          label: '表面处理',
          type: 'select_one',
          options: [
            { label: '表面处理方式一', value: '1' },
            { label: '表面处理方式二', value: '2' },
            { label: '表面处理方式三', value: '3' },
            { label: '表面处理方式四', value: '4' },
            { label: '表面处理方式五', value: '5' },
            { label: '表面处理方式六', value: '6' },
            { label: '表面处理方式七', value: '7' }
          ]
        },
        memory: {
          label: '内存',
          type: 'select_many',
          options: [
            { label: '128G', value: '128' },
            { label: '256G', value: '256' },
            { label: '512G', value: '512' },
            { label: '1T', value: '1024' }
          ]
        },
        storage: {
          label: '存储',
          type: 'select_many',
          options: [
            { label: '128G', value: '128' },
            { label: '256G', value: '256' },
            { label: '512G', value: '512' },
            { label: '1T', value: '1024' }
          ]
        },
        keyword_search: {
          label: '字段筛选',
          type: 'input'
        }
      },
      
      // 当前筛选值
      currentFilters: {}
    }
  },
  
  methods: {
    /**
     * 处理筛选变化
     */
    onFilterChange(filterData) {
      console.log('筛选变化:', filterData);
      this.currentFilters = filterData.allFilters;
    },
    
    /**
     * 重置筛选
     */
    resetFilters() {
      this.$refs.leftFilter.resetFilters();
    },
    
    /**
     * 获取当前筛选值
     */
    getFilterValues() {
      const values = this.$refs.leftFilter.getFilterValues();
      console.log('当前筛选值:', values);
      this.$message.success('筛选值已输出到控制台');
    }
  }
}
</script>

<style lang="less" scoped>
.filter-example {
  max-width: 400px;
  margin: 20px;
  
  h3 {
    margin-bottom: 20px;
    color: #333;
  }
  
  .actions {
    margin: 20px 0;
    display: flex;
    gap: 10px;
  }
  
  .current-filters {
    margin-top: 20px;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 10px 0;
      color: #333;
    }
    
    pre {
      margin: 0;
      font-size: 12px;
      color: #666;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}
</style>