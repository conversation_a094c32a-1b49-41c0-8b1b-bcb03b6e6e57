"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[43],{990:(t,e,n)=>{n.r(e),n.d(e,{default:()=>a});var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"test-container"},[n("div",{staticClass:"test-display"},[n("displayComponent",t._b({attrs:{props:t.componentProps,api_name:t.compApiName}},"displayComponent",t.flattenProps,!1))],1),n("div",{staticClass:"test-setting"},[n("settingComponent",t._b({attrs:{props:t.componentProps,api_name:t.compApiName}},"settingComponent",t.$attrs,!1))],1)])};o._withStripped=!0;var r=n(4942),s=n(4400);function p(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}var i={img:"1",tag:"1",price:"1",attr:0,stock:"1",fields:{}};const c={components:{settingComponent:s.Z.settingComponent,displayComponent:s.Z.displayComponent},provide:function(){return{setProps:this.setProps}},computed:{flattenProps:function(){return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?p(Object(n),!0).forEach((function(e){(0,r.Z)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},this.componentProps)}},data:function(){return{compApiName:"test_comp",componentProps:i}},methods:{setProps:function(t,e){e(this.componentProps),this.$forceUpdate()}}};const a=(0,n(1900).Z)(c,o,[],!1,null,"5d5e6d49",null).exports}}]);