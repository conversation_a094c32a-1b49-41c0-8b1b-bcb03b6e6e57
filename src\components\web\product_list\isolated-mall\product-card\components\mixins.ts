import { Vue, Component, Prop, Emit } from 'vue-property-decorator';
import { Options, ActionType } from '../options';
import { Product } from '../../types';
import { getDefaultCurrencyFlag } from '@/widgets/utils/product-util';

@Component
export default class CardMixin extends Vue {
  @Prop({default: () => ({})}) readonly product!: Product;
  @Prop({default: () => ({})}) readonly options!: Options;

  defaultCurrencyFlag = getDefaultCurrencyFlag();

  get isHidePrice() {
    return $dht.config.sail.isHidePrice;
  }

  get isShowPriceUnit() {
    // return this.product.is_multiple_unit && !this.product.is_common_unit;
    return true;
  }

  get priceUnitName() {
    return this.product.unit__r;
  }

  @Emit('on-action')
  onAction(type: ActionType, product: Product) {}
}
