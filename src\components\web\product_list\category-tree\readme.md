## 分类树组件
运行态对应 E:\work\FS\vcrm\src\widgets\isolated-mall\category-tree\category-tree.vue 

你可以参考 @f:\work\FS\fs-sail\dhtbiz\src\components\web\product_detail\key_info\beecraft中的文件来实现。

### 显示态
设计图为：![image](./img/商品分类预览.png)，
请在 @f:\work\FS\fs-sail\dhtbiz/src\components\web\product_list\category-tree\beecraft\display.vue 中实现它， 有一个参数控制图片是否实现与隐藏，有一个参数控制 二级分类整行是否显示。没有边框。
大小字体颜色等跟设计稿保持一致。


### 设置态
设计图为：![image](./img/商品分类设置.png)，
请在 @f:\work\FS\fs-sail\dhtbiz/src\components\web\product_list\category-tree\beecraft\setting.vue 中实现它，勾选 展示图片时，display.vue中的图片显示，否则隐藏，勾选“仅显示一级分类”时，display.vue中隐藏二级分类整行。
数据来源通过接口获取。 

props: {
  source: {
    type: String,
    default: ''
  },
  level1ImgShow: {
    type: String,
    default: '1'
  },
  showAll: {
    type: String,
    default: '1'
  }
}
