//
// 生产tpl_config文件
const path = require('path');
const fs = require('fs');

class ExtractEntryWebPackPlugin {
    constructor(options) {
        this.options = Object.assign(
            {
                key: '',
                filename: 'tpl_config',
                saveNoHashFile: true,
            },
            options
        );
    }

    parseFile(file, compiler) {
        let output = compiler.options.output;
        let filename = output.filename;
        let filematch = new RegExp(filename.replace(/(\[.*?\])/g, '(.*)'));
        let match = file.match(filematch);
        if (match) {
            return {
                name: match[1],
                hash: match[2],
            };
        }
        return null;
    }

    extractAssets(content, compiler) {
        let outputDir = compiler.options.output.path;
        let filename = this.options.filename;
        let outPath = path.join(outputDir, filename);
        fs.writeFileSync(outPath, content, 'utf8');
    }

    extractNoHashFile(hashFileName, noHashFileName, compiler) {
        let outputDir = compiler.options.output.path;
        let readStream = fs.createReadStream(`${outputDir}/${hashFileName}`);
        let writeStream = fs.createWriteStream(`${outputDir}/${noHashFileName}`);
        readStream.pipe(writeStream);
    }

    saveNoHashFile(fileName, file, compiler) {
        if (this.options.saveNoHashFile) {
            let noHashFileName = this.options.noHashFileName || fileName.toLowerCase();
            if (typeof noHashFileName !== 'string') {
                noHashFileName = noHashFileName(fileName, file);
            }
            noHashFileName = path.join(path.dirname(file), `${noHashFileName}${path.extname(file)}`);
            this.extractNoHashFile(file, noHashFileName, compiler);
        }
    }

    apply(compiler) {
        const pluginName = this.constructor.name;

        compiler.hooks.afterEmit.tap(pluginName, (compilation) => {
            const stats = compilation.getStats().toJson();
            // const entry = compiler.options.entry;
            let content = '';
            stats.chunks.forEach((chunk) => {
                if (chunk.initial && chunk.entry && chunk.names.length) {
                    chunk.files.forEach((file, index) => {
                        // let oFile = this.parseFile(file, compiler);
                        // if (oFile && entry[oFile.name]) {
                        let fileName = chunk.names[index] || chunk.names[0];
                        let key = this.options.key || fileName;
                        if (typeof key !== 'string') {
                            key = key(fileName, file);
                        }
                        content += `${key}:${path.basename(file)}\n`;
                        this.extractAssets(content, compiler);
                        this.saveNoHashFile(fileName, file, compiler);
                        // }
                    });
                }
            });
        });
    }
}

module.exports = ExtractEntryWebPackPlugin;
