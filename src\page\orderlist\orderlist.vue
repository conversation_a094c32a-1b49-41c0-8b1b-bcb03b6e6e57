<template>
  <div class="dht-orderlist-page">
    <div class="desc">orderlist page</div>
    <button @click="AiImport">AI import</button>
    <button @click="showDhtAiOrder">showDhtAiOrder</button>
    <button @click="destroyDhtAiOrder">destroyDhtAiOrder</button>
  </div>
</template>

<script>
import DhtAiOrder from "@components/common/dhtAiOrder/index.vue";

export default {
  components: {
    // DhtAiOrder
  },
  data() {
    return {
      dialogVisible: false,
      ins: null
    };
  },
  methods: {
    AiImport() {
      this.dialogVisible = true;
    },
    showDhtAiOrder() {
      Fx.getBizApi('dhtbiz', 'AiOrder').then(AiOrder => { 
        this.ins = AiOrder.showDhtAiOrder();
      })
    },
    destroyDhtAiOrder() {
      console.log('destroyDhtAiOrder')
      if (this.ins) {
        this.ins.destroy();
        this.ins = null;
      }
    }
  },
  beforeDestroy() {
    this.destroyDhtAiOrder();
  }
};
</script>

<style lang="less" scoped>
.dht-orderlist-page {
  padding: 20px;
}
</style>
