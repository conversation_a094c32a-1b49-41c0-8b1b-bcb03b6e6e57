//
//
// 替换脚本

const path = require('path');
const fs = require('fs');

let appName = __dirname.split(path.sep);
appName = appName[appName.length - 2];

let paths = [
    '../package.json',
    '../postcss.config.js',
    '../project.config.js',
    '../publicpath.config.js',
    '../src/index.js',
    '../src/components/TheLink/TheLink.vue',
];

paths.forEach((file) => {
    file = path.resolve(__dirname, file);
    fs.readFile(file, 'utf8', (err, data) => {
        if (!err) {
            data = data.replace(/dhtbiz/g, appName);
            fs.writeFile(file, data, () => {});
        }
    });
});
