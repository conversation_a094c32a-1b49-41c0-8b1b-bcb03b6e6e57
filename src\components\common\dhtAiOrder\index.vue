<template>
  <fx-dialog :visible.sync="dialogVisible" hasScroll showHeader noHeaderBorderBottom 
    custom-class="dht-ai-order-dialog" width="85%" 
    @close="closeDialog">
    <div class="dialog-header-title" slot="title">
      <span class="fx-icon-AIgongju"></span>
      <span class="dialog-title-label">{{ $t('i18n.dhtbiz.dht_ai_order.title'/*AI开单*/) }}</span>
    </div>
    <div v-if="percentage < 100" class="tabs-container flex-col-fill">
      <fx-tabs v-model="activeTab">
        <fx-tab-pane :label="$t('i18n.dhtbiz.dht_ai_order.tab_excel'/*Excel识别*/)" name="excel" class="flex-col-fill">
          <div class="tab-content excel-recognition flex-col-fill">
            <p class="upload-desc">{{ $t('i18n.dhtbiz.dht_ai_order.excel_upload_desc1'/*上传包含商品信息的Excel*/) }} 
              <fx-popover
                placement="bottom-start"
                width="520"
                trigger="hover"
                popper-class="dht-excel-demo-popper"
                v-model="showExcelDemo"
                >
                <div class="excel-table-container">
                  <table class="excel-table">
                    <thead>
                      <tr>
                        <th>{{$t('i18n.dhtbiz.dht_ai_order.code'/*编码/货号*/)}} </th>
                        <th>{{$t('i18n.dhtbiz.dht_ai_order.product'/*商品*/)}} </th>
                        <th>{{$t('i18n.dhtbiz.dht_ai_order.spec'/*规格*/)}} </th>
                        <th>{{$t('i18n.dhtbiz.dht_ai_order.quantity'/*数量*/)}} </th>
                        <!-- <th>单位</th> -->
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td><div class="skeleton"></div></td>
                        <td><div class="skeleton"></div></td>
                        <td><div class="skeleton"></div></td>
                        <td><div class="skeleton"></div></td>
                      </tr>
                      <tr>
                        <td><div class="skeleton"></div></td>
                        <td><div class="skeleton"></div></td>
                        <td><div class="skeleton"></div></td>
                        <td><div class="skeleton"></div></td>
                      </tr>
                      <tr>
                        <td><div class="skeleton"></div></td>
                        <td><div class="skeleton"></div></td>
                        <td><div class="skeleton"></div></td>
                        <td><div class="skeleton"></div></td>
                      </tr>
                    </tbody>
                  </table>
                  <div class="excel-table-tip">
                    {{$t('i18n.dhtbiz.dht_ai_order.excel_table_tip'/*AI会自动识别Excel/xls文件中的商品名称、编码、数量等信息*/)}}
                  </div>
                </div>                
                <fx-link type="standard"  slot="reference">{{$t('i18n.dhtbiz.dht_ai_order.excel_demo'/*查看示例*/)}} </fx-link>
              </fx-popover>
            </p>
            <div class="main-upload-section flex-col-fill">
              <dht-ai-order-progress v-if="percentage > 0 && percentage < 100" :percentage="percentage" className="flex-col-fill" />
              <div class="upload-section flex-col-fill" v-else>
                <fx-upload 
                  :class="[{'no-file-list': noFile}, 'upload-component']"
                  drag     
                  :url="uploadUrl"         
                  :on-remove="handleFileRemove"                 
                  :before-upload="handleFileBeforeUpload"
                  :on-success="handleFileUploadSuccess"
                  :on-error="handleFileUploadError" accept=".xlsx, .xls" :limit="1">
                  <i class="fx-icon-upload"></i>
                  <div class="fx-upload__text">{{ $t('i18n.dhtbiz.dht_ai_order.upload_text'/*点击或拖动上传文件*/) }}</div>
                  <div class="fx-upload__tip" slot="tip">{{ $t('i18n.dhtbiz.dht_ai_order.file_upload_tip'/*支持文件类型：xlsx, xls，小于2M*/) }}</div>
                </fx-upload>
              </div>
            </div>
          </div>
        </fx-tab-pane>
        <fx-tab-pane :label="$t('i18n.dhtbiz.dht_ai_order.tab_image'/*图片识别*/)" name="image"  class="flex-col-fill">
          <div class="tab-content image-recognition flex-col-fill">
            <div class="image-examples">
              <div class="example-list">
                <div class="example-card example-title-card">
                  <span class="example-title">{{$t('i18n.dhtbiz.dht_ai_order.example_common'/*常用示例*/)}} </span>
                </div>
                <div class="example-card">
                  <img :src="paperImg"  />
                  <div class="example-label">{{$t('i18n.dhtbiz.dht_ai_order.paper_order'/*纸质单据*/)}} </div>
                </div>
                <div class="example-card">
                  <img :src="handImg"  />
                  <div class="example-label">{{$t('i18n.dhtbiz.dht_ai_order.handwritten_order'/*手写清单*/)}} </div>
                </div>
                <div class="example-card">
                  <img :src="chatImg" />
                  <div class="example-label">{{$t('i18n.dhtbiz.dht_ai_order.chat_screenshot'/*聊天截图*/)}} </div>
                </div>
                <div class="example-card">
                  <img :src="elecImg"  />
                  <div class="example-label">{{$t('i18n.dhtbiz.dht_ai_order.electronic_order'/*电子单据*/)}} </div>
                </div>
              </div>
            </div>
            <div class="main-upload-section" :class="{'image-upload-section': percentage <= 0}">
              <dht-ai-order-progress v-if="percentage > 0 && percentage < 100" :percentage="percentage" className="flex-col-fill" />
              <fx-upload v-else                
                list-type="picture" multiple :limit="5" 
                :url="uploadUrl"
                :accept="imgAccept"
                :before-upload="handleBeforeUpload" :on-preview="handlePictureCardPreview"
                :on-remove="handleImageRemove" :on-success="handleImageUploadSuccess"
                :on-error="handleImageUploadError" :file-list="imageList">
                <i slot="default" class="el-icon-plus"></i>
                <div slot="tip" class="fx-upload__tip">{{ $t('i18n.dhtbiz.dht_ai_order.image_upload_tip'/*支持png、jpg格式*/) }}</div>
              </fx-upload>
              <fx-dialog :visible.sync="imagePreviewVisible" append-to-body>
                <img width="100%" :src="imagePreviewUrl" alt="">
              </fx-dialog>
            </div>
          </div>
        </fx-tab-pane>
        <fx-tab-pane :label="$t('i18n.dhtbiz.dht_ai_order.tab_text'/*文字识别*/)" name="text" class="flex-col-fill">
          <div class="tab-content text-recognition flex-col-fill">
            <p class="text-desc">{{ $t('i18n.dhtbiz.dht_ai_order.text_desc'/*请在下方区域复制或输入客户下单内容，点击按钮开始识别*/) }}</p>
            <div class="main-upload-section textarea-section flex-col-fill">
              <dht-ai-order-progress v-if="percentage > 0 && percentage < 100" :percentage="percentage" className="flex-col-fill" />
              <fx-input v-else class="textarea-input flex-col-fill" type="textarea" :rows="12" :placeholder="$t('i18n.dhtbiz.dht_ai_order.text_placeholder'/*建议包含：商品名称、数量，如:牛奶 10箱*/)"
                v-model="textContent">
              </fx-input>
            </div>
          </div>
        </fx-tab-pane>
      </fx-tabs>
      <div class="button-container">
        <fx-button type="primary" @click="handleIdentify">{{ $t('i18n.dhtbiz.dht_ai_order.start_identify'/*开始识别*/) }}</fx-button>
      </div>
    </div>

    <div v-if="percentage >= 100">
      <!-- 步骤1：可编辑AI识别结果 -->
      <div v-if="activeTab === 'image' && imageStep === 1" class="ai-ocr-edit-container results-container">
        <div class="results-header">
          <span class="back-link" @click="handleBack">
            <i class="fx-icon-arrow-left"></i>
            {{ $t('i18n.dhtbiz.dht_ai_order.back'/*返回*/) }}
          </span>
          <span class="header-title">{{ $t('i18n.dhtbiz.dht_ai_order.image_result'/*图片识别结果*/) }}</span>
        </div>
        <div class="ai-ocr-table">
          <div class="ai-ocr-table-header">
            <div class="ai-ocr-th ai-ocr-th-name">{{$t('i18n.dhtbiz.dht_ai_order.product'/*商品*/)}} </div>
            <div class="ai-ocr-th ai-ocr-th-num">{{$t('i18n.dhtbiz.dht_ai_order.quantity'/*数量*/)}} </div>
          </div>
          <div class="ai-ocr-table-body">
            <div class="ai-ocr-tr" v-for="(row, idx) in aiResProducts" :key="idx">
              <div class="ai-ocr-td ai-ocr-td-name">
                <fx-input v-model="row.name" size="small" class="" />
              </div>
              <div class="ai-ocr-td ai-ocr-td-num">
                <!-- <fx-input
                  v-model="row.num"
                  size="small"
                  class=""
                  @input="onNumInput(row, idx)"
                  :maxlength="8"
                  placeholder="0"
                /> -->
                <fx-input-number :controls="false" size="small" v-model="row.num" :min="1" :max="99999999"></fx-input-number>
              </div>
            </div>
          </div>
        </div>
        <div class="ai-ocr-btn-box">
          <fx-button type="primary" @click="handleImageNextStep">{{$t('i18n.dhtbiz.dht_ai_order.next_step'/*下一步*/)}} </fx-button>
        </div>
      </div>
      <!-- 步骤2：展示匹配结果 -->
      <div v-else>
        <!-- 复用原有的匹配结果表格 -->
        <div class="results-container">
          <div class="results-header">
            <div class="header-left">
              <span class="back-link" @click="handleBack"><i class="fx-icon-arrow-left"></i>{{ $t('i18n.dhtbiz.dht_ai_order.back'/*返回*/) }}</span>
              <span class="header-title">{{ $t('i18n.dhtbiz.dht_ai_order.match_result'/*匹配结果*/) }}</span>
            </div>

            <div class="customer-input-section">
              <label class="required-label"><span class="required-label-red">*</span>{{ $t('i18n.dhtbiz.dht_ai_order.customer_name'/*客户名称*/) }}</label>
              <fx-object-input-autocomplete ref="customerInput" object-api-name="AccountObj" 
                size="micro"
                :value="customer">
              </fx-object-input-autocomplete>
            </div>
          </div>
          <fx-table :data="tableData" style="width: 100%">
            <fx-table-column prop="keyword" :label="$t('i18n.dhtbiz.dht_ai_order.keyword1'/*关键词1*/)" sortable></fx-table-column>
            <fx-table-column prop="keyword2" :label="$t('i18n.dhtbiz.dht_ai_order.keyword2'/*关键词2*/)" sortable></fx-table-column>
            <fx-table-column prop="product" :label="$t('i18n.dhtbiz.dht_ai_order.product'/*商品*/)" width="180" sortable>
              <template slot-scope="scope">
                <fx-link type="standard" size="small" v-if="scope.row._id"
                  @click.stop.prevent="handleProductDetail(scope.row)">{{ scope.row.display_name || scope.row.name || '-'
                  }}</fx-link>
                <span v-else>-</span>
              </template>
            </fx-table-column>
            <fx-table-column prop="product_spec" :label="$t('i18n.dhtbiz.dht_ai_order.spec'/*规格*/)" sortable></fx-table-column>
            <fx-table-column prop="product_code" :label="$t('i18n.dhtbiz.dht_ai_order.code'/*编号/货号*/)" sortable></fx-table-column>
            <fx-table-column prop="quantity" :label="$t('i18n.dhtbiz.dht_ai_order.quantity'/*数量*/)" sortable></fx-table-column>
            <fx-table-column prop="status" :label="$t('i18n.dhtbiz.dht_ai_order.match_status'/*匹配状态*/)" sortable>
              <template slot-scope="scope">
                <span v-if="scope.row.status === 1" class="success-status">{{ $t('i18n.dhtbiz.dht_ai_order.match_success'/*匹配成功*/) }}</span>
                <span v-else class="fail-status">{{ $t('i18n.dhtbiz.dht_ai_order.match_fail'/*匹配失败*/) }}</span>
              </template>
            </fx-table-column>
            <fx-table-column :label="$t('i18n.dhtbiz.dht_ai_order.operation'/*操作*/)" width="120">
              <template slot-scope="scope">
                <div class="action-buttons">
                  <fx-link type="standard" size="small" @click.stop.prevent="handleEditRow(scope.row)">{{ $t('i18n.dhtbiz.dht_ai_order.change'/*更换*/) }}</fx-link>
                  <fx-link type="standard" size="small" @click.stop.prevent="handleDeleteRow(scope.row)">{{ $t('i18n.dhtbiz.dht_ai_order.delete'/*删除*/) }}</fx-link>
                </div>
              </template>
            </fx-table-column>
          </fx-table>

          <div class="results-footer">
            <div class="footer-left recognize-summary">
              <span class="result-count" >{{successCount}}</span> <span> {{ $t('i18n.dhtbiz.dht_ai_order.success_count'/*条匹配成功*/, {count: ''}) }}</span>，

              <span class="result-count" >{{failCount}}</span><span>{{ $t('i18n.dhtbiz.dht_ai_order.fail_count'/*条失败*/, {count: ''}) }}</span>
            </div>
            <div class="footer-right">
              <fx-button type="primary" @click="handleGenerateOrder">{{ $t('i18n.dhtbiz.dht_ai_order.generate_order'/*生成订单*/) }}</fx-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </fx-dialog>
</template>

<script>
import FxObjectInputAutocomplete from "@components/common/FxObjectInputAutocomplete/index.vue";
import DhtAiOrderProgress from './progress.vue';

// 图片地址 统一管理
import paperImg from '@img/dht-paper-docs.png'
import handImg from '@img/dht-hand-written.png'
import chatImg from '@img/dht-chat-screenshots.png'
import elecImg from '@img/dht-electronic-docs.png'

export default {
  name: 'DhtAiOrder',
  components: {
    FxObjectInputAutocomplete,
    DhtAiOrderProgress
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    uploadUrl() {
      let appId = '';
      // 如果是下游, 需要添加 appid
      if(window.$dht) {
        appId = '?appid=FSAID_11490c84';
      }
      // return '/FSC/EM/File/UploadByStream' + appId;
      return '/FSC/EM/File/UploadByForm' + appId;
    },
    noFile() {
      return !this.excelFile?.fileName;
    },
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    imgAccept() {
      return "image/*";
    },
    orderImgDemo() {
      let domain = FS_STATIC_PATH.replace('/html', '');
      return `${domain}/assets/dht/web/ai-order-img-demo.png`;
    }
  },
  data() {
    return {
      paperImg,
      handImg,
      chatImg,
      elecImg,
      activeTab: 'excel',
      showExcelDemo: false,
      customer: {
        name: '',
      }, // 示例数据
      // partner: '抖音集团',
      tableData: [ 
        // 示例表格数据       
        // { id: 1, keyword: '运动', display_name: '沙发', name: '沙发', _id: '62ce853a928dcb000160a157', product: '防水透气运动鞋', product_spec: '42码', product_code: 'bm1234', quantity: 2, unit: '双', status: '匹配成功' },        
      ],
      currentPage: 1,
      pageSize: 10,
      excelFile: {},
      imageList: [],  // 图片列表
      imagePreviewVisible: false, // 图片预览 Dialog 是否可见
      imagePreviewUrl: '', // 预览图片的 URL
      textContent: '', // 用于文字识别的文本内容
      percentage: 0,    // 识别进度
      successCount: 0,  // 成功识别数量
      failCount: 0,    // 失败识别数量
      aiResProducts: [], // 识别出来的产品列表, 图片模式下, 允许编辑名称和数量
      searchKey: 'name', // 调用List 接口时的 搜索关键词
      useSearchKey: false, // 是否使用产品编码搜索
      productList: [], // List 接口返回的 产品列表
      imageStep: 1, // 1=编辑AI结果，2=展示匹配结果
    };
  },
  created() {
    this.init();
  },
  mounted() {
  },
  methods: {
    init() {
      // 下游 获取当前用户 id
      this.getCustomer();
    },
    getCustomer() {
      const me = this;
      if(window.$dht) { // 下游 获取当前用户 id
        CRM.util.FHHApi({
          url: `/EM6HDHT/API/v1/object/dht_account/service/get_account_simple_data`,
          data: {},
          success(res) {
            console.log('Get customer res:', res);
            if(res.Result.StatusCode === 0) {
              const data = res.Value;
              me.customer._id = data.account_id;
              me.customer.name = data.account_id__r;
              me.customer.display_name = me.customer.name;
            }
          }
        });
      }
    },
    closeDialog() {
      // console.log('closeDialog');
      this.dialogVisible = false;
      this.percentage = 0;
      // this.$emit('update:visible', false);
      this.$emit('close');
    },
    handleFileBeforeUpload(files) {
      const isLt2M = files[0].size / (1024 * 1024) < 2;
      if (!isLt2M) {
        CRM.util.alert($t('i18n.dhtbiz.dht_ai_order.upload_excel_first'/*上传Excel文件大小不能超过 2MB!*/));
        return false;
      }
      return true;
    },    
    handleFileUploadSuccess(response, file, fileList) {
      this.excelFile = {
        fileName: file.name,
        fileNpath: response.TempFileName,
        size: file.size,
      };
      /*response: {
          "TempFileName": "TN_dff358881adf405993e82b093f2f5ac8",
          "FileExtension": "xlsx"
      }
       file :{
          "status": "success",
          "name": "12-095销售部订单下发-河南新时沏-郑州12.11.xlsx",
          "name2": "12-095销售部订单下发-河南新时沏-郑州12.11",
          "size": 37221,
          "percentage": 100,
          "uid": 1746512176450,
          "raw": {
              "uid": 1746512176450
          },
          "response": {
              "TempFileName": "TN_44e1b5e2bd6a4bb7b5b67b7cdbde3b82",
              "FileExtension": "xlsx"
          }
      } */
      // console.log('Excel Upload success:', response, file, fileList);
    },
    handleFileUploadError(err, file, fileList) {
      console.error('Excel Upload error:', err);
      CRM.util.alert($t('i18n.dhtbiz.dht_ai_order.excel_upload_error'/*Excel上传失败，请稍后重试！*/));
    },
    handleFileRemove(file, fileList) {
      this.excelFile = {};
    },
    handleBeforeUpload(files) {
      const isLt10M = files[0].size / (1024 * 1024) < 10;
      if (!isLt10M) {
        CRM.util.alert($t('i18n.dhtbiz.dht_ai_order.image_size_limit'/*上传图片大小不能超过 10MB!*/));
        return false;
      }
      return true;
    },    
    handleImageUploadSuccess(response, file, fileList) {
      // console.log('Image Upload success:', response, file);      
      this.imageList = fileList;
    },
    handleImageUploadError(err, file, fileList) {
      // console.error('Image Upload error:', err);
      CRM.util.alert($t('i18n.dhtbiz.dht_ai_order.image_upload_error'/*图片上传失败！*/));
    },
    handleImageRemove(file, fileList) {
      // console.log('Image Removed:', file, fileList);
      this.imageList = fileList;
    },
    handlePictureCardPreview(file) {
      // console.log('Picture Card Preview:', file);
      this.imagePreviewUrl = file.url; // 设置预览图片的 URL
      this.imagePreviewVisible = true; // 打开预览 Dialog
    },
    showPercentage() {
      if (this.percentage > 0 && this.percentage < 100) return; // 防止重复点击
      this.percentage = 0;
      // 模拟进度
      this.progressTimer = setInterval(() => {
        if (this.percentage < 98) {
          this.percentage += 2;
        } else {
          clearInterval(this.progressTimer);
          this.progressTimer = null;
        }
      }, 100);
    },
    showLoading() {
      // 增加 #dht-ai-order-loading 样式, 隐藏 loading 动画
      CRM.util.showLoading_tip('', null, 'dht-ai-order-loading');
    },
    clearLoading() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer);
        this.progressTimer = null;
        this.percentage = 0;
      }     
      CRM.util.hideLoading_tip(null, 'dht-ai-order-loading');
    },
    // 开始识别 按钮
    handleIdentify() {
      if (this.percentage > 0 && this.percentage < 100) return; // 防止重复点击      
      const me = this;

      // 使用策略模式处理不同 tab 的识别逻辑
      const identifyStrategies = {
        excel: () => this.identify_excel(),
        image: () => this.identify_image(),
        text: () => this.identify_text()
      };

      // 直接通过 activeTab 调用对应的识别函数
      const identifyFn = identifyStrategies[this.activeTab];
      if (identifyFn) {
        identifyFn();
      }
    },
    // 调用提示词模版, 获取识别结果
    fetchAiIdentify(params) {
      console.log('Fetch ai identify:', params);
      const me = this;
      let nParams = {};
      if(Array.isArray(params)) {
        params.forEach(item => {
          nParams[item.name] = item.value;
        });
      } else {
        nParams = params;
      }

      nParams.maxTokens = 4096;
      
      return new Promise((resolve, reject) => {
        CRM.util.FHHApi({
          url: `/EM1HDHT/API/v1/object/agent/service/prompt_completions`,
          data: nParams,
          success(res) {
            console.log('Identify excel prompt_completions res:', res);
            try {
              if (res.Result.StatusCode === 0) {
                let rstMessage = res.Value?.message;
                if(rstMessage) {
                  rstMessage = rstMessage.replace(/^```json\n/, '').replace(/\n```$/, '');
                }
                
                let products = [];
                const rst = rstMessage ? JSON.parse(rstMessage) : {};
                if(rst.errMsg) {
                  reject(rst.errMsg);
                } else {
                  products = rst.productList || [];
                  if(rst.customerName) {
                    me.customer.name = rst.customerName;
                  }
                  if(rst.searchKey) {
                    me.searchKey = rst.searchKey;
                  } else {
                    me.searchKey = 'name';
                  }
                }
                me.aiResProducts = products;
                resolve(products);
              } 
            } catch (error) {
              reject(error);
              console.log('Identify excel prompt_completions res error:', error);
            }
          }
        });
      });
      //使用APL 模拟后台接口
      /* return new Promise((resolve, reject) => {
        FxUI.userDefine 
          .call_controller("CstmCtrl_excelReplaceCepInterfacePromptTempl__c", params)
          .then((res) => {
          try {
            console.log('Identify excel prompt_completions res:', res);
            let errMsg = '';
            if (res.Value.functionResult) {              
              let rstMessage = res.Value.functionResult?.message;
              if(rstMessage) {
                rstMessage = rstMessage.replace(/^```json\n/, '').replace(/\n```$/, '');
              }
              
              let products = [];
              const rst = rstMessage ? JSON.parse(rstMessage) : {};
              if(rst.errMsg) {
                reject(rst.errMsg);
              } else {
                products = rst.productList || [];
                if(rst.customerName) {
                  me.customer.name = rst.customerName;
                }
                if(rst.searchKey) {
                  me.searchKey = rst.searchKey;
                } else {
                  me.searchKey = 'name';
                }
              }
              me.aiResProducts = products;
              resolve(products);           
            } 
          } catch (error) {
            reject(error);
            console.log('Identify excel prompt_completions res error:', error);
          }          
        })
        .catch((err) => {
          reject(err);
          console.log(err);          
        });
      }); */
    },
    // 根据识别出来的产品列表, 获取产品数据
    fetchProductData(products) {
      const me = this;
      if(products.length <= 0) {
        return Promise.reject($t('i18n.dhtbiz.dht_ai_order.no_product_info'/*未能识别到产品信息*/));
      }

      const hasPrdCodeWithThreshold = function(products) {
        if (!Array.isArray(products) || products.length === 0) {
          return false;
        }

        let prdCodeCount = 0;
        let validPrdCodeCount = 0;

        products.forEach(product => {
          if (product.hasOwnProperty('prdCode')) {
            prdCodeCount++;
            if (product.prdCode) {
              validPrdCodeCount++;
            }
          }
        });
        // 检查是否存在 prdCode 字段，并且 60% 以上的字段有值
        return prdCodeCount > 0 && (validPrdCodeCount / prdCodeCount) >= 0.6;
      }

      // 使用产品编码匹配
      let useSearchKey = false;
      if(me.searchKey !== 'name' 
        && products.length > 0 
        // 检查数组中是否存在 prdCode 字段
        && hasPrdCodeWithThreshold(products)) {
        useSearchKey = true;
      }
      me.useSearchKey = useSearchKey;
      let searchKey = 'name';
      if(useSearchKey) {
        searchKey = me.searchKey;
      }
      
      const productNames = products.map(item => {
        if(useSearchKey) {
          return `"${item.prdCode}"`;
        } else {
          return `"${item.name}"`;
        }
      }).join(',');
      
      const params = {
        "serializeEmpty": false,
        "extractExtendInfo": false,
        "object_describe_api_name": "ProductObj",
        "include_describe": false,
        "include_layout": false,
        "need_tag": false,
        "search_template_type": "default",
        "ignore_scene_record_type": true,
        "search_query_info": `{\"limit\":200,\"offset\":0,\"filters\":[{\"field_name\":\"${searchKey}\",\"field_values\":[${productNames}],\"operator\":\"IN\"}],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}`,
        "pageSizeOption": [200],
      };
      console.log('fetchProductData params:', params);
      return new Promise((resolve, reject) => {
        CRM.util.FHHApi({
          url: `/EM1HNCRM/API/v1/object/ProductObj/controller/List`,
          data: params,
          success(res) {
            console.log('fetchProductData res:', res);
            if (res.Result.StatusCode === 0) {
              const products = res.Value.dataList;
              me.productList = products;
              resolve(products);
            }
          }
        });
      });
    },
    // 删除 或 手动选择数据后, 更新匹配失败和成功的数量
    updateCount(row, action) {
      const me = this;
      if(action === 'delete') {
        if(row.status === 1) {
          this.successCount--;
        } else {
          this.failCount--;
        }
        // me.aiResProducts 中删除
        me.aiResProducts = me.aiResProducts.filter(item => {
            if(this.prdCode) {
              return item.prdCode !== row.prdCode;
            } else {
              return item.name !== row.name;
            }
        });
      } else if(action === 'select') {
        // 原来是匹配失败, 现在匹配成功
        if(row.status != 1) {
          this.successCount++;
          this.failCount--;
        } 
        // 原来是匹配成功, 不做处理
      }
    },
    // 组装 更新表格数据
    updateTableData(productList) {
      // console.log('Update table data:', productList);
      const me = this;
      this.failCount = 0;
      this.successCount = 0;
      const tb = me.aiResProducts;
      let it = {};
      const tableData = [];
      tb.forEach(item => {
        if(me.useSearchKey) {
          it.keyword = item.prdCode;
          it.keyword2 = item.name;
        } else {
          it.keyword = item.name;
        }
        
        const product = productList.find(p => {
          if(me.useSearchKey) {
            return p[me.searchKey] === item.prdCode;
          } else {
            return p.name === item.name;
          }
        });

        if (product) {
          it.status = 1;  // 匹配成功
          me.successCount++;
          Object.assign(it, product);
        } else {
          it.status = 0;  // 匹配失败
          me.failCount++;
        }
        it.quantity = item.num;
        tableData.push(it);
        it = {};
      });
      me.tableData = tableData;
    },    
    identify_common(params) {
      // console.log('Identify common');
      const me = this;
      this.showPercentage();
      this.showLoading();
      this.fetchAiIdentify(params)
        .then((aiResProducts) => {
          return this.fetchProductData(aiResProducts);
        })
        .then((products) => {
          this.updateTableData(products);
          this.percentage = 100;
      }).catch((err) => {
        this.percentage = 0;
        CRM.util.alert(err);
        this.clearLoading();
      }).finally(() => {
        CRM.util.hideLoading_tip(null, 'dht-ai-order-loading');
      });
    },
    async identify_excel() {
      // console.log('Identify excel');
      const me = this;
      if(!me.excelFile.fileName) {
        CRM.util.alert('请先上传excel文件');
        return;
      }

      let params = [
        {
          name: "apiName",
          type: "String",
          value: "prompt_content_from_excel__c"
        },
        {
          name: "sceneVariables",
          type: "Map",
          value: {
            "fileName": me.excelFile.fileName,
            "fileNpath": me.excelFile.fileNpath,
            "sheet": "Sheet1" // todo not finish 表单名称, 应该后台获取, 此处不传  JDE
          }
        }
      ];
      me.identify_common(params);      
    },
    identify_image() {
      console.log('Identify image');
      const me = this;
      if(me.imageList.length <= 0) {
        CRM.util.alert($t('i18n.dhtbiz.dht_ai_order.upload_image_first'/*请先上传图片*/));
        return;
      }
      const imgNpath = me.imageList.map(item => item.response.TempFileName);
      const params = [
        {
          name: "apiName",
          type: "String",
          value: "prompt_product_info_from_image__c"
        },
        // {
        //   name: "sceneVariables",
        //   type: "Map",
        //   value: {
        //     "imgNpath": imgNpath
        //   }
        // },
        {
          name: "supportImage",
          type: "Boolean",
          value: true
        },
        {
          name: "imageStrings",
          type: "Array",
          value: imgNpath
        },
        {
          name: "imageSetting",
          type: "Map",
          value: {
            "variableType": 2,
            "objectApiName": "",
            "objectFieldName": "",
            "sceneVariableName": "imgNpath"
          }
        }
      ];
      this.showPercentage();
      this.showLoading();
      this.fetchAiIdentify(params)
        .then((aiResProducts) => {
          this.imageStep = 1;
          this.percentage = 100;
          this.clearLoading();
        })
        .catch((err) => {
          this.percentage = 0;
          CRM.util.alert(err);
          this.clearLoading();
        });
    },
    identify_text() {
      // console.log('Identify text');
      const me = this;
      if(!me.textContent) {
        CRM.util.alert($t('i18n.dhtbiz.dht_ai_order.input_text_first'/*请先输入文本*/));
        return;
      }

      let params = [
        {
          name: "apiName",
          type: "String",
          value: "prompt_extract_product_info_from_text__c"
        },
        {
          name: "sceneVariables",
          type: "Map",
          value: {
            "inputText": me.textContent
          }
        }
      ];
      me.identify_common(params);
    },
    handleExport() {
      console.log('Export results');
      // CRM.util.alert($t('i18n.dhtbiz.dht_ai_order.export_result'/*导出结果...*/));
    },
    handleGenerateOrder() {
      console.log('Generate order');
      const me = this;
      const pData = {};

      // 更新匹配失败的数量
      if(me.failCount > 0) {
        CRM.util.alert($t('i18n.dhtbiz.dht_ai_order.match_fail_tip'/*匹配失败的产品数量为: {{count}}, 请先更换正确产品或删除*/, { count: me.failCount }));
        return;
      }

      // if (me.activeTab === 'excel') {
      const customer = me.$refs.customerInput.getInputValue();
      if (!(customer && customer._id)) {
        CRM.util.alert($t('i18n.dhtbiz.dht_ai_order.select_customer_first'/*请先选择客户*/));
        return;
      }
      pData.account_id = customer._id;
      pData.account_id__r = customer.name;
      // }

      // const recordType = 'default__c';
      const detailObjRecordType = 'default__c';
      const products = this.tableData;

      pData.addMdData = {
        product: [...products],
        recordType: detailObjRecordType, // 从对象 订单产品业务类型
      };

      const options = {
        apiname: 'SalesOrderObj',
        source: 'quickorder', // 标识快速下单来源
        // _from: 'cart', // 标识需要恢复购物车的价格政策
        showDetail: true, // 订单提交成功后显示订单详情
        noPreCalculate: true, // 标识不需要前期的预计算
        isSubmitAndCreate: false, // 不显示提交继续创建按钮
        // record_type: recordType,
        data: pData,
        success: (action, data, dataId, details) => {
          console.log('add order success:', action, data, dataId, details);
        }
      };
      CRM.api.add(options);
    },
    handleProductDetail(row) {
      console.log('Product detail:', row);
      const apiname = 'ProductObj';
      const id = row._id;
      if (!id) return;
      CRM.api.show_crm_detail({
        apiName: apiname,
        id: id,
      });
    },
    handleEditRow(row) {
      const me = this;
      const data = [];
      if (row._id) {
        data.push({ _id: row._id, name: row.display_name || row.name });
      }
      CRM.api.pick_data({
        apiName: 'ProductObj',
        data,
        single: true,
        hideAdd: true,
        methods: {
          select: async function (res) {
            console.log('select row:', res);
            const npro = res.selected;
            if (npro) {              
              // 更新匹配失败的数量
              me.updateCount(row, 'select');
              // 使用 Vue.set 或 this.$set 来确保响应式更新
              const index = me.tableData.findIndex(item => item._id === row._id);
              if (index !== -1) {
                me.$set(me.tableData, index, {
                  ...row,
                  ...npro,
                  status: 1
                });
              }
            }
          }
        }
      });
    },
    handleDeleteRow(row) {
      console.log('Delete row:', row);
      this.tableData = this.tableData.filter(item => item._id !== row._id);
      this.updateCount(row, 'delete');
    },
    handleSizeChange(val) {
      this.pageSize = val;
      // console.log(`每页 ${val} 条`);
      // this.fetchData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      // console.log(`当前页: ${val}`);
      // this.fetchData();
    },
    handleBack() {
      // 如果当前是图片识别，且图片识别步骤为2，则返回步骤1
      if(this.activeTab === 'image' && this.imageStep === 2) {
        this.imageStep = 1;
      } else {
        this.percentage = 0;
        this.tableData = [];
        // 清空文件上传
        this.excelFile = {};
        this.imageList = [];
      }
    },
    handleImageNextStep() {
      // this.showLoading();
      CRM.util.showLoading_tip();
      this.fetchProductData(this.aiResProducts)
        .then((products) => {
          this.updateTableData(products);
          this.imageStep = 2;
          CRM.util.hideLoading_tip();
        })
        .catch((err) => {
          CRM.util.alert(err);
          CRM.util.hideLoading_tip();
        });
    },
    onNumInput(row, idx) {
      // 只允许输入数字，自动过滤非数字
      row.num = String(row.num).replace(/\D/g, '');
    },
  }
};
</script>
<style lang="less">
#dht-ai-order-loading {
  .crm-loading-anima {
    display: none;
  }
}
.dht-excel-demo-popper {
  .excel-table-container {
    max-width: 100%;
    margin: 0 auto;
    overflow: hidden;
  }
  .excel-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 16px;
    color: #303133;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #e4e7ed;
    th, td {
      text-align: left;
      padding: 12px 0 12px 12px;
      border-bottom: 1px solid #e4e7ed;
    }
    th {
      font-weight: 700;
      background: #fff;
      border-bottom: 2px solid #ebeef5;
    }
    tr:last-child td {
      border-bottom: none;
    }
  }
  .skeleton {
    border-radius: 99px;
    background: var(-----color-neutrals04, #EAEBEE);
    height: 16px;
    width: 80%;    
    margin: 6px 0;
  }
  .excel-table-tip {
    color: #606266;
    font-size: 15px;
    margin: 16px 0 8px 8px;
    line-height: 1.5;
  }
}
.dht-ai-order-dialog {
  background-image: url('https://ceshi112.fspage.com/assets/dht/web/dht-ai-order-bg.svg');  
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  .el-dialog__header {
    padding-bottom: 5px;
  }
  .fx-icon-AIgongju {
    margin-right: 10px;
    font-size: 14px;
  }
  .el-dialog__body {
      padding-top: 0;
      display: flex;
      flex-direction: column;
  }
  
  .el-tabs__content {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
  }

  .image-upload-section {
    .el-upload--picture {
      border-radius: var(--LH32px, 8px);
      border: 1px dashed var(--color-neutrals05);
      background: var(--color-neutrals02);

      display: flex;
      justify-content: center;
      align-items: center;
      width: 68px;
      height: 68px;

      &:hover {
        box-shadow: 0 4px 24px 0 rgba(64,158,255,0.12);
        border-color: var(--color-primary06, #FF8000);
      }

      .el-icon-plus {
        font-size: 18px;
        color: var(--color-special02);
      }
    }
  }

  .el-table {
    border-radius: var(--L_M, 16px);

    th {
      padding: 4px 0;
    }
  }

  
}
</style>

<style lang="less" scoped>
.dht-ai-order-dialog {
  .flex-col-fill {
    display: flex;
    flex-direction: column;
    flex: 1 1 0%;
  }

  .dialog-header-title {
    font-size: 16px;
  }  

  .tabs-container,
  .results-container {
    margin-top: 0px;
    min-height: 85vh;
    flex: 1 1 0%;
    display: flex;
    flex-direction: column;
  }

  .tabs-container {
    .fx-tabs {
      flex: 1 1 0%;
      display: flex;
      flex-direction: column;
    }   

    .main-upload-section {
      flex: 1 1 0%;
      display: flex;
      padding: 16px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 4px;
      flex: 1 0 0;
      align-self: stretch;
      border-radius: var(--L_M, 16px);
      border: 1px solid var(--color-neutrals05);
      background: #FFF;
      min-height: 0; // 关键：防止溢出

      .upload-section {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .textarea-section {
      flex: 1 1 0%;
      .textarea-input {
        flex: 1 1 0%;
        height: 100%;
        width: 100%;

        /deep/ .el-textarea__inner {
          flex: 1 1 0%;
          height: 100%;
          width: 100%;
          border: 0;
          padding: 0;
        }
      }
    }
  }

  .button-container {
    margin-top: 25px;
    text-align: right;
  }

  .excel-recognition {
    .upload-desc {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
      line-height: 1.5;
      margin-right: 2em;
    }

    .upload-section {
      margin-top: 20px;
      text-align: center;

      .no-file-list {
        /deep/ .fx-upload-list {
          display: none;
        }
      }

      .upload-component {
        width: 100%;

        /deep/ .el-upload-dragger {
          // 穿透修改内部样式
          width: auto; // 自适应宽度
          padding: 40px;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
          cursor: pointer;

          &:hover {
            border-color: #409eff;
          }
        }

        .fx-icon-upload {
          font-size: 50px;
          color: #c0c4cc;
          margin: 0 auto 16px;
          display: block;
        }

        .fx-upload__text {
          color: #606266;
          font-size: 14px;
        }

        .fx-upload__tip {
          color: #909399;
          font-size: 12px;
          margin-top: 7px;
        }
      }
    }
  }

  .image-recognition {
    .image-examples {
      border-radius: 16px;
      background: rgba(0, 0, 0, 0.03);
      padding: 16px;
      width: 100%;
      box-sizing: border-box;

      .example-list {
        display: flex;
        flex-direction: row;
        width: 100%;
        gap: 0; // 由flex自动分配间距
        justify-content: space-between;
        align-items: stretch;
      }

      .example-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 0px 18px 0px rgba(8, 24, 68, 0.04);
        border: 1px solid #f0f0f0;
        padding: 12px;
        min-width: 164px;
        min-height: 172px;
        flex: 1 1 0;
        margin: 0 8px;
        box-sizing: border-box;
        transition: box-shadow 0.2s, border-color 0.2s;        

        img {
          width: 100%;
          max-width: 164px;
          height: 140px;
          object-fit: contain;
          border-radius: 8px;
          background: #f6f7fa;
          box-shadow: 0 1px 4px 0 rgba(0,0,0,0.03);
        }

        .example-label {
          font-size: 14px;
          color: var(--color-neutrals15, #545861);
          text-align: center;
          margin-top: 12px;
        }
      }

      // "常用示例"卡片特殊样式
      .example-title-card {
        background: transparent;
        box-shadow: none;
        border: none;
        justify-content: center;
        min-width: 120px;
        min-height: 172px;
        margin: 0 8px;
        .example-title {
          color: #888;
          font-size: 16px;
          letter-spacing: 1px;
          text-align: center;
          line-height: 172px;
          width: 100%;
          display: block;
        }
      }

      // 让卡片等分，间距自适应
      .example-card {
        flex: 1 1 0;
        margin: 0 8px;
      }
      .example-list > .example-card:first-child { margin-left: 0; }
      .example-list > .example-card:last-child { margin-right: 0; }
    }

    .image-upload-section {
      flex: 1 1 0%;
      margin-top: 16px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: flex-start;
      /deep/ .el-upload-list--picture {
        display: flex;        
      }
      
      /deep/ .el-upload-list__item-picture {
        margin-right: 12px;
      }

      .el-icon-plus {
        font-size: 32px;
        color: #c0c4cc;
      }

      .fx-upload--picture-card {
        width: 100%;
        min-height: 160px;
        background: #fafbfc;
        border: 1px dashed #d9d9d9;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .fx-upload__tip {
        margin-top: 4px;
        color: #909399;
        font-size: 13px;
        text-align: left;
      }
    }

    .button-container {
      // 复用 excel tab 的样式
      margin-top: 25px;
      text-align: center;
    }
  }

  .text-recognition {
    .text-desc {
      font-size: 14px;
      color: #606266;
      margin-bottom: 10px;
    }

    .fx-textarea {
      margin-bottom: 20px; // 与下方按钮的间距
    }

    .button-container {
      // 复用之前的样式
      margin-top: 25px;
      text-align: center;
    }
  }

  .results-container {
    .success-status {
      color: #16B4AB;
    }
    .fail-status {
      color: #FF8000;
    }
    .action-buttons {
      display: flex;
      gap: 10px; // 按钮间距

      .fx-button--text {
        padding: 0; // 移除文字按钮的内边距
        color: #409eff;

        &:hover {
          opacity: 0.8;
        }

        i {
          font-size: 16px; // 调整图标大小
        }
      }
    }

    .pagination-container {
      margin-top: 15px;
      text-align: right;
    }
  }

  .results-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    .back-link {
      color: #409eff;
      font-size: 15px;
      cursor: pointer;
      margin-right: 16px;
      user-select: none;

      &:hover {
        text-decoration: underline;
      }

      i.fx-icon-arrow-left:before {
        color: #409eff;
      }
    }

    .header-title {
      color: var(--color-neutrals15);
      flex: 1;
    }

    .customer-input-section {
      display: flex;
      align-items: baseline;
      justify-content: flex-end;
      min-width: 350px;

      .required-label {
        .required-label-red {
          color: #f56c6c;
        }

        font-size: 14px;
        margin-right: 8px;

        &::after {
          content: '';
        }
      }

      .fx-input {
        width: 180px;
      }
    }
  }

  .results-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;

    .footer-left {}

    .footer-right {
      .fx-button--primary {
        padding: 0; // 移除文字按钮的内边距
      }
    }

    .recognize-summary {
      font-size: 14px;
      color: var(--color-neutrals11);


      .result-count {
        color: var(--color-neutrals19);
        padding-right: 3px;
        font-weight: 400;
      }
    }
  }
}

.ai-ocr-edit-container {
  display: flex;
  flex-direction: column;
  flex: 1 1 0%;
  min-height: 400px;
  background: transparent;
}

.ai-ocr-table {
  width: 100%;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(8,24,68,0.06);
  display: flex;
  flex-direction: column;
  flex: 1 1 0%;
  justify-content: flex-start;

  color: var(--Text-H1, #181C25);
  font-size: 13px;
  text-align: left;
}

.ai-ocr-table-header {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  .ai-ocr-th {
    padding: 12px 0 12px 16px;
    font-weight: 700;
  }
  .ai-ocr-th-name { width: 70%; }
  .ai-ocr-th-num { width: 30%; }
}

.ai-ocr-table-body {
  flex: 1 1 0%;
  .ai-ocr-tr {
    display: flex;
    border-bottom: 1px solid #f0f0f0;
    &:last-child { border-bottom: none; }
    .ai-ocr-td {
      padding: 2px 6px;
      display: flex;
    }
    .ai-ocr-td-name { width: 70%; }
    .ai-ocr-td-num { width: 30%; }
    // 输入框样式
    /deep/ .el-input__inner {
      border: none !important;
      box-shadow: none;
      transition: border-color 0s;
      text-align: left;
    }

    /deep/ .el-input-number {
      width: 100%;
    }
    /deep/ .el-input__inner:focus,
    /deep/ .el-input__inner:hover {
      border-radius: var(--S_WH20px, 4px);
      border: 1px solid var(--color-neutrals05, #DEE1E8) !important;
      background: #FFF;    
    }
  }
}

.ai-ocr-btn-box {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>
