import runningHooks from './running-hooks';
export default function () {
    return {
        name: 'dht_web_top_shopping_cart',
        displayName: '首页购物车飘数',
        data: {},
        related: {
            attributeSettings: [
                {
                  name: 'Setter<PERSON>ield',
                  data: {
                    setter: {
                      component: () => import('./setting.vue')
                    }
                  }
            }],
            previewDisplay: () => import(
                './display.vue'
            )
        },
        hooks: runningHooks.hooks
    };
}