
/* 将vcrm中dht的组件暴露, 参考E:\work\FS\vcrm\src\widgets\index.ts 中注册的registerWidget名称  配置说明
目前有的组件名有:
cartQuantityInput
quantityInput
orderQuickluQuantityInput
productList
sortSelect
recordType
cartRecordType
cartFooter
shopCart
goodDetail
spuDetail
skuDetail
productDetailMeta
attachPreview
splitScreen
singleSelect
selectConfirm
hotZoneEdit
dhtModules
*/
import { loadnsail } from '@/share/utils/load';


const loadWidget = (widgetName, args) => {
    return new Promise((resolve, reject) => {
        // 并行调用 loadnsail() 和 Fx.async，等待两者都完成
        Promise.all([
            new Promise((resolveSail) => {
                // 如果window.$dht存在，则直接resolve
                if(window.$dht) {
                    resolveSail();
                } else {
                    loadnsail().then(() => {
                        resolveSail();
                    })
                }
            }),
            new Promise((resolveSdk) => {
                Fx.async(['vcrm/sdk'], function(Sdk) {
                    resolveSdk(Sdk);
                });
            })
        ])
        .then(([, Sdk]) => {
            Sdk.widgetService.getWidgetApp(widgetName, { ...args })
                .then((res) => {
                    console.log(res);
                    resolve(res);
                })
                .catch(reject);
        })
        .catch(reject);
    });

    // return new Promise((resolve) => {
    //     Fx.async(['vcrm/sdk'], function(Sdk) {
    //         Sdk.widgetService.getWidgetApp(widgetName, {...args}).then((res)=>{
    //             console.log(res)
    //             resolve(res)
    //         })
    //     })
    // })
}

// 使用loadWidget方法导出所有vcrm组件
export const cartQuantityInput = args => loadWidget('cartQuantityInput', args)
export const quantityInput = args => loadWidget('quantityInput', args)
export const orderQuickluQuantityInput = args => loadWidget('orderQuickluQuantityInput', args)
export const productList = args => loadWidget('productList', args)
export const sortSelect = args => loadWidget('sortSelect', args)
export const recordType = args => loadWidget('recordType', args)
export const cartRecordType = args => loadWidget('cartRecordType', args)
export const cartFooter = args => loadWidget('cartFooter', args)
export const shopCart = args => loadWidget('shopCart', args)
export const goodDetail = args => loadWidget('goodDetail', args)
export const spuDetail = args => loadWidget('spuDetail', args)
export const skuDetail = args => loadWidget('skuDetail', args)
export const productDetailMeta = args => loadWidget('productDetailMeta', args)
export const attachPreview = args => loadWidget('attachPreview', args)
export const splitScreen = args => loadWidget('splitScreen', args)
export const singleSelect = args => loadWidget('singleSelect', args)
export const selectConfirm = args => loadWidget('selectConfirm', args)
export const hotZoneEdit = args => loadWidget('hotZoneEdit', args)
export const dhtModules = args => loadWidget('dhtModules', args)


export default {
    cartQuantityInput,
    quantityInput,
    orderQuickluQuantityInput,
    productList,
    sortSelect,
    recordType,
    cartRecordType,
    cartFooter,
    shopCart,
    goodDetail,
    spuDetail,
    skuDetail,
    productDetailMeta,
    attachPreview,
    splitScreen,
    singleSelect,
    selectConfirm,
    hotZoneEdit,
    dhtModules
};



