@import "../../assets/style/colors";

.dht-card-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

.dht-card-ellipsis2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.dht-card-item {
  width: 216px;
  min-height: 338px;
  box-sizing: border-box;
  margin: 0 0 16px 16px;
  flex: none;
  &-content {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border-radius: 4px;
    overflow: hidden;
    background-color: white;
    transition: all .2s ease-in-out;
    box-shadow:-2px 2px 16px rgba(0, 0, 0, .15);
    //&:hover {
    //  box-shadow:-2px 2px 16px rgba(0, 0, 0, .15);
    //  border-color: #F0F2F5;
    //}
  }
  &-img {
    position: relative;
    height: 214px;
    width: 100%;
    box-sizing: border-box;
    cursor: pointer;
    .img {
      height: 100%;
      width: 100%;
      object-fit: contain;
    }
    &-default {
      display: flex;
      align-items: center;
      justify-content: center;
      .img {
        height: 150px;
        width: 150px;
      }
    }
  }
  &-info {
    padding: 10px;
    text-align: left;
    font-size: 14px;

    &.dht-card-jinbei {
        .item {
            &-price {
                color: var(--color-primary06) !important;
            }
        }

        .field-item-symbol,
        .field-item-label {
            display: none !important;
        }
    }

    .item {
      &-name {
        color: #333333;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      &-display-name {
        display: flex;
      }
      &-price {
        position: relative;
        display: flex;
        align-items: center;
        margin-top: 2px;
        font-size: 18px;
        font-weight: bold;
        color: var(--color-neutrals19);
        line-height: 28px;

        .price-prefix {
          font-size: 18px;
        }

        > .price-unit {
          flex-basis: 0;
          flex-grow: 1;
          font-size: 12px;
          color: #999;
        }

        .stock-info {
          font-size: 12px;
          color: #999;
          font-weight: normal;
          position: absolute;
          right: 0;
        }
      }
    }
  }
  &-operate {
    box-sizing: border-box;
    visibility: visible;
    position: absolute;
    right: 0;
    bottom: 8px;
    z-index: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    padding: 8px 10px 0 10px;
    background-color: var(--color-neutrals01);
    opacity: 1;
    transition: all .25s ease-in-out;

    .single-spec-operate {
      display: flex;
      align-items: center;
      .product-unit {
        margin-left: 4px;
        font-size: 12px;
        color: var(--color-neutrals11);
      }
      .quantity-input {
        width: 80px;
      }

      > .card-item-input-wrap {
        display: flex;
        align-items: center;
      }

      > .add-cart-btn {
        margin-left: 5px;
        font-size: 16px;
        background: #F5F7FA;
        width: 24px;
        min-width: 24px;
        height: 24px;
        position: relative;
        line-height: 20px;
        cursor: pointer;
        transition: all .1s ease-in-out;
        border-radius: 4px;
        &:active {
          transform: scale(0.95);
        }
        &:hover {
          background-color: #ff8d1a !important;
          color: white;
        }
        &.active {
          background-color: var(--color-primary06);
          color: white;
        }
        &::before {
          content: '\e74f';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
  }
  .dht-new-product-flag {
    position: absolute;
    left: 0;
    top: 0;
    &-bg {
      position: absolute;
      left: 0;
      top: 0;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 48px 48px 0 0;
      border-color: #f1972a transparent transparent transparent;
    }
    &-text {
      position: absolute;
      left: 6px;
      top: 6px;
      color: white;
      font-size: 13px;
    }
  }
  .dht-collection-btn {
    display: inline-flex;
    visibility: visible;
    position: absolute;
    background: rgba(0,0,0, .5);
    top: 10px;
    right: 10px;
    border-radius: 2px;
    height: 24px;
    padding: 0 5px;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 12px;
    box-sizing: border-box;
    z-index: 1;
    transition: all .25s ease-in-out;
    opacity: 1;
    cursor: pointer;
    i {
      margin-right: 4px;
    }
    .el-icon-star-on {
      color: @second-color;
      font-size: 16px;
    }
  }
  &-hide {
    .dht-collection-btn,
    .dht-card-item-operate{
      visibility: hidden;
      opacity: 0;
    }
    .dht-card-item-content {
      box-shadow: none;
    }
  }
  .dht-lazy-load-error {
    font-size: 30px;
    color: #999;
  }
}

@media (min-width: 1440px) {
  .dht-card-item {
    width: 230px;
    min-height: 338px;

    &-img {
      height: 228px;
    }
  }
}

@media (min-width: 1920px) {
  .dht-card-item {
    width: 240px;
    min-height: 349px;

    &-img {
      height: 238px;
    }
  }
}

.field-list {
  line-height: 18px;
  font-size: 12px;
  color: var(--color-neutrals11);

  > .field-item {
    display: flex;
    align-items: center;

    .field-item-label {
        display: flex;
        max-width: 50%;
    }

    .field-item-value {
        display: flex;
        flex-basis: 0;
        flex-grow: 1;
    }
  }
}

.dht-left-tag {
  position: absolute;
  top: 5px;
  left: 0px;
  display: flex;
  align-items: center;
  font-size: 10px;
  color: var(--color-neutrals01);
  overflow: hidden;

  > .dht-left-tag-text{
    height: 18px;
    line-height: 18px;
    padding: 0 5px 0 10px;
    background-color: var(--color-primary06);
  }
  > .dht-left-tag-text-new {
    height: 20px;
    line-height: 20px;
    padding: 1px 4px;
    border-radius: 2px;
    text-align: center;
    color: #FFFFFF;
  }
  .dht-left-tag-text-new+.dht-left-tag-text-new {
    margin-left: 3px;
  }

  > .dht-left-tag-icon {
    height: 18px;
    width: 11px;
    transform: translateX(-4px) skewX(-20deg);
    border-top-right-radius: 3px;
    border-bottom-right-radius: 5px;
    background-color: var(--color-primary06);
  }
}

