## 商城列表容器组件
相当于商城列表的初始化,处理容器内各个子组件之间依赖公共数据,以及规范子组件之间的事件通讯

## 架构设计图

### 整体架构

```mermaid
graph TB
    %% 容器组件
    Container[Product List Container<br/>商城列表容器组件]
    
    %% 核心数据和API
    EventBus[Event Bus<br/>事件总线]
    PageData[dhtPageContext<br/>页面上下文数据]
    ContainerAPI[dhtContainerApi<br/>容器API接口]
    
    %% 子组件
    CategoryTree[Category Tree<br/>分类树组件]
    SearchBox[Search Box<br/>搜索框组件]
    FilterPanel[Filter Panel<br/>筛选面板组件]
    SortControl[Sort Control<br/>排序控制组件]
    ProductList[Product List<br/>商品列表组件]
    Pagination[Pagination<br/>分页组件]
    
    %% 数据流向
    Container --> EventBus
    Container --> PageData
    Container --> ContainerAPI
    
    %% 向子组件传递数据
    Container -.->|slot props| CategoryTree
    Container -.->|slot props| SearchBox
    Container -.->|slot props| FilterPanel
    Container -.->|slot props| SortControl
    Container -.->|slot props| ProductList
    Container -.->|slot props| Pagination
    
    %% 事件通信
    CategoryTree -->|category:change| EventBus
    SearchBox -->|search:change| EventBus
    FilterPanel -->|filter:change| EventBus
    SortControl -->|sort:change| EventBus
    ProductList -->|list:refresh| EventBus
    Pagination -->|list:refresh| EventBus
    
    %% 容器监听事件
    EventBus -->|事件监听| Container
    
    %% 数据更新通知
    Container -->|list:loading| EventBus
    Container -->|list:data:update| EventBus
    Container -->|list:error| EventBus
    Container -->|global:reset| EventBus
    
    %% 样式定义
    classDef container fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef core fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef component fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef event fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class Container container
    class EventBus,PageData,ContainerAPI core
    class CategoryTree,SearchBox,FilterPanel,SortControl,ProductList,Pagination component
```

### 分类组件数据流闭环示例

以下是以分类树组件为例，展示容器与子组件之间的完整数据流闭环：

```mermaid
graph TB
    subgraph "上行数据流-Props传递"
        %% 容器组件
        Container1[Container 容器组件]
        
        %% 核心数据和API
        SlotProps[Slot Props<br/>插槽属性传递]
        EventBus1[eventBus<br/>事件总线]
        PageData1[dhtPageContext<br/>页面上下文数据]
        ContainerAPI1[dhtContainerApi<br/>容器API]
        
        %% 子组件
        CategoryTree1[CategoryTree<br/>分类树组件]
        MallCategoryTree1[mallCategoryTree<br/>业务分类树组件]
        
        %% 数据流向
        Container1 -->|创建| EventBus1
        Container1 -->|维护| PageData1
        Container1 -->|提供| ContainerAPI1
        
        %% 向子组件传递数据
        Container1 -->|slot props| SlotProps
        SlotProps -->|传递| EventBus1
        SlotProps -->|传递| PageData1
        SlotProps -->|传递| ContainerAPI1
        
        %% 子组件接收数据
        EventBus1 -->|接收| CategoryTree1
        PageData1 -->|接收| CategoryTree1
        ContainerAPI1 -->|接收| CategoryTree1
        
        %% 子组件内部
        CategoryTree1 -->|动态加载| MallCategoryTree1
    end
    
    subgraph "下行数据流-事件传递"
        %% 用户交互
        User[用户]
        
        %% 子组件
        MallCategoryTree2[mallCategoryTree<br/>业务分类树组件]
        CategoryTree2[CategoryTree<br/>分类树组件]
        
        %% 事件钩子
        RunningHooks[running-hooks.js<br/>事件钩子]
        
        %% 事件总线
        EventBus2[eventBus<br/>事件总线]
        
        %% 容器组件
        Container2[Container 容器组件]
        PageData2[dhtPageContext<br/>页面上下文数据]
        
        %% 用户交互触发事件
        User -->|点击分类| MallCategoryTree2
        
        %% 事件传递
        MallCategoryTree2 -->|select事件| CategoryTree2
        CategoryTree2 -->|emit select| RunningHooks
        
        %% 事件钩子处理
        RunningHooks -->|获取容器实例| Container2
        RunningHooks -->|获取事件类型| Container2
        RunningHooks -->|emit CATEGORY_CHANGE| EventBus2
        
        %% 容器监听事件
        EventBus2 -->|on CATEGORY_CHANGE| Container2
        
        %% 容器处理事件
        Container2 -->|handleCategoryChange| Container2
        Container2 -->|setDhtPageContext| PageData2
        Container2 -->|resetFiltersAndPagination| Container2
        Container2 -->|refreshList| Container2
    end
    
    %% 连接两个子图
    Container2 -.->|数据更新| Container1
    
    %% 样式定义
    classDef container fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef data fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef component fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef user fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef hook fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class Container1,Container2 container
    class EventBus1,EventBus2,PageData1,PageData2,ContainerAPI1,SlotProps data
    class CategoryTree1,CategoryTree2,MallCategoryTree1,MallCategoryTree2 component
    class User user
    class RunningHooks hook
```

## 数据流和事件通信详解

### 传入的子组件的数据

容器组件通过 **slot props** 向所有子组件传递三个核心对象：

#### 1. eventBus (事件总线)
```javascript
// Vue实例，用于组件间事件通信
eventBus: new Vue()
```

#### 2. dhtPageContext (页面上下文数据)
```javascript
{
  // 当前分类信息
  category: {
    id: null,           // 分类ID
    name: '',          // 分类名称
    path: []           // 分类路径
  },
  
  // 搜索信息
  search: {
    keyword: '',       // 搜索关键词
    timestamp: 0       // 搜索时间戳
  },
  
  // 筛选条件数组
  filters: [
    // {
    //   field_name: 'name',
    //   field_values: ['test'],
    //   operator: 'LIKE'
    // }
  ],
  
  // 列表状态
  list: {
    loading: false,    // 加载状态
    total: 0,         // 总数量
    page: 1,          // 当前页码
    pageSize: 20,     // 每页大小
    error: null       // 错误信息
  },
  
  // 排序条件数组
  orders: [
    // {
    //   fieldName: 'name',
    //   isAsc: false
    // }
  ]
}
```

#### 3. dhtContainerApi (容器API接口)
```javascript
{
  $dht: window.$dht,                    // 全局DHT对象
  getEventTypes: () => DHT_PAGE_EVENT_TYPES  // 获取事件类型常量
}
```

### 子组件之间的事件通信

#### 事件类型常量
```javascript
const DHT_PAGE_EVENT_TYPES = {
  // 分类相关
  CATEGORY_CHANGE: 'category:change',
  
  // 搜索相关
  SEARCH_CHANGE: 'search:change',
  
  // 筛选相关
  FILTER_CHANGE: 'filter:change',
  
  // 排序相关
  SORT_CHANGE: 'sort:change',
  
  // 列表相关
  LIST_REFRESH: 'list:refresh',
  LIST_LOADING: 'list:loading',
  LIST_ERROR: 'list:error',
  LIST_DATA_UPDATE: 'list:data:update',
  
  // 全局重置
  GLOBAL_RESET: 'global:reset'
};
```

#### 上行事件 (子组件 → 容器)

1. **category:change** - 分类变更
   - 触发组件: CategoryTree
   - 数据格式: `{ id, name, path }`
   - 容器处理: 更新分类信息，重置筛选和分页，刷新列表

2. **search:change** - 搜索变更
   - 触发组件: SearchBox
   - 数据格式: `{ keyword }`
   - 容器处理: 更新搜索关键词，重置筛选和分页，刷新列表

3. **filter:change** - 筛选变更
   - 触发组件: FilterPanel
   - 数据格式: `筛选条件数组`
   - 容器处理: 更新筛选条件，重置分页到第一页，刷新列表

4. **sort:change** - 排序变更
   - 触发组件: SortControl
   - 数据格式: `排序条件数组`
   - 容器处理: 更新排序条件，刷新列表

5. **list:refresh** - 列表刷新请求
   - 触发组件: ProductList, Pagination
   - 数据格式: `{ page?, pageSize? }`
   - 容器处理: 更新分页信息，刷新列表

#### 下行事件 (容器 → 子组件)

1. **list:loading** - 列表加载状态
   - 数据格式: `boolean`
   - 接收组件: ProductList, Pagination

2. **list:data:update** - 列表数据更新
   - 数据格式: `{ data, total, page, pageSize }`
   - 接收组件: ProductList, Pagination

3. **list:error** - 列表加载错误
   - 数据格式: `Error对象`
   - 接收组件: ProductList

4. **global:reset** - 全局重置
   - 数据格式: `无`
   - 接收组件: 所有子组件

### 通信流程示例

#### 用户搜索商品流程
```
1. 用户在SearchBox输入关键词
2. SearchBox发送 search:change 事件
3. Container监听到事件，更新dhtPageContext.pageState.search
4. Container重置筛选条件和分页
5. Container调用refreshList()刷新数据
6. Container发送 list:loading 事件通知加载开始
7. API调用完成后，Container发送 list:data:update 事件
8. ProductList和Pagination接收到数据更新事件，重新渲染
```

#### 用户切换分类流程
```
1. 用户在CategoryTree点击分类
2. CategoryTree发送 category:change 事件
3. Container监听到事件，更新dhtPageContext.pageState.category
4. Container重置搜索、筛选条件和分页
5. Container调用refreshList()刷新数据
6. 所有相关子组件接收到数据变化，自动更新显示
```

### 组件职责分工

- **Container**: 状态管理中心、事件协调器、API调用者
- **CategoryTree**: 分类选择和导航
- **SearchBox**: 关键词搜索输入
- **FilterPanel**: 多维度筛选条件设置
- **SortControl**: 排序方式选择
- **ProductList**: 商品数据展示和交互
- **Pagination**: 分页导航控制