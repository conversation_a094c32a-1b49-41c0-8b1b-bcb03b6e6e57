/**
 * @desc 架构组提供的插件引擎的二次封装，目的是为了使其适用在详情页中
 *
 */
import { PresetPlugins } from './plugins/index';

export default class PluginService {
  constructor(options) {
    const{ appId = '', objectApiName, api = {} } = options;
    this.appId = appId;
    this.objectApiName = objectApiName;
    this.api = api;
    this.service = null; // 插件引擎
  }

  init() {
    return new Promise((resolve, reject) => {
      const opts = this._getPluginOpts();

      CRM.api.pluginService.init(opts).then(service => {
        if (service) {
          this.service = service;
          this._getPlugins().then(plugins => {
            service.use(plugins).then(resolve).catch((err) => {
              console.error(err);
              reject(err);
            });
          }).catch((err) => {
            console.error(err);
            reject(err);
          });
        } else {
          reject();
        }
      }).catch((err) => {
        console.error(err);
        reject(err);
      });
    });
  }

  _getPluginOpts() {
    return {
      appId: this.appId,
      api: this.api,
      isRecordLog: false,
      isPrintLog: true,
      traceId: CRM.util.getUUIdAsMiniProgram(),
    };
  }

  /**
   * @desc 获取所有的插件
   * 包含：内置插件、PAAS内置插件、内置业务插件、中台业务插件等
   */
  _getPlugins() {
    return new Promise((resolve) => {
      const presetPlugins = this._getPresetPlugins();
      const commonPlugins = this._getCommonPlugins();
      const plugins = presetPlugins.concat(commonPlugins);

      resolve(plugins);
    });
  }

  /**
   * @desc 获取内置插件
   */
  _getCommonPlugins() {
    // 内置支持pwc插件
    const pluginList = [{
      pluginApiName: 'PWCPlugin',
      resource:  () => {
        return window?.PAAS?.plugin?.libs?.get('PWCPlugin');
      },
      params: {}
    }];
    return pluginList;
  }

  /**
   * @desc 获取内置业务插件
   * 未使用
   */
  _getPresetPlugins() {
    const presetPlugins = PresetPlugins[this.objectApiName];
    return Array.isArray(presetPlugins) && presetPlugins.length ? presetPlugins : [];
  }

  // 运行异步插件
  run(name, params) {
    this.service.run(name, params).catch(console.error);
  }

  // 运行同步插件
  runSync(name, params) {
    this.service.runSync(name, params).catch(console.error);
  }

  destroy() {
    if (this.service) {
      this.service.destroy();
      this.service = null;
    }
    console.log('详情页插件引擎销毁');
  }
}
