const webpack = require('webpack');
const { merge } = require('webpack-merge');
const common = require('./webpack.common.js');
const config = require('../../project.config');
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const ExtractEntryWebPackPlugin = require('../plugin/extract-entry-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const path = require('path');

//
//
const capitalize = (str) => {
    return str.replace(/^./, (m) => {
        return m.toUpperCase();
    });
};

let webpackConfig = merge(common, {
    mode: 'production',

    // devtool: 'nosources-source-map',
    devtool: false,

    output: {
        path: path.resolve(__dirname, '../../release'),
        filename: '[name]-[contenthash:6].js',
        chunkFilename: '[name]/[name]-chunk-[contenthash:6].js',
    },

    module: {
        rules: [
            {
                test: /\.(css|less)$/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                        options: {
                            esModule: false,
                        },
                    },
                    'css-loader',
                    'postcss-loader',
                    'less-loader',
                    {
                        loader: 'style-resources-loader',
                        options: {
                            patterns: [path.resolve(__dirname, '../../src/assets/css/mixins/*.less')],
                        },
                    },
                ],
            },
        ],
    },

    performance: {
        hints: false,
    },

    optimization: {
        minimize: true,

        minimizer: [
            new CssMinimizerPlugin({
                parallel: true,
            }),

            new TerserPlugin({
                parallel: true,
                extractComments: false,
                terserOptions: {
                    compress: {
                        drop_console: true,
                    },
                },
            }),
        ],
    },

    plugins: [
        new ExtractEntryWebPackPlugin({
            key(entry, file) {
                let ext = path.extname(file).slice(1);
                if (entry === 'app') {
                    return `${config.name}${capitalize(ext)}Entry`;
                }
                return `${config.name}${capitalize(entry)}${capitalize(ext)}Entry`;
            },
            filename: 'tpl_config',
        }),

        new MiniCssExtractPlugin({
            filename: '[name]-[contenthash:6].css',
            chunkFilename: '[name]/[name]-[contenthash:6].css',
        }),

        new webpack.DefinePlugin({
            _WEBPACK_PRODUCTION: true,
        }),
    ],
});

module.exports = webpackConfig;
