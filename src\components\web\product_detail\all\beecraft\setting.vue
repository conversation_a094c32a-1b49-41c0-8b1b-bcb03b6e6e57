<template>
  <div class="dhtbiz dht-product-detail-all-setting" >

      <card-setting />
      <!-- 1px 通栏分割线 -->
      <div class="dht-divider-line"></div>

      <key-info-setting />
      
      <!-- 1px 通栏分割线 -->
      <div class="dht-divider-line"></div>

      <!-- 下拉框选择一个自定义插件 -->
      <div class="dht-plugin-select-wrapper"> 
        <div class="dht-name dht-title">
          {{ $t('dht.component.web.product_detail_all.custom_plugin', '客开插件') }}
        </div>
        <div class="dht-plugin-select-content"> 
          <fx-select size="small" 
            class="dht-plugin-select-select"
            :options="pluginOptions"
            v-model="pluginApiName"
            @change="handlePluginChange" 
            clearable
            filterable
          >
          </fx-select>
        </div>
      </div>
  </div>
</template>
<script>
/* 多语如下 */
import { dhtBizModel } from '../../../utils/model';

import CardSetting from '../../card/beecraft/setting.vue';
import KeyInfoSetting from '../../key_info/beecraft/setting.vue';
// import RichTextSetting from '../../rich_text/beecraft/setting.vue';


export default {
  name: 'WebProductDetailAllSetting',
  inject: ['useInternalNode'],
  components: {        
    CardSetting,
    KeyInfoSetting,
    // RichTextSetting
  },
  data() {
      return {
        objectContext: {},
        pluginOptions: [],
        pluginApiName: '',
      };
  },
  computed: {
    spuMode() {
      return dhtBizModel.isSpuMode();
    },
    mainObjApiName() {
      return dhtBizModel.isSpuMode() ? 'SPUObj' : 'ProductObj';
    },
    // pluginOptions() {
    //   return [
    //     { label: '订货通商品详情JS插件', value: 'dht.product.detail' }
    //   ];
    // }
  },
  methods: {
    init() {
      const { img, tag, price, attr, stock, name, showType, selectedFields, pluginApiName, objectContext } = this.useInternalNode(node => {
            return node.data;
      });
      this.img = img;
      this.tag = tag;
      this.price = price;
      this.attr = attr + '';
      this.stock = stock;
      this.name = name;
      this.showType = showType;
      this.selectedFields = selectedFields;
      this.pluginApiName = pluginApiName;
      this.getPluginOptions();
    },      
    getPluginOptions() {
      const me = this;
      return new Promise((resolve, reject) => {
        CRM.util.FHHApi({
          url: `/EM1HCompBuild/Plugin/queryByTypes`,
          data: {
            pageSize: 20,
            pageNumber: 1,
            type: 'dht_product_detail_plugin',
            isAll: true,
            client: 'web',
          },
          success(res) {
            if (res.Result.StatusCode === 0) {
              const plugins = res.Value.plugins.filter(
                item => {
                  // 发布, 且启用了的
                  return item.buildResult !== '未发布' && item.status == 1 // [ignore-i18n]
                }                
              );
              let rst = plugins.map(item => {
                return {
                  label: item.name,
                  value: item.apiName,
                };
              });
              me.pluginOptions = rst;              
              resolve(rst);
            } else {
              resolve({});
            }
          }
        });
      });
    },
    handlePluginChange(val) {
      console.log('pluginApiName:', val);
      this.updateProps('pluginApiName', val);
    }, 
    updateProps(key, val) {
      const { actions } = this.useInternalNode();
      actions.setCustom((data) => {
        data[key] = val;
      });
      this.$set(this, key, val);
    },  
  },
  created() {
    this.init();      
  },
  mounted () {
  },
};
</script>
<style lang="less">
.dhtbiz.dht-product-detail-all-setting {
  display: flex;
  flex-direction: column;
  width: 100%;   

  .dht-title {
      &.dht-name {
        cursor: pointer;
        color: var(--color-neutrals19, #181C25);
        font-size: 16px;
        font-weight: 700; 
        margin-bottom: 16px;
      } 
      color: var(--color-neutrals15, #545861);
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 7px;
  }  

  .dht-section {
    margin-bottom: 16px;
  }

  .dht-divider-line {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    margin-left: -12px;
    margin-right: -35px;
    border-top: 1px solid var(--color-neutrals05, #DEE1E8);
    margin-bottom: 16px;    
  }

  .dht-radio-group {
      color: var(--color-neutrals19, #181c25);
      display: flex;
      flex-direction: column;
      font-size: 14px;

      &.dht-row-radios {
        flex-direction: row;
      }

      .fx-radio {
          margin-bottom: 8px;
      }
  }
}
</style>
