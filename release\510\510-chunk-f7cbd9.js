"use strict";(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[510],{9510:(t,e,a)=>{a.r(e),a.d(e,{default:()=>s});var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"fx-object-input-autocomplete-container"},[a("fx-autocomplete",{staticClass:"fx-object-input-autocomplete",class:{"is-error":t.hasError},attrs:{size:t.size,"fetch-suggestions":t.querySearch,placeholder:t.$t("i18n.dhtbiz.common.please_input")},on:{select:t.handleSelect,blur:t.handleBlur},model:{value:t.showVal,callback:function(e){t.showVal=e},expression:"showVal"}},[a("i",{staticClass:"el-input__icon fx-icon-add-2",attrs:{slot:"suffix"},on:{click:function(e){return e.stopPropagation(),e.preventDefault(),t.handlePickDate.apply(null,arguments)}},slot:"suffix"})]),t.hasError?a("div",{staticClass:"error-tip"},[a("i",{staticClass:"fx-icon-warning"}),t._v("\n    "+t._s(t.$t("i18n.dhtbiz.common.no_match_data"))+"\n  ")]):t._e()],1)};i._withStripped=!0;const n={name:"fx-object-input-autocomplete",props:{objectApiName:{type:String,default:"AccountObj"},value:{type:Object,default:function(){return{}}},size:{type:String,default:"small"}},data:function(){return{inputValue:{},showVal:"",objDataList:[],hasError:!1}},watch:{value:{handler:function(t){this.setShowVal(t)},deep:!0}},mounted:function(){var t=this;this.querySearch(this.value.name,(function(e){var a=e.find((function(e){return e.name===t.value.name}));!t.value._id&&a?t.setShowVal(a):t.setShowVal(t.value),t.checkError()}))},methods:{setShowVal:function(t){t.value=t.display_name||t.name,this.showVal=t.value,this.inputValue=t,this.checkError()},checkError:function(){var t=this;this.showVal?-1===this.objDataList.findIndex((function(e){return e._id===t.inputValue._id&&e.value===t.showVal}))?this.showError():this.clearError():this.clearError()},showError:function(){this.hasError=!0},clearError:function(){this.hasError=!1},handleBlur:function(){this.checkError(),this.hasError&&(this.inputValue={name:this.showVal,value:this.showVal})},querySearch:function(t,e){var a=this,i={associated_object_describe_api_name:this.objectApiName,search_query_info:'{"limit":10,"offset":0,"filters":[{"field_name":"name","field_values":["'.concat(t,'"],"operator":"LIKE"}]}'),include_describe:!1,include_layout:!1,client:"web"};CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/".concat(this.objectApiName,"/controller/RelatedList"),data:i,success:function(t){if(0===t.Result.StatusCode){var i=t.Value.dataList.map((function(t){return t.value=t.display_name||t.name,t}));a.objDataList=i,e(i)}else e([])}})},handlePickDate:function(){var t=this,e=[];this.inputValue._id&&e.push({_id:this.inputValue._id,name:this.inputValue.name}),CRM.api.pick_data({apiName:this.objectApiName,data:e,single:!0,hideAdd:!0,methods:{select:function(e){var a=e.selected;a&&(a.value=a.display_name||a.name,t.objDataList=[a],t.setShowVal(a))}}})},handleSelect:function(t){this.setShowVal(t)},getInputValue:function(){return this.inputValue}}};const s=(0,a(1900).Z)(n,i,[],!1,null,"38a034f3",null).exports}}]);