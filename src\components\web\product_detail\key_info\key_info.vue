<template>
  <div class="dht-product-detail-key-info-running">    
    <div class="dht-title">{{ cName }}</div>
    <div class="dht-content">
      <div class="dht-item" :class="{'dht-double': showType === '2'}" v-for="(item, index) in cFields" :key="item.key">
        <div class="dht-field">          
          <div class="dht-text dht-name">{{ item.name }}</div>
          <div class="dht-text dht-val">
            <div v-if="item.type === 'file_attachment'">
              <template v-if="item.value && item.value.length">
                <!-- <attach-preview :attaches="item.value"></attach-preview> -->
              </template>
              <template v-else>--</template>
            </div>
            <div v-else-if="item.type === 'image'">
              <template v-if="item.value && item.value.length">
                <div v-for="(imgObj, i) in item.value" :key="i"
                     class="dht-meta-item-img-wrap"
                     @click="previewPicture(item.value, i)">
                  <fx-image
                    class="dht-meta-item-img"
                    :src="imgObj.smallUrl"
                    fit="fill">
                    <div slot="error" style="font-size: 18px;">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </fx-image>
                </div>
              </template>
              <template v-else>--</template>
            </div>
            <div v-else-if="item.type === 'url'">
              <a v-if="!!item.value" class="link-url" :href="item.value" target="_blank">
                {{ item.value }}
              </a>
              <template v-else>--</template>
            </div>
            <div v-else-if="item.type === 'long_text'">
              <p v-for="(val, idx) in item.value" :key="idx">{{ val }}</p>
            </div>
            <template v-else>{{item.value || '--'}}</template>
          </div>
        </div>
      </div>      
    </div>
  </div>
</template>

<script>
// import { dhtBizModel } from '../../utils/model';

  export default {
    name: 'dht_web_product_detail_key_info',   
    props : {  
      name: {
        type: String,
        default: '',
      },    
      showType: {
        type: String,
        default: '1',
      },
      selectedFields: {
        type: Array,
        default: () => [],
      },
      objectContext: {
        type: Object,
        default: () => {
          return {
            data: {},
            describe: {},
          }
        },
      },
    },
    computed: {
      cName() {
        return this.name || '';
      },
      cFields() {
        let sfields = this.selectedFields || [];
        let allFields = this.objectContext.describe.fields || [];
        const detailData = this.objectContext.data || {};
        let rst = sfields.map(key => {
          let item = allFields[key] || {};
          return {  
            key,
            type: item.type,
            name: item.label_r || item.label || '--',
            value: detailData[key] || '--',
          }
        });
        return rst;
      },
    },
    data() {
      return {        
      }
    }, 
    methods: {
      init() {
      },
      previewPicture(value, index) {
        console.log(value, index);
      }
    },   
    mounted() {
      this.init();
    }    
  }
</script>

<style lang="less" scoped>
// @import '~less/class.less';
.dht-product-detail-key-info-running {
  padding: 16px;

.dht-title {
  font-size: 14px;
  color: #181c25;
  margin-bottom: 16px;
}

.dht-content {  
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 100%; 
}

.dht-item {
  flex: 0 0 100%;
  padding: 8px 0;  
  box-sizing: border-box;
  text-align: left;
  max-width: 100%;
}
.dht-item.dht-double {
  flex: 0 0 50%;
  max-width: 50%;
}

.dht-field {
  width: 100%;
  display: flex;
  align-items: flex-start;
  font-size: 11px;
  padding-right: 10px;
  .dht-text {
    display: inline-block;
    width: 100%;
    overflow: hidden; 
    text-overflow: ellipsis; 
    white-space: nowrap; 
  }

  .dht-val {
    color: var(--color-neutrals19, #181c25);
    margin-bottom: 4px;
  }
  .dht-name {
    color: var(--color-neutrals11, #91959e);
  }
}

}

</style>