<div :class="prefixClass">
    <template v-if="!isDataLoading && iList.length">
      <div :class="prefixClass + '-content'">
        <component
          v-for="item in iList"
          :key="item._id"
          :is="cardComponent"
          :product="item"
          :options="options"
          :precision="precision"
          :product-config="productConfig"
          :is-configured="isConfigured"
          :product-fields="productFields"
          @on-action="onAction">
        </component>
      </div>
      <div v-if="pageOptions" :class="prefixClass + '-pagination'">
        <fx-pagination
          :page-size="pageOptions.pageSize"
          :current-page.sync="pageOptions.currentPage"
          :hide-on-single-page="true"
          :total="pageOptions.total"
          @current-change="onPageChange"
          layout="total, prev, pager, next, jumper"
          background>
        </fx-pagination>
      </div>
    </template>
    <span
      v-if="!isDataLoading && !iList.length"
      :class="prefixClass + '-empty'"
      class="dht-absolute-center">
        <img class="dht-empty-img" src="../../assets/images/nodata.png">
        {{ $t('没找到相关商品') }}
    </span>
    <loading class="dht-absolute-center" v-show="isDataLoading"></loading>
  </div>
