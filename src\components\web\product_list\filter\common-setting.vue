<template>
  <div class="filter-setting">
    <!-- 筛选设置 -->
    <div class="setting-section">
      <div class="dht-name dht-title" @click="toggleSection('filter')">
        <i :class="sectionStates.filter ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"></i>
        筛选
      </div>
      <div class="dht-content" v-show="sectionStates.filter">
        <div class="form-item">
          <label class="form-label">筛选字段</label>
          <field-selector
            v-model="filterInfo.fields"
            placeholder="请选择筛选字段"
            dialog-title="选择筛选字段"
            :selector-options="filterFieldsSelectorOptions"
            :max-display-count="5"
            @change="onFilterFieldsChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FieldSelector from '@/components/common/fieldselect/index.vue';

/**
 * 筛选设置组件
 * 功能：
 * 1. 配置筛选字段
 */
export default {
  name: 'FilterSetting',
  inject: ['useInternalNode'],

  components: {
    FieldSelector
  },

  props: {
    allFields: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      // 折叠状态控制
      sectionStates: {
        filter: true
      },

      // 筛选设置
      filterInfo: {
        fields: []
      }
    }
  },

  computed: {
    // 筛选字段选择器选项
    filterFieldsSelectorOptions() {
      const fieldData = [];
      Object.keys(this.allFields).forEach(key => {
        const field = this.allFields[key];
        if (['text', 'currency', 'select_one', 'select_many', 'number'].includes(field.type)) {
          fieldData.push({
            id: field.api_name,
            name: field.label || field.label_r
          });
        }
      });

      return {
        tabs: [{
          id: 'fields',
          title: '筛选字段',
          data: fieldData
        }],
        enableThreeColumns: true,
        enableDragSort: true
      };
    }
  },

  methods: {
    init() {
      this.initSettingData();
    },

    // 初始化设置数据
    initSettingData() {
      // 从useInternalNode获取筛选信息
      const { filter_fields } = this.useInternalNode(node => {
        return node.data;
      });

      if (filter_fields) {
        // 初始化筛选字段
        if (filter_fields && filter_fields.length > 0) {
          this.filterInfo.fields = filter_fields.map(fieldId => {
            let name = fieldId
            const field = this.allFields[fieldId];
            if(field) {
              name = field.label || field.label_r
            }
            return { id: fieldId, name,  tabId: 'fields' }
          });
        }
      }
    },

    // 切换折叠状态
    toggleSection(sectionName) {
      this.sectionStates[sectionName] = !this.sectionStates[sectionName];
    },

    // 筛选字段变更处理
    onFilterFieldsChange(selectedFields) {
      this.filterInfo.fields = selectedFields || [];
      this.updateFilterInfo();
    },

    // 更新筛选信息
    updateFilterInfo() {
      const filter_fields = this.filterInfo.fields.map(item => item.id);
      this.updateProps('filter_fields', filter_fields);
    },

    updateProps(key, val) {
      const { actions } = this.useInternalNode();
      actions.setCustom((data) => {
        data[key] = val;
      });
    }
  },

  created() {
    this.init();
  }
}
</script>

<style lang="less" scoped>
.filter-setting {
  width: 100%;

  .setting-section {
    margin-bottom: 0;
    border-bottom: 1px solid #E0E0E0;
    padding-bottom: 16px;
    margin-bottom: 16px;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }

    .dht-name {
      font-size: 16px;
      color: #181C25;
      cursor: pointer;
      user-select: none;
      display: flex;
      align-items: center;
      padding: 8px 0;

      &:hover {
        color: #409eff;
      }

      i {
        margin-right: 8px;
        font-size: 14px;
        transition: transform 0.2s ease;
      }
    }

    .dht-content {
      padding: 16px 0 0 0;

      .form-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .form-label {
          width: 100%;
          margin-right: 0;
          margin-bottom: 8px;
          font-size: 14px;
          color: #333;
          text-align: left;
          flex-shrink: 0;
        }

        .fx-input {
          flex: none;
          max-width: 100%;
        }

        .fx-select {
          flex: none;
          max-width: 100%;
        }

        .field-select-input-wrapper {
          flex: none;
          width: 100%;
        }

        .fx-checkbox {
          margin-left: 0;
        }
      }
    }
  }
}

/* 字段选择器样式调整 */
.field-select-input-wrapper {
  .field-select-input {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 8px 12px;
    min-height: 32px;
    background-color: #fff;
    cursor: pointer;
    transition: border-color 0.2s ease;

    &:hover {
      border-color: #c0c4cc;
    }

    .input-display {
      .placeholder {
        color: #c0c4cc;
        font-size: 14px;
      }

      .selected-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        align-items: center;

        .fx-tag {
          margin: 0;
        }

        .more-count {
          color: #909399;
          font-size: 12px;
          cursor: pointer;
        }

        .add-btn {
          margin-left: 4px;
          padding: 0 4px;
          height: 20px;
          line-height: 18px;
        }
      }
    }
  }
}
</style>
