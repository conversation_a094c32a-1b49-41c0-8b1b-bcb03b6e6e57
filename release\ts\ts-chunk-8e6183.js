(self.webpackChunkDhtbiz=self.webpackChunkDhtbiz||[]).push([[269],{9846:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>c});var n=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)};n._withStripped=!0;var o=r(5861),i=r(4687),a=r.n(i);const s={data:function(){return{fileList:[]}},computed:{noFile:function(){return this.fileList.length<=0},uploadUrl:function(){return"/FSC/EM/File/UploadByStream"}},mounted:function(){Fx.getBizApi("dhtbiz","AiOrder").then((function(t){t.showDhtAiOrder()}))},methods:{handleFileBeforeUpload:function(t){},handleFileRemove:function(t){this.fileList=[]},handleHttpRequest:function(t){var e=t.file,r=(t.onSuccess,t.onError,this),n=new FileReader;n.onload=function(){var t=(0,o.Z)(a().mark((function t(e){var n,o;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=e.target.result,o=new Uint8Array(n),fetch("/FSC/EM/File/UploadByStream",{method:"POST",body:o,headers:{"Content-Type":"application/octet-stream","Resource-Type":"TC",extension:"jpeg"}}).then((function(t){if(!t.ok)throw new Error(t.statusText);return t.json()})).then((function(t,e,n){r.handleFileUploadSuccess(t,e,n)}));case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),n.readAsArrayBuffer(e)},handleFileUploadSuccess:function(t,e){window.location.host},handleFileUploadError:function(t){}}};const c=(0,r(1900).Z)(s,n,[function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"dht-test"},[t._v("\n  test table\n  \n  "),r("div",{staticClass:"dht-excel-demo"},[r("div",{staticClass:"excel-table-container"},[r("table",{staticClass:"excel-table"},[r("thead",[r("tr",[r("th",[t._v("商品编码")]),r("th",[t._v("商品名称")]),r("th",[t._v("规格")]),r("th",[t._v("数量")]),r("th",[t._v("单位")])])]),r("tbody",[r("tr",[r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})])]),r("tr",[r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})])]),r("tr",[r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})]),r("td",[r("div",{staticClass:"skeleton"})])])])]),r("div",{staticClass:"excel-table-tip"},[t._v("\n        AI会自动识别Excel/xls文件中的商品名称、编码、数量、规格、单位等信息\n      ")])])])])}],!1,null,"1dcc7b1b",null).exports},7061:(t,e,r)=>{var n=r(8698).default;function o(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t.exports=o=function(){return r},t.exports.__esModule=!0,t.exports.default=t.exports;var e,r={},i=Object.prototype,a=i.hasOwnProperty,s=Object.defineProperty||function(t,e,r){t[e]=r.value},c="function"==typeof Symbol?Symbol:{},l=c.iterator||"@@iterator",u=c.asyncIterator||"@@asyncIterator",f=c.toStringTag||"@@toStringTag";function h(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{h({},"")}catch(e){h=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var o=e&&e.prototype instanceof x?e:x,i=Object.create(o.prototype),a=new A(n||[]);return s(i,"_invoke",{value:O(t,r,a)}),i}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}r.wrap=d;var v="suspendedStart",y="suspendedYield",m="executing",g="completed",w={};function x(){}function b(){}function _(){}var E={};h(E,l,(function(){return this}));var L=Object.getPrototypeOf,k=L&&L(L(G([])));k&&k!==i&&a.call(k,l)&&(E=k);var C=_.prototype=x.prototype=Object.create(E);function S(t){["next","throw","return"].forEach((function(e){h(t,e,(function(t){return this._invoke(e,t)}))}))}function F(t,e){function r(o,i,s,c){var l=p(t[o],t,i);if("throw"!==l.type){var u=l.arg,f=u.value;return f&&"object"==n(f)&&a.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(f).then((function(t){u.value=t,s(u)}),(function(t){return r("throw",t,s,c)}))}c(l.arg)}var o;s(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,o){r(t,n,e,o)}))}return o=o?o.then(i,i):i()}})}function O(t,r,n){var o=v;return function(i,a){if(o===m)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=j(s,n);if(c){if(c===w)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===v)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var l=p(t,r,n);if("normal"===l.type){if(o=n.done?g:y,l.arg===w)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=g,n.method="throw",n.arg=l.arg)}}}function j(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,j(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),w;var i=p(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,w;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,w):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,w)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function G(t){if(t||""===t){var r=t[l];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(a.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(n(t)+" is not iterable")}return b.prototype=_,s(C,"constructor",{value:_,configurable:!0}),s(_,"constructor",{value:b,configurable:!0}),b.displayName=h(_,f,"GeneratorFunction"),r.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,h(t,f,"GeneratorFunction")),t.prototype=Object.create(C),t},r.awrap=function(t){return{__await:t}},S(F.prototype),h(F.prototype,u,(function(){return this})),r.AsyncIterator=F,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new F(d(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},S(C),h(C,f,"Generator"),h(C,l,(function(){return this})),h(C,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},r.values=G,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(P),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,o){return s.type="throw",s.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=a.call(i,"catchLoc"),l=a.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,w):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),w},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),P(r),w}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;P(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:G(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),w}},r}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports},8698:t=>{function e(r){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(r)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},4687:(t,e,r)=>{var n=r(7061)();t.exports=n;try{regeneratorRuntime=n}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},5861:(t,e,r)=>{"use strict";function n(t,e,r,n,o,i,a){try{var s=t[i](a),c=s.value}catch(t){return void r(t)}s.done?e(c):Promise.resolve(c).then(n,o)}function o(t){return function(){var e=this,r=arguments;return new Promise((function(o,i){var a=t.apply(e,r);function s(t){n(a,o,i,s,c,"next",t)}function c(t){n(a,o,i,s,c,"throw",t)}s(void 0)}))}}r.d(e,{Z:()=>o})}}]);